import { describe, it, expect } from 'vitest';
import { ConstitutionalEngine } from './ConstitutionalEngine';
import { ProjectType } from '../models/enums';

describe('ConstitutionalEngine Import Test', () => {
  it('should import ConstitutionalEngine correctly', () => {
    expect(ConstitutionalEngine).toBeDefined();
    expect(typeof ConstitutionalEngine).toBe('function');
  });

  it('should create ConstitutionalEngine instance', () => {
    const engine = new ConstitutionalEngine();
    expect(engine).toBeDefined();
    expect(engine.getConstitution()).toBeNull();
  });

  it('should create blueprint with basic options', () => {
    const engine = new ConstitutionalEngine();
    const result = engine.createBlueprint({
      projectType: ProjectType.WEB_APPLICATION,
      name: 'Test Project',
      author: 'Test Author',
    });

    expect(result).toBeDefined();
    expect(result.success).toBeDefined();
    expect(result.constitution).toBeDefined();
    expect(result.validation).toBeDefined();
  });
});