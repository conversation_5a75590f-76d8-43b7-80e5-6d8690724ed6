import { describe, it, expect } from 'vitest';
import { DataEntity } from './DataEntity';
import { DataFieldType } from './enums';

describe('DataEntity', () => {
  it('should create a data entity with default values', () => {
    const entity = new DataEntity();

    expect(entity.id).toBeDefined();
    expect(entity.name).toBe('');
    expect(entity.fields).toEqual([]);
    expect(entity.relationships).toEqual([]);
    expect(entity.validationRules).toEqual([]);
    expect(entity.constraints).toEqual([]);
  });

  it('should create a data entity with provided data', () => {
    const entityData = {
      name: 'User',
      description: 'User entity for authentication',
    };

    const entity = new DataEntity(entityData);

    expect(entity.name).toBe('User');
    expect(entity.description).toBe('User entity for authentication');
  });

  it('should add a field to the entity', () => {
    const entity = new DataEntity({ name: 'User' });

    const field = entity.addField({
      name: 'email',
      type: DataFieldType.EMAIL,
      required: true,
      constraints: [],
    });

    expect(entity.fields).toHaveLength(1);
    expect(field.name).toBe('email');
    expect(field.type).toBe(DataFieldType.EMAIL);
    expect(field.required).toBe(true);
  });

  it('should update a field', () => {
    const entity = new DataEntity({ name: 'User' });
    const field = entity.addField({
      name: 'email',
      type: DataFieldType.EMAIL,
      required: true,
      constraints: [],
    });

    const updated = entity.updateField(field.id, {
      required: false,
      description: 'User email address',
    });

    expect(updated).toBe(true);
    expect(entity.fields[0].required).toBe(false);
    expect(entity.fields[0].description).toBe('User email address');
  });

  it('should remove a field', () => {
    const entity = new DataEntity({ name: 'User' });
    const field = entity.addField({
      name: 'email',
      type: DataFieldType.EMAIL,
      required: true,
      constraints: [],
    });

    const removed = entity.removeField(field.id);

    expect(removed).toBe(true);
    expect(entity.fields).toHaveLength(0);
  });

  it('should find field by name', () => {
    const entity = new DataEntity({ name: 'User' });
    entity.addField({
      name: 'email',
      type: DataFieldType.EMAIL,
      required: true,
      constraints: [],
    });

    const field = entity.getFieldByName('email');

    expect(field).toBeDefined();
    expect(field?.name).toBe('email');
  });

  it('should validate entity successfully', () => {
    const entity = new DataEntity({ name: 'User' });
    entity.addField({
      name: 'email',
      type: DataFieldType.EMAIL,
      required: true,
      constraints: [],
    });

    const validation = entity.validate();

    expect(validation.isValid).toBe(true);
    expect(validation.errors).toHaveLength(0);
  });

  it('should fail validation for empty name', () => {
    const entity = new DataEntity({ name: '' });

    const validation = entity.validate();

    expect(validation.isValid).toBe(false);
    expect(validation.errors).toHaveLength(1);
    expect(validation.errors[0].code).toBe('REQUIRED');
  });

  it('should fail validation for invalid name format', () => {
    const entity = new DataEntity({ name: '123InvalidName' });

    const validation = entity.validate();

    expect(validation.isValid).toBe(false);
    expect(validation.errors.some((e) => e.code === 'INVALID_FORMAT')).toBe(
      true
    );
  });

  it('should validate data against entity definition', () => {
    const entity = new DataEntity({ name: 'User' });
    entity.addField({
      name: 'email',
      type: DataFieldType.EMAIL,
      required: true,
      constraints: [],
    });
    entity.addField({
      name: 'age',
      type: DataFieldType.NUMBER,
      required: false,
      constraints: [],
    });

    const validData = {
      email: '<EMAIL>',
      age: 25,
    };

    const validation = entity.validateData(validData);

    expect(validation.isValid).toBe(true);
  });

  it('should fail data validation for missing required field', () => {
    const entity = new DataEntity({ name: 'User' });
    entity.addField({
      name: 'email',
      type: DataFieldType.EMAIL,
      required: true,
      constraints: [],
    });

    const invalidData = {
      age: 25,
    };

    const validation = entity.validateData(invalidData);

    expect(validation.isValid).toBe(false);
    expect(
      validation.errors.some((e) => e.code === 'REQUIRED_FIELD_MISSING')
    ).toBe(true);
  });

  it('should serialize to JSON and deserialize correctly', () => {
    const entity = new DataEntity({ name: 'User', description: 'Test entity' });
    entity.addField({
      name: 'email',
      type: DataFieldType.EMAIL,
      required: true,
      constraints: [],
    });

    const json = entity.toJSON();
    const deserialized = DataEntity.fromJSON(json);

    expect(deserialized.name).toBe(entity.name);
    expect(deserialized.description).toBe(entity.description);
    expect(deserialized.fields).toHaveLength(1);
    expect(deserialized.fields[0].name).toBe('email');
  });

  it('should clone entity correctly', () => {
    const entity = new DataEntity({ name: 'User' });
    entity.addField({
      name: 'email',
      type: DataFieldType.EMAIL,
      required: true,
      constraints: [],
    });

    const cloned = entity.clone();

    expect(cloned.id).toBe(entity.id);
    expect(cloned.name).toBe(entity.name);
    expect(cloned.fields).toHaveLength(1);
    expect(cloned.fields[0].name).toBe('email');

    // Ensure it's a deep clone
    cloned.name = 'ModifiedUser';
    expect(entity.name).toBe('User');
  });
});
