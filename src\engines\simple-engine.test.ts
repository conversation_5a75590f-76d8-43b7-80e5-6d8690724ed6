import { describe, it, expect } from 'vitest';
import { SimpleConstitutionalEngine } from './SimpleEngine';

describe('SimpleConstitutionalEngine', () => {
  it('should create and work correctly', () => {
    const engine = new SimpleConstitutionalEngine();
    expect(engine).toBeDefined();
    expect(engine.getConstitution()).toBeNull();
  });

  it('should create blueprint', () => {
    const engine = new SimpleConstitutionalEngine();
    const result = engine.createBlueprint({
      name: 'Test Project',
      author: 'Test Author',
    });

    expect(result.success).toBe(true);
    expect(result.constitution).toBeDefined();
    expect(result.constitution.name).toBe('Test Project Constitution');
  });
});