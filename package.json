{"name": "ai-code-visualizer", "displayName": "AI Code Visualizer - Visual Intent Contract", "description": "可视化意图契约白板系统 - 确保AI严格按照用户意图执行", "version": "0.0.1", "engines": {"vscode": "^1.74.0"}, "categories": ["Visualization", "Other"], "activationEvents": ["onCommand:aiCodeVisualizer.openWhiteboard", "onView:aiCodeVisualizer.explorer"], "main": "./dist/extension.js", "contributes": {"commands": [{"command": "aiCodeVisualizer.openWhiteboard", "title": "🏛️ Open Visual Intent Contract Whiteboard", "category": "AI Code Visualizer"}, {"command": "aiCodeVisualizer.createBlueprint", "title": "📋 Create New Project Blueprint", "category": "AI Code Visualizer"}, {"command": "aiCodeVisualizer.createDataEntity", "title": "📊 Create New Data Entity", "category": "AI Code Visualizer"}, {"command": "aiCodeVisualizer.showStatistics", "title": "📈 Show Project Statistics", "category": "AI Code Visualizer"}, {"command": "aiCodeVisualizer.openWebview", "title": "🖥️ Open Interactive Whiteboard", "category": "AI Code Visualizer"}, {"command": "aiCodeVisualizer.showFileWatcherStatus", "title": "🔍 Show File Watcher Status", "category": "AI Code Visualizer"}, {"command": "aiCodeVisualizer.showDetailedAnalysisReport", "title": "📊 Show Detailed Analysis Report", "category": "AI Code Visualizer"}, {"command": "aiCodeVisualizer.analyzeDependencies", "title": "🔍 Analyze Project Dependencies", "category": "AI Code Visualizer"}, {"command": "aiCodeVisualizer.showDependencyReport", "title": "📋 Show Dependency Report", "category": "AI Code Visualizer"}, {"command": "aiCodeVisualizer.visualizeDependencyGraph", "title": "🌐 Visualize Dependency Graph", "category": "AI Code Visualizer"}, {"command": "aiCodeVisualizer.classifyLegacyCode", "title": "🏗️ Classify Legacy Code", "category": "AI Code Visualizer"}, {"command": "aiCodeVisualizer.showClassificationReport", "title": "📋 Show Classification Report", "category": "AI Code Visualizer"}, {"command": "aiCodeVisualizer.showQualityHeatmap", "title": "🔥 Show Quality Heatmap", "category": "AI Code Visualizer"}, {"command": "aiCodeVisualizer.showQualityMatrix", "title": "📊 Show Quality Matrix", "category": "AI Code Visualizer"}, {"command": "aiCodeVisualizer.compareBlueprint", "title": "🔍 Compare Blueprint with Code", "category": "AI Code Visualizer"}, {"command": "aiCodeVisualizer.showComparisonReport", "title": "📋 Show Blueprint Comparison Report", "category": "AI Code Visualizer"}, {"command": "aiCodeVisualizer.showBlueprintComparison", "title": "🎯 Show Visual Blueprint Comparison", "category": "AI Code Visualizer"}, {"command": "aiCodeVisualizer.renderBlueprint", "title": "🎨 Render Blueprint Whiteboard", "category": "AI Code Visualizer"}, {"command": "aiCodeVisualizer.renderComparison", "title": "🔍 Render Blueprint Comparison", "category": "AI Code Visualizer"}], "views": {"explorer": [{"id": "aiCodeVisualizer.explorer", "name": "Visual Intent Contract", "when": "workspaceHasFolder"}]}, "menus": {"explorer/context": [{"command": "aiCodeVisualizer.openWhiteboard", "group": "aiCodeVisualizer"}]}}, "scripts": {"vscode:prepublish": "npm run package", "compile": "webpack", "watch": "webpack --watch", "package": "webpack --mode production --devtool hidden-source-map", "compile-tests": "tsc -p . --outDir out", "watch-tests": "tsc -p . -w --outDir out", "pretest": "npm run compile-tests && npm run compile && npm run lint", "lint": "eslint src --ext ts", "lint:fix": "eslint src --ext ts --fix", "format": "prettier --write \"src/**/*.ts\"", "format:check": "prettier --check \"src/**/*.ts\"", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage"}, "devDependencies": {"@types/vscode": "^1.74.0", "@types/node": "16.x", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.28.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "prettier": "^2.8.0", "typescript": "^4.9.4", "webpack": "^5.75.0", "webpack-cli": "^5.0.0", "ts-loader": "^9.4.1", "vitest": "^0.34.0", "@vitest/coverage-v8": "^0.34.0", "@types/d3": "^7.4.0"}, "dependencies": {"d3": "^7.8.5"}}