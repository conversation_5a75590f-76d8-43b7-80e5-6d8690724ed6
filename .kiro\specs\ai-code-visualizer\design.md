# Design Document - 可视化意图契约系统

## Overview

本设计文档描述了"可视化意图契约（Visual Intent Contract）"VSCode插件的技术架构。该系统实现了用户作为"立法者"、白板作为"宪法"、AI作为"执行者"的革命性协作模式，通过四阶段工作流程（立法→编译→审计→裁决）确保AI严格按照用户意图执行。

## Architecture

### 整体架构模式

采用**分层架构 + 事件驱动**的混合模式：

```
┌─────────────────────────────────────────────────────────────┐
│                    Presentation Layer                       │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   Simple Mode   │  │   Expert Mode   │  │  Audit Panel   │ │
│  │   (清单式界面)    │  │   (白板编辑器)    │  │  (差异分析)     │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                   Application Layer                         │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │ Blueprint Mgr   │  │ Intent Compiler │  │ Judgment Sys    │ │
│  │ (蓝图管理器)      │  │ (意图编译器)      │  │ (裁决系统)       │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    Domain Layer                             │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │ Constitutional  │  │ Code Analysis   │  │ Diff Analysis   │ │
│  │ Engine (宪法引擎) │  │ Engine (分析引擎) │  │ Engine (差异引擎) │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                Infrastructure Layer                         │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │ VSCode API      │  │ File System     │  │ AI Integration  │ │
│  │ (扩展接口)        │  │ (文件系统)        │  │ (AI集成)        │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 核心设计原则

1. **权力分离原则**：严格区分用户权限、系统权限和AI权限
2. **状态机驱动**：四阶段工作流程通过状态机严格控制
3. **事件溯源**：所有操作都可追溯到原始意图
4. **增量处理**：支持大型项目的性能优化
5. **容错设计**：处理现实世界的复杂情况

## Components and Interfaces

### 1. 宪法引擎 (Constitutional Engine)

**职责**：管理项目"宪法"（蓝图、规则、数据字典）

```typescript
interface ConstitutionalEngine {
  // 蓝图管理
  createBlueprint(projectType: ProjectType): Blueprint
  updateBlueprint(blueprint: Blueprint): ValidationResult
  validateBlueprint(blueprint: Blueprint): ValidationResult
  
  // 规则管理
  addBusinessRule(rule: BusinessRule): void
  validateRuleConsistency(rules: BusinessRule[]): ConflictReport
  resolveRuleConflict(conflict: RuleConflict, resolution: Resolution): void
  
  // 数据字典管理
  defineDataEntity(entity: DataEntity): void
  validateDataIntegrity(data: any, entityType: string): ValidationResult
}

interface Blueprint {
  id: string
  modules: FunctionalModule[]
  uiComponents: UIComponent[]
  dataEntities: DataEntity[]
  businessRules: BusinessRule[]
  relationships: Relationship[]
  metadata: BlueprintMetadata
}

interface BusinessRule {
  id: string
  condition: LogicCondition
  action: LogicAction
  priority: number
  conflicts?: string[]
}
```

### 2. 意图编译器 (Intent Compiler)

**职责**：将用户蓝图编译为AI的原子指令

```typescript
interface IntentCompiler {
  compile(blueprint: Blueprint): CompilationResult
  generateAtomicInstructions(module: FunctionalModule): AtomicInstruction[]
  injectConstraints(instructions: AtomicInstruction[]): ConstrainedInstruction[]
  validateInstructionChain(instructions: ConstrainedInstruction[]): ValidationResult
}

interface AtomicInstruction {
  id: string
  type: InstructionType
  target: string // 文件路径或组件名
  operation: string // 具体操作描述
  constraints: Constraint[]
  dependencies: string[] // 依赖的其他指令ID
  traceability: TraceabilityInfo // 回溯到原始蓝图
}

interface Constraint {
  type: 'SCOPE' | 'DEPENDENCY' | 'PROHIBITION' | 'VALIDATION'
  rule: string
  enforcement: 'STRICT' | 'WARNING'
}
```

### 3. 代码分析引擎 (Code Analysis Engine)

**职责**：分析现有代码和AI生成的代码

```typescript
interface CodeAnalysisEngine {
  // 静态分析
  analyzeProject(projectPath: string): ProjectAnalysis
  analyzeFile(filePath: string): FileAnalysis
  extractFunctionality(code: string, language: Language): Functionality[]
  
  // 依赖分析
  buildDependencyGraph(project: ProjectAnalysis): DependencyGraph
  findCircularDependencies(graph: DependencyGraph): CircularDependency[]
  
  // 遗留代码处理
  classifyLegacyCode(code: string): LegacyClassification
  generateReformationPlan(legacyCode: LegacyClassification): ReformationPlan
}

interface ProjectAnalysis {
  files: FileAnalysis[]
  dependencies: DependencyGraph
  metrics: ProjectMetrics
  issues: CodeIssue[]
  legacyAreas: LegacyArea[]
}

interface LegacyClassification {
  clearAreas: CodeArea[] // 绿色：结构清晰
  ambiguousAreas: CodeArea[] // 灰色：待改造
  blackBoxAreas: CodeArea[] // 第三方依赖
}
```

### 4. 差异分析引擎 (Diff Analysis Engine)

**职责**：比较蓝图与代码现实，生成可视化差异报告

```typescript
interface DiffAnalysisEngine {
  // 核心差异分析
  analyzeDifferences(blueprint: Blueprint, codeReality: ProjectAnalysis): DiffReport
  classifyDifferences(diffs: Difference[]): ClassifiedDifferences
  
  // 增量分析（性能优化）
  performIncrementalAnalysis(changes: CodeChange[]): IncrementalDiffReport
  scheduleDeepAnalysis(affectedAreas: string[]): Promise<DeepAnalysisResult>
  
  // 可追溯性
  traceToOriginalIntent(codeElement: CodeElement): TraceabilityChain
}

interface DiffReport {
  matches: Match[] // 绿色：完全匹配
  partialMatches: PartialMatch[] // 黄色：部分匹配
  violations: Violation[] // 红色：违背/缺失
  hallucinations: Hallucination[] // 紫色：AI幻觉
  pendingVerification: PendingItem[] // 蓝色：待深度验证
}

interface Violation {
  type: 'MISSING' | 'INCORRECT' | 'RULE_VIOLATION'
  element: CodeElement
  expectedBehavior: string
  actualBehavior: string
  severity: 'HIGH' | 'MEDIUM' | 'LOW'
  fixSuggestion: string
}
```

### 5. 可视化白板系统 (Visualization Whiteboard System)

**职责**：提供无边界白板和可视化交互

```typescript
interface WhiteboardSystem {
  // 白板管理
  createWhiteboard(mode: 'SIMPLE' | 'EXPERT'): Whiteboard
  renderBlueprint(blueprint: Blueprint): void
  renderDiffAnalysis(diffReport: DiffReport): void
  
  // 交互处理
  handleNodeClick(nodeId: string): void
  handleNodeDrag(nodeId: string, position: Position): void
  handleZoomAndPan(viewport: Viewport): void
  
  // 布局算法
  autoLayout(nodes: Node[], relationships: Relationship[]): Layout
  preserveUserLayout(customLayout: Layout): void
}

interface Whiteboard {
  canvas: Canvas
  nodes: Map<string, Node>
  edges: Map<string, Edge>
  viewport: Viewport
  interactionMode: InteractionMode
}

interface Node {
  id: string
  type: NodeType
  position: Position
  size: Size
  style: NodeStyle
  data: any
  status: NodeStatus // GREEN | YELLOW | RED | PURPLE | BLUE
}
```

### 6. 裁决系统 (Judgment System)

**职责**：处理用户的可视化裁决操作

```typescript
interface JudgmentSystem {
  // 裁决操作
  executeJudgment(judgment: Judgment): JudgmentResult
  purgeHallucinations(hallucinationIds: string[]): PurgeResult
  enforceCompliance(violationId: string): ComplianceResult
  implementMissingFeature(featureId: string): ImplementationResult
  
  // 指令生成
  generateRefactoringInstructions(judgment: Judgment): RefactoringInstruction[]
  validateInstructionSafety(instructions: RefactoringInstruction[]): SafetyReport
}

interface Judgment {
  type: 'PURGE' | 'ENFORCE' | 'IMPLEMENT' | 'MODIFY'
  targetId: string
  reason: string
  expectedOutcome: string
  userConfirmation: boolean
}
```

## Data Models

### 核心数据模型

```typescript
// 项目宪法
interface ProjectConstitution {
  id: string
  version: string
  blueprint: Blueprint
  businessRules: BusinessRule[]
  dataEntities: DataEntity[]
  metadata: ConstitutionMetadata
  history: ConstitutionChange[]
}

// 功能模块
interface FunctionalModule {
  id: string
  name: string
  description: string
  type: ModuleType
  subModules: FunctionalModule[]
  uiComponents: UIComponent[]
  dataRequirements: DataRequirement[]
  businessRules: string[] // 引用BusinessRule的ID
  status: ModuleStatus
}

// UI组件定义
interface UIComponent {
  id: string
  name: string
  type: ComponentType
  properties: ComponentProperty[]
  events: ComponentEvent[]
  parentModule: string
  position?: Position
}

// 数据实体
interface DataEntity {
  id: string
  name: string
  fields: DataField[]
  relationships: EntityRelationship[]
  validationRules: ValidationRule[]
  constraints: DataConstraint[]
}

// 代码现实映射
interface CodeReality {
  projectStructure: ProjectStructure
  implementations: Implementation[]
  dependencies: DependencyGraph
  issues: CodeIssue[]
  metrics: QualityMetrics
}
```

## Error Handling

### 错误分类和处理策略

```typescript
enum ErrorType {
  // 宪法错误
  CONSTITUTIONAL_VIOLATION = 'CONSTITUTIONAL_VIOLATION',
  RULE_CONFLICT = 'RULE_CONFLICT',
  BLUEPRINT_INCONSISTENCY = 'BLUEPRINT_INCONSISTENCY',
  
  // 编译错误
  COMPILATION_FAILURE = 'COMPILATION_FAILURE',
  INSTRUCTION_INVALID = 'INSTRUCTION_INVALID',
  CONSTRAINT_VIOLATION = 'CONSTRAINT_VIOLATION',
  
  // 分析错误
  ANALYSIS_TIMEOUT = 'ANALYSIS_TIMEOUT',
  PARSING_ERROR = 'PARSING_ERROR',
  DEPENDENCY_CYCLE = 'DEPENDENCY_CYCLE',
  
  // 系统错误
  RESOURCE_EXHAUSTED = 'RESOURCE_EXHAUSTED',
  PERMISSION_DENIED = 'PERMISSION_DENIED',
  EXTERNAL_SERVICE_FAILURE = 'EXTERNAL_SERVICE_FAILURE'
}

interface ErrorHandler {
  handleError(error: SystemError): ErrorResponse
  recoverFromError(error: SystemError): RecoveryResult
  logError(error: SystemError): void
  notifyUser(error: SystemError): void
}
```

### 容错机制

1. **宪法保护**：任何可能破坏项目宪法的操作都会被阻止
2. **优雅降级**：分析失败时提供部分结果而不是完全失败
3. **事务回滚**：支持撤销有问题的操作
4. **资源限制**：防止大型项目导致的资源耗尽

## Testing Strategy

### 测试层次

1. **单元测试**
   - 各个引擎的核心逻辑
   - 数据模型的验证
   - 工具函数的正确性

2. **集成测试**
   - 四阶段工作流程的完整性
   - 组件间的数据流
   - VSCode API的集成

3. **端到端测试**
   - 完整的用户场景
   - 不同项目类型的支持
   - 性能基准测试

4. **用户体验测试**
   - 非技术用户的可用性
   - 认知负荷评估
   - 错误恢复流程

### 测试数据

```typescript
interface TestScenario {
  name: string
  description: string
  inputBlueprint: Blueprint
  expectedCode: ProjectStructure
  expectedDifferences: DiffReport
  performanceThresholds: PerformanceThreshold[]
}

// 测试用例包括：
// 1. 简单博客系统
// 2. 电商平台
// 3. 遗留代码改造
// 4. 大型企业应用
// 5. 多语言混合项目
```

## Performance Considerations

### 性能优化策略

1. **增量分析**：只分析变更的部分
2. **懒加载**：按需加载大型项目的部分
3. **缓存机制**：缓存分析结果和编译结果
4. **并行处理**：利用多线程进行代码分析
5. **资源池**：复用昂贵的分析器实例

### 性能指标

- 蓝图编译时间：< 5秒（中型项目）
- 增量分析时间：< 1秒
- 全局深度分析：< 30秒（大型项目）
- 内存使用：< 500MB（正常项目）
- UI响应时间：< 100ms

## Security Considerations

### 安全措施

1. **代码隔离**：AI生成的代码在沙箱中执行
2. **权限控制**：严格限制文件系统访问
3. **输入验证**：验证所有用户输入和AI输出
4. **审计日志**：记录所有关键操作
5. **敏感信息保护**：避免泄露项目敏感信息

### 威胁模型

- **恶意AI输出**：通过约束和验证防范
- **代码注入**：通过静态分析检测
- **资源耗尽攻击**：通过资源限制防范
- **数据泄露**：通过权限控制和加密防范