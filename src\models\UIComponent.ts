import {
  BaseEntity,
  Position,
  ComponentProperty,
  ComponentEvent,
} from './interfaces';
import { ComponentType } from './enums';
import { ValidationResult, ValidationResultImpl } from './ValidationResult';

/**
 * UI组件类
 * 定义用户界面元素，作为"宪法"中UI规范的组成部分
 */
export class UIComponent implements BaseEntity {
  public id: string;
  public name: string;
  public description?: string;
  public createdAt: Date;
  public updatedAt: Date;
  public version: string;
  public type: ComponentType;
  public properties: ComponentProperty[];
  public events: ComponentEvent[];
  public parentModuleId: string;
  public position?: Position;
  public children: string[]; // 子组件ID列表
  public isVisible: boolean;
  public isEnabled: boolean;

  constructor(data: Partial<UIComponent> = {}) {
    this.id = data.id || this.generateId();
    this.name = data.name || '';
    this.description = data.description;
    this.createdAt = data.createdAt || new Date();
    this.updatedAt = data.updatedAt || new Date();
    this.version = data.version || '1.0.0';
    this.type = data.type || ComponentType.CUSTOM;
    this.properties = data.properties || [];
    this.events = data.events || [];
    this.parentModuleId = data.parentModuleId || '';
    this.position = data.position;
    this.children = data.children || [];
    this.isVisible = data.isVisible !== undefined ? data.isVisible : true;
    this.isEnabled = data.isEnabled !== undefined ? data.isEnabled : true;
  }

  /**
   * 添加属性
   */
  addProperty(
    property: Omit<ComponentProperty, 'name'> & { name: string }
  ): ComponentProperty {
    // 检查属性是否已存在
    const existingIndex = this.properties.findIndex(
      (p) => p.name === property.name
    );
    if (existingIndex !== -1) {
      // 更新现有属性
      this.properties[existingIndex] = property;
    } else {
      // 添加新属性
      this.properties.push(property);
    }
    this.touch();
    return property;
  }

  /**
   * 更新属性
   */
  updateProperty(name: string, updates: Partial<ComponentProperty>): boolean {
    const propertyIndex = this.properties.findIndex((p) => p.name === name);
    if (propertyIndex === -1) {
      return false;
    }

    this.properties[propertyIndex] = {
      ...this.properties[propertyIndex],
      ...updates,
    };
    this.touch();
    return true;
  }

  /**
   * 删除属性
   */
  removeProperty(name: string): boolean {
    const initialLength = this.properties.length;
    this.properties = this.properties.filter((p) => p.name !== name);
    if (this.properties.length < initialLength) {
      this.touch();
      return true;
    }
    return false;
  }

  /**
   * 获取属性值
   */
  getPropertyValue(name: string): any {
    const property = this.properties.find((p) => p.name === name);
    return property?.value;
  }

  /**
   * 设置属性值
   */
  setPropertyValue(name: string, value: any): boolean {
    const property = this.properties.find((p) => p.name === name);
    if (property) {
      property.value = value;
      this.touch();
      return true;
    }
    return false;
  }

  /**
   * 添加事件
   */
  addEvent(event: ComponentEvent): void {
    // 检查事件是否已存在
    const existingIndex = this.events.findIndex((e) => e.name === event.name);
    if (existingIndex !== -1) {
      // 更新现有事件
      this.events[existingIndex] = event;
    } else {
      // 添加新事件
      this.events.push(event);
    }
    this.touch();
  }

  /**
   * 删除事件
   */
  removeEvent(name: string): boolean {
    const initialLength = this.events.length;
    this.events = this.events.filter((e) => e.name !== name);
    if (this.events.length < initialLength) {
      this.touch();
      return true;
    }
    return false;
  }

  /**
   * 添加子组件
   */
  addChild(childId: string): void {
    if (!this.children.includes(childId)) {
      this.children.push(childId);
      this.touch();
    }
  }

  /**
   * 删除子组件
   */
  removeChild(childId: string): boolean {
    const initialLength = this.children.length;
    this.children = this.children.filter((id) => id !== childId);
    if (this.children.length < initialLength) {
      this.touch();
      return true;
    }
    return false;
  }

  /**
   * 设置位置
   */
  setPosition(position: Position): void {
    this.position = position;
    this.touch();
  }

  /**
   * 验证UI组件的完整性
   */
  validate(): ValidationResult {
    const result = new ValidationResultImpl();

    // 验证基本信息
    if (!this.name || this.name.trim() === '') {
      result.addError('Component name is required', 'name', 'REQUIRED');
    }

    if (!/^[a-zA-Z][a-zA-Z0-9_]*$/.test(this.name)) {
      result.addError(
        'Component name must start with a letter and contain only letters, numbers, and underscores',
        'name',
        'INVALID_FORMAT'
      );
    }

    if (!this.parentModuleId || this.parentModuleId.trim() === '') {
      result.addError(
        'Component must belong to a module',
        'parentModuleId',
        'REQUIRED'
      );
    }

    // 验证组件类型特定的属性
    const typeValidation = this.validateComponentType();
    result.merge(typeValidation);

    // 验证属性
    for (const property of this.properties) {
      const propertyValidation = this.validateProperty(property);
      result.merge(propertyValidation);
    }

    // 验证事件
    for (const event of this.events) {
      const eventValidation = this.validateEvent(event);
      result.merge(eventValidation);
    }

    return result;
  }

  /**
   * 验证组件类型特定的要求
   */
  private validateComponentType(): ValidationResult {
    const result = new ValidationResultImpl();

    switch (this.type) {
      case ComponentType.BUTTON:
        if (!this.getPropertyValue('text') && !this.getPropertyValue('icon')) {
          result.addWarning(
            'Button should have either text or icon',
            'properties',
            'MISSING_CONTENT'
          );
        }
        break;

      case ComponentType.INPUT:
        if (!this.getPropertyValue('type')) {
          result.addWarning(
            'Input should specify input type',
            'properties',
            'MISSING_TYPE'
          );
        }
        break;

      case ComponentType.SELECT:
        if (!this.getPropertyValue('options')) {
          result.addError(
            'Select component must have options',
            'properties',
            'MISSING_OPTIONS'
          );
        }
        break;

      case ComponentType.TABLE:
        if (!this.getPropertyValue('columns')) {
          result.addError(
            'Table component must have columns defined',
            'properties',
            'MISSING_COLUMNS'
          );
        }
        break;

      case ComponentType.FORM:
        if (this.children.length === 0) {
          result.addWarning(
            'Form should contain form elements',
            'children',
            'EMPTY_FORM'
          );
        }
        break;
    }

    return result;
  }

  /**
   * 验证属性
   */
  private validateProperty(property: ComponentProperty): ValidationResult {
    const result = new ValidationResultImpl();

    if (!property.name || property.name.trim() === '') {
      result.addError(
        'Property name is required',
        `properties.${property.name}`,
        'REQUIRED'
      );
    }

    if (!/^[a-zA-Z][a-zA-Z0-9_]*$/.test(property.name)) {
      result.addError(
        'Property name must start with a letter and contain only letters, numbers, and underscores',
        `properties.${property.name}`,
        'INVALID_FORMAT'
      );
    }

    if (
      property.required &&
      (property.value === undefined || property.value === null)
    ) {
      result.addError(
        `Required property ${property.name} has no value`,
        `properties.${property.name}`,
        'REQUIRED_VALUE_MISSING'
      );
    }

    // 验证属性值类型
    if (property.value !== undefined && property.type) {
      const typeValidation = this.validatePropertyType(property);
      result.merge(typeValidation);
    }

    return result;
  }

  /**
   * 验证属性类型
   */
  private validatePropertyType(property: ComponentProperty): ValidationResult {
    const result = new ValidationResultImpl();

    const expectedType = property.type.toLowerCase();
    const actualType = typeof property.value;

    switch (expectedType) {
      case 'string':
        if (actualType !== 'string') {
          result.addError(
            `Property ${property.name} should be a string, got ${actualType}`,
            `properties.${property.name}`,
            'TYPE_MISMATCH'
          );
        }
        break;
      case 'number':
        if (actualType !== 'number' || isNaN(property.value)) {
          result.addError(
            `Property ${property.name} should be a number, got ${actualType}`,
            `properties.${property.name}`,
            'TYPE_MISMATCH'
          );
        }
        break;
      case 'boolean':
        if (actualType !== 'boolean') {
          result.addError(
            `Property ${property.name} should be a boolean, got ${actualType}`,
            `properties.${property.name}`,
            'TYPE_MISMATCH'
          );
        }
        break;
      case 'array':
        if (!Array.isArray(property.value)) {
          result.addError(
            `Property ${property.name} should be an array, got ${actualType}`,
            `properties.${property.name}`,
            'TYPE_MISMATCH'
          );
        }
        break;
      case 'object':
        if (actualType !== 'object' || Array.isArray(property.value)) {
          result.addError(
            `Property ${property.name} should be an object, got ${actualType}`,
            `properties.${property.name}`,
            'TYPE_MISMATCH'
          );
        }
        break;
    }

    return result;
  }

  /**
   * 验证事件
   */
  private validateEvent(event: ComponentEvent): ValidationResult {
    const result = new ValidationResultImpl();

    if (!event.name || event.name.trim() === '') {
      result.addError(
        'Event name is required',
        `events.${event.name}`,
        'REQUIRED'
      );
    }

    if (!/^[a-zA-Z][a-zA-Z0-9_]*$/.test(event.name)) {
      result.addError(
        'Event name must start with a letter and contain only letters, numbers, and underscores',
        `events.${event.name}`,
        'INVALID_FORMAT'
      );
    }

    if (!event.type || event.type.trim() === '') {
      result.addError(
        'Event type is required',
        `events.${event.name}.type`,
        'REQUIRED'
      );
    }

    return result;
  }

  /**
   * 获取组件的默认属性（基于组件类型）
   */
  static getDefaultProperties(type: ComponentType): ComponentProperty[] {
    switch (type) {
      case ComponentType.BUTTON:
        return [
          { name: 'text', type: 'string', value: 'Button', required: false },
          { name: 'disabled', type: 'boolean', value: false, required: false },
          {
            name: 'variant',
            type: 'string',
            value: 'primary',
            required: false,
          },
        ];

      case ComponentType.INPUT:
        return [
          { name: 'type', type: 'string', value: 'text', required: true },
          { name: 'placeholder', type: 'string', value: '', required: false },
          { name: 'required', type: 'boolean', value: false, required: false },
          { name: 'disabled', type: 'boolean', value: false, required: false },
        ];

      case ComponentType.SELECT:
        return [
          { name: 'options', type: 'array', value: [], required: true },
          { name: 'multiple', type: 'boolean', value: false, required: false },
          { name: 'disabled', type: 'boolean', value: false, required: false },
        ];

      case ComponentType.TABLE:
        return [
          { name: 'columns', type: 'array', value: [], required: true },
          { name: 'data', type: 'array', value: [], required: false },
          { name: 'sortable', type: 'boolean', value: true, required: false },
          {
            name: 'filterable',
            type: 'boolean',
            value: false,
            required: false,
          },
        ];

      default:
        return [
          { name: 'className', type: 'string', value: '', required: false },
          { name: 'style', type: 'object', value: {}, required: false },
        ];
    }
  }

  /**
   * 获取组件的默认事件（基于组件类型）
   */
  static getDefaultEvents(type: ComponentType): ComponentEvent[] {
    switch (type) {
      case ComponentType.BUTTON:
        return [
          { name: 'onClick', type: 'click' },
          { name: 'onMouseEnter', type: 'mouseenter' },
          { name: 'onMouseLeave', type: 'mouseleave' },
        ];

      case ComponentType.INPUT:
        return [
          { name: 'onChange', type: 'change' },
          { name: 'onFocus', type: 'focus' },
          { name: 'onBlur', type: 'blur' },
        ];

      case ComponentType.SELECT:
        return [
          { name: 'onChange', type: 'change' },
          { name: 'onFocus', type: 'focus' },
        ];

      default:
        return [{ name: 'onClick', type: 'click' }];
    }
  }

  /**
   * 序列化为JSON
   */
  toJSON(): Record<string, any> {
    return {
      id: this.id,
      name: this.name,
      description: this.description,
      createdAt: this.createdAt.toISOString(),
      updatedAt: this.updatedAt.toISOString(),
      version: this.version,
      type: this.type,
      properties: this.properties,
      events: this.events,
      parentModuleId: this.parentModuleId,
      position: this.position,
      children: this.children,
      isVisible: this.isVisible,
      isEnabled: this.isEnabled,
    };
  }

  /**
   * 从JSON反序列化
   */
  static fromJSON(json: Record<string, any>): UIComponent {
    return new UIComponent({
      ...json,
      createdAt: new Date(json.createdAt),
      updatedAt: new Date(json.updatedAt),
    });
  }

  /**
   * 克隆组件
   */
  clone(): UIComponent {
    return UIComponent.fromJSON(this.toJSON());
  }

  /**
   * 更新时间戳
   */
  private touch(): void {
    this.updatedAt = new Date();
  }

  /**
   * 生成唯一ID
   */
  private generateId(): string {
    return `component_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
