import * as vscode from 'vscode';
import * as ts from 'typescript';
import { 
  BaseParser, 
  ParseResult, 
  ASTNode, 
  SymbolInfo, 
  DependencyInfo, 
  ExportInfo, 
  ParseError,
  ParseWarning,
  ParameterInfo
} from './BaseParser';

/**
 * TypeScript/JavaScript解析器
 */
export class TypeScriptParser extends BaseParser {
  private sourceFile: ts.SourceFile | null = null;
  private typeChecker: ts.TypeChecker | null = null;

  /**
   * 解析文档
   */
  public async parse(document: vscode.TextDocument): Promise<ParseResult> {
    const content = document.getText();
    const fileName = document.fileName;

    try {
      // 创建TypeScript源文件
      this.sourceFile = ts.createSourceFile(
        fileName,
        content,
        ts.ScriptTarget.Latest,
        true,
        this.getScriptKind(document.languageId)
      );

      // 创建程序和类型检查器
      const program = ts.createProgram([fileName], {
        allowJs: true,
        allowSyntheticDefaultImports: true,
        esModuleInterop: true,
        target: ts.ScriptTarget.Latest,
        module: ts.ModuleKind.CommonJS
      }, {
        getSourceFile: (name) => name === fileName ? this.sourceFile! : undefined,
        writeFile: () => {},
        getCurrentDirectory: () => '',
        getDirectories: () => [],
        fileExists: () => true,
        readFile: () => '',
        getCanonicalFileName: (name) => name,
        useCaseSensitiveFileNames: () => true,
        getNewLine: () => '\n',
        getDefaultLibFileName: () => 'lib.d.ts'
      });

      this.typeChecker = program.getTypeChecker();

      // 解析AST
      const ast = this.buildAST(this.sourceFile);
      
      // 提取符号信息
      const symbols = this.extractSymbols(this.sourceFile);
      
      // 提取依赖信息
      const dependencies = this.extractDependencies(this.sourceFile);
      
      // 提取导出信息
      const exports = this.extractExports(this.sourceFile);
      
      // 计算代码度量
      const metrics = this.config.calculateMetrics ? this.createBaseMetrics(content) : {
        linesOfCode: 0,
        cyclomaticComplexity: 0,
        cognitiveComplexity: 0,
        maintainabilityIndex: 0,
        technicalDebt: 0,
        duplicatedLines: 0
      };

      return {
        success: true,
        ast,
        symbols,
        dependencies,
        exports,
        errors: [],
        warnings: [],
        metrics
      };

    } catch (error) {
      const parseError = this.createError(
        `Parse error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        0,
        0,
        'PARSE_ERROR'
      );

      return {
        success: false,
        symbols: [],
        dependencies: [],
        exports: [],
        errors: [parseError],
        warnings: [],
        metrics: this.createBaseMetrics(content)
      };
    }
  }

  /**
   * 检查是否支持该语言
   */
  public supports(languageId: string): boolean {
    return ['typescript', 'javascript', 'typescriptreact', 'javascriptreact'].includes(languageId);
  }

  /**
   * 获取支持的语言列表
   */
  public getSupportedLanguages(): string[] {
    return ['typescript', 'javascript', 'typescriptreact', 'javascriptreact'];
  }

  /**
   * 获取脚本类型
   */
  private getScriptKind(languageId: string): ts.ScriptKind {
    switch (languageId) {
      case 'typescript':
        return ts.ScriptKind.TS;
      case 'typescriptreact':
        return ts.ScriptKind.TSX;
      case 'javascriptreact':
        return ts.ScriptKind.JSX;
      case 'javascript':
      default:
        return ts.ScriptKind.JS;
    }
  }

  /**
   * 构建AST
   */
  private buildAST(node: ts.Node): ASTNode {
    const range = this.getNodeRange(node);
    
    const astNode: ASTNode = {
      type: ts.SyntaxKind[node.kind],
      name: this.getNodeName(node),
      range,
      children: [],
      metadata: {
        kind: node.kind,
        flags: node.flags,
        modifierFlags: ts.getCombinedModifierFlags(node as ts.Declaration)
      }
    };

    // 递归处理子节点
    if (this.config.maxDepth > 0) {
      ts.forEachChild(node, (child) => {
        const childParser = new TypeScriptParser({
          ...this.config,
          maxDepth: this.config.maxDepth - 1
        });
        childParser.sourceFile = this.sourceFile;
        childParser.typeChecker = this.typeChecker;
        
        astNode.children.push(childParser.buildAST(child));
      });
    }

    return astNode;
  }

  /**
   * 获取节点范围
   */
  private getNodeRange(node: ts.Node): vscode.Range {
    if (!this.sourceFile) {
      return new vscode.Range(0, 0, 0, 0);
    }

    const start = this.sourceFile.getLineAndCharacterOfPosition(node.getStart());
    const end = this.sourceFile.getLineAndCharacterOfPosition(node.getEnd());

    return new vscode.Range(
      new vscode.Position(start.line, start.character),
      new vscode.Position(end.line, end.character)
    );
  }

  /**
   * 获取节点名称
   */
  private getNodeName(node: ts.Node): string | undefined {
    if (ts.isIdentifier(node)) {
      return node.text;
    }

    if (ts.isVariableDeclaration(node) && ts.isIdentifier(node.name)) {
      return node.name.text;
    }

    if (ts.isFunctionDeclaration(node) && node.name && ts.isIdentifier(node.name)) {
      return node.name.text;
    }

    if (ts.isClassDeclaration(node) && node.name && ts.isIdentifier(node.name)) {
      return node.name.text;
    }

    if (ts.isInterfaceDeclaration(node) && ts.isIdentifier(node.name)) {
      return node.name.text;
    }

    if (ts.isMethodDeclaration(node) && ts.isIdentifier(node.name)) {
      return node.name.text;
    }

    if (ts.isPropertyDeclaration(node) && ts.isIdentifier(node.name)) {
      return node.name.text;
    }

    return undefined;
  }

  /**
   * 提取符号信息
   */
  private extractSymbols(sourceFile: ts.SourceFile): SymbolInfo[] {
    const symbols: SymbolInfo[] = [];

    const visit = (node: ts.Node) => {
      // 函数声明
      if (ts.isFunctionDeclaration(node) && node.name) {
        symbols.push(this.createFunctionSymbol(node));
      }
      
      // 类声明
      else if (ts.isClassDeclaration(node) && node.name) {
        symbols.push(this.createClassSymbol(node));
        
        // 处理类成员
        node.members.forEach(member => {
          if (ts.isMethodDeclaration(member) && ts.isIdentifier(member.name)) {
            symbols.push(this.createMethodSymbol(member, node.name!.text));
          } else if (ts.isPropertyDeclaration(member) && ts.isIdentifier(member.name)) {
            symbols.push(this.createPropertySymbol(member, node.name!.text));
          }
        });
      }
      
      // 接口声明
      else if (ts.isInterfaceDeclaration(node)) {
        symbols.push(this.createInterfaceSymbol(node));
      }
      
      // 变量声明
      else if (ts.isVariableDeclaration(node) && ts.isIdentifier(node.name)) {
        symbols.push(this.createVariableSymbol(node));
      }

      ts.forEachChild(node, visit);
    };

    visit(sourceFile);
    return symbols;
  }

  /**
   * 创建函数符号
   */
  private createFunctionSymbol(node: ts.FunctionDeclaration): SymbolInfo {
    const parameters = node.parameters.map(param => this.createParameterInfo(param));
    const isAsync = !!(node.modifiers?.some(mod => mod.kind === ts.SyntaxKind.AsyncKeyword));

    return {
      name: node.name!.text,
      type: 'function',
      range: this.getNodeRange(node),
      visibility: 'public',
      isAsync,
      parameters,
      returnType: node.type ? node.type.getText() : undefined,
      documentation: this.extractDocumentation(node)
    };
  }

  /**
   * 创建类符号
   */
  private createClassSymbol(node: ts.ClassDeclaration): SymbolInfo {
    return {
      name: node.name!.text,
      type: 'class',
      range: this.getNodeRange(node),
      visibility: this.getVisibility(node.modifiers),
      documentation: this.extractDocumentation(node)
    };
  }

  /**
   * 创建接口符号
   */
  private createInterfaceSymbol(node: ts.InterfaceDeclaration): SymbolInfo {
    return {
      name: node.name.text,
      type: 'interface',
      range: this.getNodeRange(node),
      visibility: 'public',
      documentation: this.extractDocumentation(node)
    };
  }

  /**
   * 创建方法符号
   */
  private createMethodSymbol(node: ts.MethodDeclaration, className: string): SymbolInfo {
    const parameters = node.parameters.map(param => this.createParameterInfo(param));
    const isAsync = !!(node.modifiers?.some(mod => mod.kind === ts.SyntaxKind.AsyncKeyword));
    const isStatic = !!(node.modifiers?.some(mod => mod.kind === ts.SyntaxKind.StaticKeyword));

    return {
      name: `${className}.${(node.name as ts.Identifier).text}`,
      type: 'method',
      range: this.getNodeRange(node),
      visibility: this.getVisibility(node.modifiers),
      isAsync,
      isStatic,
      parameters,
      returnType: node.type ? node.type.getText() : undefined,
      documentation: this.extractDocumentation(node)
    };
  }

  /**
   * 创建属性符号
   */
  private createPropertySymbol(node: ts.PropertyDeclaration, className: string): SymbolInfo {
    const isStatic = !!(node.modifiers?.some(mod => mod.kind === ts.SyntaxKind.StaticKeyword));

    return {
      name: `${className}.${(node.name as ts.Identifier).text}`,
      type: 'property',
      range: this.getNodeRange(node),
      visibility: this.getVisibility(node.modifiers),
      isStatic,
      returnType: node.type ? node.type.getText() : undefined,
      documentation: this.extractDocumentation(node)
    };
  }

  /**
   * 创建变量符号
   */
  private createVariableSymbol(node: ts.VariableDeclaration): SymbolInfo {
    const parent = node.parent;
    const isConst = ts.isVariableDeclarationList(parent) && 
                   !!(parent.flags & ts.NodeFlags.Const);

    return {
      name: (node.name as ts.Identifier).text,
      type: isConst ? 'constant' : 'variable',
      range: this.getNodeRange(node),
      visibility: 'public',
      returnType: node.type ? node.type.getText() : undefined
    };
  }

  /**
   * 创建参数信息
   */
  private createParameterInfo(param: ts.ParameterDeclaration): ParameterInfo {
    return {
      name: (param.name as ts.Identifier).text,
      type: param.type ? param.type.getText() : undefined,
      optional: !!param.questionToken,
      defaultValue: param.initializer ? param.initializer.getText() : undefined
    };
  }

  /**
   * 获取可见性
   */
  private getVisibility(modifiers?: ts.NodeArray<ts.ModifierLike>): 'public' | 'private' | 'protected' | 'internal' {
    if (!modifiers) return 'public';

    for (const modifier of modifiers) {
      switch (modifier.kind) {
        case ts.SyntaxKind.PrivateKeyword:
          return 'private';
        case ts.SyntaxKind.ProtectedKeyword:
          return 'protected';
        case ts.SyntaxKind.PublicKeyword:
          return 'public';
      }
    }

    return 'public';
  }

  /**
   * 提取文档注释
   */
  private extractDocumentation(node: ts.Node): string | undefined {
    if (!this.config.includeDocumentation || !this.sourceFile) {
      return undefined;
    }

    const sourceFileText = this.sourceFile.getFullText();
    const commentRanges = ts.getLeadingCommentRanges(sourceFileText, node.getFullStart());
    
    if (!commentRanges || commentRanges.length === 0) {
      return undefined;
    }

    const lastComment = commentRanges[commentRanges.length - 1];
    const commentText = sourceFileText.substring(lastComment.pos, lastComment.end);
    
    // 清理JSDoc注释
    return commentText
      .replace(/\/\*\*|\*\/|\* ?/g, '')
      .trim();
  }

  /**
   * 提取依赖信息
   */
  private extractDependencies(sourceFile: ts.SourceFile): DependencyInfo[] {
    const dependencies: DependencyInfo[] = [];

    const visit = (node: ts.Node) => {
      // import语句
      if (ts.isImportDeclaration(node) && ts.isStringLiteral(node.moduleSpecifier)) {
        dependencies.push(this.createImportDependency(node));
      }
      
      // require调用
      else if (ts.isCallExpression(node) && 
               ts.isIdentifier(node.expression) && 
               node.expression.text === 'require' &&
               node.arguments.length > 0 &&
               ts.isStringLiteral(node.arguments[0])) {
        dependencies.push(this.createRequireDependency(node));
      }

      ts.forEachChild(node, visit);
    };

    visit(sourceFile);
    return dependencies;
  }

  /**
   * 创建import依赖
   */
  private createImportDependency(node: ts.ImportDeclaration): DependencyInfo {
    const module = (node.moduleSpecifier as ts.StringLiteral).text;
    const imported: string[] = [];
    let isDefault = false;
    let alias: string | undefined;

    if (node.importClause) {
      // 默认导入
      if (node.importClause.name) {
        isDefault = true;
        imported.push(node.importClause.name.text);
      }

      // 命名导入
      if (node.importClause.namedBindings) {
        if (ts.isNamedImports(node.importClause.namedBindings)) {
          node.importClause.namedBindings.elements.forEach(element => {
            imported.push(element.name.text);
            if (element.propertyName) {
              alias = element.name.text;
            }
          });
        } else if (ts.isNamespaceImport(node.importClause.namedBindings)) {
          imported.push('*');
          alias = node.importClause.namedBindings.name.text;
        }
      }
    }

    return {
      module,
      type: 'import',
      range: this.getNodeRange(node),
      imported,
      isDefault,
      alias
    };
  }

  /**
   * 创建require依赖
   */
  private createRequireDependency(node: ts.CallExpression): DependencyInfo {
    const module = (node.arguments[0] as ts.StringLiteral).text;

    return {
      module,
      type: 'require',
      range: this.getNodeRange(node),
      imported: ['*']
    };
  }

  /**
   * 提取导出信息
   */
  private extractExports(sourceFile: ts.SourceFile): ExportInfo[] {
    const exports: ExportInfo[] = [];

    const visit = (node: ts.Node) => {
      // export声明
      if (ts.isExportDeclaration(node)) {
        if (node.exportClause && ts.isNamedExports(node.exportClause)) {
          node.exportClause.elements.forEach(element => {
            exports.push({
              name: element.name.text,
              type: 'named',
              range: this.getNodeRange(element),
              isReExport: !!node.moduleSpecifier,
              originalModule: node.moduleSpecifier ? 
                (node.moduleSpecifier as ts.StringLiteral).text : undefined
            });
          });
        }
      }
      
      // export修饰的声明
      else if (node.modifiers?.some(mod => mod.kind === ts.SyntaxKind.ExportKeyword)) {
        const isDefault = node.modifiers.some(mod => mod.kind === ts.SyntaxKind.DefaultKeyword);
        const name = this.getNodeName(node) || 'default';
        
        exports.push({
          name,
          type: isDefault ? 'default' : 'named',
          range: this.getNodeRange(node)
        });
      }

      ts.forEachChild(node, visit);
    };

    visit(sourceFile);
    return exports;
  }
}