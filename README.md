# AI Code Visualizer - Visual Intent Contract

可视化意图契约白板系统 - 确保AI严格按照用户意图执行

## 项目概述

本项目是一个革命性的VSCode插件，实现了"可视化意图契约（Visual Intent Contract）"白板系统。该系统将用户定位为"立法者"，白板作为"宪法"，AI作为严格受约束的"执行者"。

### 核心哲学
- **用户是立法者**（制定规则）
- **白板是宪法**（不可违背的法律）
- **AI是执行者**（严格受约束的工具）
- **任何AI的创造性和"幻觉"都被视为"违宪"行为**

## 开发环境设置

### 前置要求
- Node.js >= 16.x
- VSCode >= 1.74.0
- npm 或 yarn

### 安装依赖
```bash
npm install
```

### 开发命令

#### 编译和构建
```bash
# 编译TypeScript
npm run compile

# 监听模式编译
npm run watch

# 生产环境打包
npm run package
```

#### 代码质量
```bash
# 运行ESLint检查
npm run lint

# 自动修复ESLint问题
npm run lint:fix

# 格式化代码
npm run format

# 检查代码格式
npm run format:check
```

#### 测试
```bash
# 运行所有测试
npm test

# 监听模式运行测试
npm run test:watch

# 生成测试覆盖率报告
npm run test:coverage
```

### 调试扩展

1. 在VSCode中打开项目
2. 按 `F5` 或使用 "Run Extension" 配置启动调试
3. 在新的VSCode窗口中测试扩展功能

### 项目结构

```
├── src/                    # 源代码目录
│   ├── extension.ts        # 扩展入口文件
│   ├── providers/          # 提供者类
│   │   ├── WhiteboardProvider.ts
│   │   └── ExplorerProvider.ts
│   └── **/*.test.ts        # 测试文件
├── dist/                   # 编译输出目录
├── .vscode/                # VSCode配置
├── package.json            # 项目配置
├── tsconfig.json           # TypeScript配置
├── webpack.config.js       # Webpack配置
├── vitest.config.ts        # Vitest测试配置
├── .eslintrc.json          # ESLint配置
└── .prettierrc             # Prettier配置
```

## 技术栈

- **开发语言**: TypeScript
- **框架**: VSCode Extension API
- **前端可视化**: D3.js（计划中）
- **构建工具**: Webpack
- **测试框架**: Vitest
- **代码质量**: ESLint + Prettier
- **代码分析**: TypeScript Compiler API（计划中）

## 开发状态

✅ 项目基础设施搭建完成
- VSCode扩展项目结构
- TypeScript配置
- Webpack构建配置
- 测试框架（Vitest）
- 代码质量工具（ESLint + Prettier）
- 基础命令和视图注册

🚧 待实现功能
- 核心数据模型
- 代码分析引擎
- 可视化白板系统
- 意图编译器
- 差异分析引擎
- 裁决系统

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。