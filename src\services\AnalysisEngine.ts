import * as vscode from 'vscode';
import * as path from 'path';
import { FileChangeEvent } from './FileSystemWatcher';
import { ParserManager } from '../parsers/ParserManager';
import { ParseResult } from '../parsers/BaseParser';
import { DependencyAnalyzer, DependencyGraph } from '../analyzers/DependencyAnalyzer';
import { LegacyCodeClassifier, CodeClassification } from '../classifiers/LegacyCodeClassifier';
import { BlueprintCodeComparator, BlueprintComparison } from '../comparators/BlueprintCodeComparator';

/**
 * 分析结果接口
 */
export interface AnalysisResult {
  fileUri: vscode.Uri;
  analysisType: 'syntax' | 'semantic' | 'dependency' | 'structure';
  timestamp: number;
  success: boolean;
  data?: any;
  errors?: string[];
  warnings?: string[];
}

/**
 * 分析任务接口
 */
export interface AnalysisTask {
  id: string;
  fileUri: vscode.Uri;
  type: 'full' | 'incremental';
  priority: 'high' | 'medium' | 'low';
  timestamp: number;
  retryCount: number;
}

/**
 * 分析引擎配置
 */
export interface AnalysisEngineConfig {
  enableIncrementalAnalysis: boolean;
  enableBackgroundAnalysis: boolean;
  maxConcurrentTasks: number;
  taskTimeout: number;
  retryLimit: number;
  analysisTypes: string[];
}

/**
 * 分析引擎
 * 处理文件变更事件并执行代码分析
 */
export class AnalysisEngine {
  private config: AnalysisEngineConfig;
  private taskQueue: AnalysisTask[] = [];
  private activeTasks = new Map<string, AnalysisTask>();
  private analysisResults = new Map<string, AnalysisResult[]>();
  private resultHandlers: ((result: AnalysisResult) => void)[] = [];
  private isProcessing = false;
  private parserManager: ParserManager;
  private dependencyAnalyzer: DependencyAnalyzer;
  private legacyCodeClassifier: LegacyCodeClassifier;
  private blueprintComparator: BlueprintCodeComparator;
  private lastDependencyGraph: DependencyGraph | null = null;
  private lastClassifications: Map<string, CodeClassification> | null = null;
  private lastComparisons: Map<string, BlueprintComparison> | null = null;

  constructor(config: Partial<AnalysisEngineConfig> = {}) {
    this.config = {
      enableIncrementalAnalysis: true,
      enableBackgroundAnalysis: true,
      maxConcurrentTasks: 3,
      taskTimeout: 30000, // 30秒
      retryLimit: 2,
      analysisTypes: ['syntax', 'semantic', 'dependency', 'structure'],
      ...config
    };

    // 初始化解析器管理器
    this.parserManager = new ParserManager({
      includeComments: true,
      includeDocumentation: true,
      calculateMetrics: true,
      detectPatterns: true,
      maxDepth: 50,
      timeout: 10000
    });

    // 初始化依赖分析器
    this.dependencyAnalyzer = new DependencyAnalyzer({
      includeExternalDependencies: true,
      includeBuiltinModules: false,
      maxDepth: 10,
      circularDependencyThreshold: 3,
      enableCaching: true
    });

    // 初始化遗留代码分类器
    this.legacyCodeClassifier = new LegacyCodeClassifier({
      complexityThresholds: {
        low: 10,
        medium: 20,
        high: 30
      },
      maintainabilityThresholds: {
        excellent: 80,
        good: 60,
        fair: 40,
        poor: 20
      },
      enableStrictMode: false
    });

    // 初始化蓝图代码比对器
    this.blueprintComparator = new BlueprintCodeComparator({
      strictMode: false,
      ignorePrivateMembers: true,
      ignoreTestFiles: true,
      minimumConfidence: 0.6,
      enableFuzzyMatching: true,
      fuzzyThreshold: 0.8
    });
  }

  /**
   * 启动分析引擎
   */
  public start(): void {
    console.log('🔬 AnalysisEngine started with config:', this.config);
    
    if (this.config.enableBackgroundAnalysis) {
      this.startBackgroundProcessing();
    }
  }

  /**
   * 停止分析引擎
   */
  public stop(): void {
    this.isProcessing = false;
    this.taskQueue = [];
    this.activeTasks.clear();
    console.log('🛑 AnalysisEngine stopped');
  }

  /**
   * 处理文件变更事件
   */
  public handleFileChange(event: FileChangeEvent): void {
    console.log(`📝 Processing file change: ${event.type} - ${event.relativePath}`);

    // 根据文件类型和变更类型决定分析策略
    const analysisType = this.determineAnalysisType(event);
    const priority = this.determinePriority(event);

    if (analysisType) {
      this.queueAnalysisTask({
        id: this.generateTaskId(event),
        fileUri: event.uri,
        type: analysisType,
        priority,
        timestamp: event.timestamp,
        retryCount: 0
      });
    }
  }

  /**
   * 添加分析结果处理器
   */
  public onAnalysisResult(handler: (result: AnalysisResult) => void): vscode.Disposable {
    this.resultHandlers.push(handler);

    return new vscode.Disposable(() => {
      const index = this.resultHandlers.indexOf(handler);
      if (index > -1) {
        this.resultHandlers.splice(index, 1);
      }
    });
  }

  /**
   * 队列分析任务
   */
  private queueAnalysisTask(task: AnalysisTask): void {
    // 检查是否已有相同文件的任务
    const existingIndex = this.taskQueue.findIndex(
      t => t.fileUri.toString() === task.fileUri.toString()
    );

    if (existingIndex > -1) {
      // 更新现有任务
      this.taskQueue[existingIndex] = task;
    } else {
      // 添加新任务
      this.taskQueue.push(task);
    }

    // 按优先级排序
    this.taskQueue.sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      return priorityOrder[b.priority] - priorityOrder[a.priority];
    });

    console.log(`📋 Queued analysis task: ${task.id} (${task.type}, ${task.priority})`);
  }

  /**
   * 开始后台处理
   */
  private startBackgroundProcessing(): void {
    this.isProcessing = true;
    this.processTaskQueue();
  }

  /**
   * 处理任务队列
   */
  private async processTaskQueue(): Promise<void> {
    while (this.isProcessing) {
      // 检查是否有可用的处理槽位
      if (this.activeTasks.size >= this.config.maxConcurrentTasks) {
        await this.sleep(100);
        continue;
      }

      // 获取下一个任务
      const task = this.taskQueue.shift();
      if (!task) {
        await this.sleep(100);
        continue;
      }

      // 执行任务
      this.executeTask(task);
    }
  }

  /**
   * 执行分析任务
   */
  private async executeTask(task: AnalysisTask): Promise<void> {
    this.activeTasks.set(task.id, task);

    try {
      console.log(`🔍 Executing analysis task: ${task.id}`);
      
      const result = await this.performAnalysis(task);
      this.handleAnalysisResult(result);
      
    } catch (error) {
      console.error(`❌ Analysis task failed: ${task.id}`, error);
      
      // 重试逻辑
      if (task.retryCount < this.config.retryLimit) {
        task.retryCount++;
        this.queueAnalysisTask(task);
      } else {
        // 创建失败结果
        const failureResult: AnalysisResult = {
          fileUri: task.fileUri,
          analysisType: 'syntax', // 默认类型
          timestamp: Date.now(),
          success: false,
          errors: [error instanceof Error ? error.message : 'Unknown error']
        };
        this.handleAnalysisResult(failureResult);
      }
    } finally {
      this.activeTasks.delete(task.id);
    }
  }

  /**
   * 执行实际的分析
   */
  private async performAnalysis(task: AnalysisTask): Promise<AnalysisResult> {
    const document = await vscode.workspace.openTextDocument(task.fileUri);

    try {
      // 使用解析器管理器进行深度分析
      const parseResult = await this.parserManager.parse(document);

      const result: AnalysisResult = {
        fileUri: task.fileUri,
        analysisType: 'syntax',
        timestamp: Date.now(),
        success: parseResult.success,
        data: {
          // 基础信息
          lineCount: document.lineCount,
          characterCount: document.getText().length,
          language: document.languageId,
          hasErrors: parseResult.errors.length > 0,
          
          // 解析结果
          symbols: parseResult.symbols,
          dependencies: parseResult.dependencies.map(dep => dep.module),
          exports: parseResult.exports.map(exp => exp.name),
          
          // 代码度量
          metrics: parseResult.metrics,
          
          // 详细分析数据
          parseResult: parseResult,
          
          // 兼容旧格式
          complexity: parseResult.metrics.cyclomaticComplexity,
          functions: parseResult.symbols
            .filter(s => s.type === 'function' || s.type === 'method')
            .map(s => s.name)
        },
        errors: parseResult.errors.map(err => err.message),
        warnings: parseResult.warnings.map(warn => warn.message)
      };

      return result;

    } catch (error) {
      // 降级到简单分析
      const content = document.getText();
      
      return {
        fileUri: task.fileUri,
        analysisType: 'syntax',
        timestamp: Date.now(),
        success: false,
        data: {
          lineCount: document.lineCount,
          characterCount: content.length,
          language: document.languageId,
          hasErrors: true,
          complexity: this.calculateComplexity(content),
          dependencies: this.extractDependencies(content),
          exports: this.extractExports(content),
          functions: this.extractFunctions(content)
        },
        errors: [`Analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`]
      };
    }
  }

  /**
   * 处理分析结果
   */
  private handleAnalysisResult(result: AnalysisResult): void {
    const fileKey = result.fileUri.toString();
    
    // 存储结果
    if (!this.analysisResults.has(fileKey)) {
      this.analysisResults.set(fileKey, []);
    }
    this.analysisResults.get(fileKey)!.push(result);

    // 触发结果处理器
    this.resultHandlers.forEach(handler => {
      try {
        handler(result);
      } catch (error) {
        console.error('Error in analysis result handler:', error);
      }
    });

    console.log(`✅ Analysis completed: ${result.fileUri.fsPath} (${result.analysisType})`);
  }

  /**
   * 确定分析类型
   */
  private determineAnalysisType(event: FileChangeEvent): 'full' | 'incremental' | null {
    if (event.fileType !== 'code') {
      return null; // 不分析非代码文件
    }

    switch (event.type) {
      case 'created':
        return 'full';
      case 'changed':
        return this.config.enableIncrementalAnalysis ? 'incremental' : 'full';
      case 'deleted':
        return null; // 删除的文件不需要分析
      default:
        return null;
    }
  }

  /**
   * 确定任务优先级
   */
  private determinePriority(event: FileChangeEvent): 'high' | 'medium' | 'low' {
    // 当前打开的文件优先级高
    const activeEditor = vscode.window.activeTextEditor;
    if (activeEditor && activeEditor.document.uri.toString() === event.uri.toString()) {
      return 'high';
    }

    // 配置文件优先级中等
    if (event.fileType === 'config') {
      return 'medium';
    }

    // 其他文件优先级低
    return 'low';
  }

  /**
   * 生成任务ID
   */
  private generateTaskId(event: FileChangeEvent): string {
    return `${event.type}_${path.basename(event.uri.fsPath)}_${event.timestamp}`;
  }

  /**
   * 计算代码复杂度（简化版）
   */
  private calculateComplexity(content: string): number {
    const complexityKeywords = [
      'if', 'else', 'for', 'while', 'switch', 'case', 
      'try', 'catch', 'function', 'class', '&&', '||'
    ];
    
    let complexity = 1; // 基础复杂度
    
    complexityKeywords.forEach(keyword => {
      const regex = new RegExp(`\\b${keyword}\\b`, 'g');
      const matches = content.match(regex);
      if (matches) {
        complexity += matches.length;
      }
    });

    return complexity;
  }

  /**
   * 提取依赖关系
   */
  private extractDependencies(content: string): string[] {
    const dependencies: string[] = [];
    
    // 匹配 import 语句
    const importRegex = /import\s+.*?\s+from\s+['"]([^'"]+)['"]/g;
    let match;
    while ((match = importRegex.exec(content)) !== null) {
      dependencies.push(match[1]);
    }

    // 匹配 require 语句
    const requireRegex = /require\s*\(\s*['"]([^'"]+)['"]\s*\)/g;
    while ((match = requireRegex.exec(content)) !== null) {
      dependencies.push(match[1]);
    }

    return [...new Set(dependencies)]; // 去重
  }

  /**
   * 提取导出
   */
  private extractExports(content: string): string[] {
    const exports: string[] = [];
    
    // 匹配 export 语句
    const exportRegex = /export\s+(?:default\s+)?(?:class|function|const|let|var)\s+(\w+)/g;
    let match;
    while ((match = exportRegex.exec(content)) !== null) {
      exports.push(match[1]);
    }

    return exports;
  }

  /**
   * 提取函数
   */
  private extractFunctions(content: string): string[] {
    const functions: string[] = [];
    
    // 匹配函数定义
    const functionRegex = /(?:function\s+(\w+)|(\w+)\s*:\s*\([^)]*\)\s*=>|(\w+)\s*\([^)]*\)\s*{)/g;
    let match;
    while ((match = functionRegex.exec(content)) !== null) {
      const functionName = match[1] || match[2] || match[3];
      if (functionName) {
        functions.push(functionName);
      }
    }

    return functions;
  }

  /**
   * 获取分析结果
   */
  public getAnalysisResults(fileUri: vscode.Uri): AnalysisResult[] {
    return this.analysisResults.get(fileUri.toString()) || [];
  }

  /**
   * 清除分析结果
   */
  public clearAnalysisResults(fileUri?: vscode.Uri): void {
    if (fileUri) {
      this.analysisResults.delete(fileUri.toString());
    } else {
      this.analysisResults.clear();
    }
  }

  /**
   * 获取统计信息
   */
  public getStats(): {
    queuedTasks: number;
    activeTasks: number;
    totalResults: number;
    isProcessing: boolean;
  } {
    return {
      queuedTasks: this.taskQueue.length,
      activeTasks: this.activeTasks.size,
      totalResults: Array.from(this.analysisResults.values()).reduce((sum, results) => sum + results.length, 0),
      isProcessing: this.isProcessing
    };
  }

  /**
   * 获取解析器管理器
   */
  public getParserManager(): ParserManager {
    return this.parserManager;
  }

  /**
   * 获取支持的语言列表
   */
  public getSupportedLanguages(): string[] {
    return this.parserManager.getSupportedLanguages();
  }

  /**
   * 获取解析器统计信息
   */
  public getParserStats(): any {
    return this.parserManager.getStats();
  }

  /**
   * 批量分析文档
   */
  public async analyzeDocuments(documents: vscode.TextDocument[]): Promise<Map<string, AnalysisResult>> {
    const results = new Map<string, AnalysisResult>();
    
    for (const document of documents) {
      try {
        const task: AnalysisTask = {
          id: `batch_${document.fileName}_${Date.now()}`,
          fileUri: document.uri,
          type: 'full',
          priority: 'medium',
          timestamp: Date.now(),
          retryCount: 0
        };

        const result = await this.performAnalysis(task);
        results.set(document.uri.toString(), result);
        
        // 触发结果处理器
        this.handleAnalysisResult(result);
        
      } catch (error) {
        console.error(`Failed to analyze ${document.fileName}:`, error);
      }
    }

    return results;
  }

  /**
   * 获取详细的分析报告
   */
  public getDetailedReport(): string {
    const stats = this.getStats();
    const parserStats = this.getParserStats();
    
    let report = '🔬 Analysis Engine Report\n';
    report += '=========================\n\n';
    
    report += `📊 Analysis Statistics:\n`;
    report += `  • Queued Tasks: ${stats.queuedTasks}\n`;
    report += `  • Active Tasks: ${stats.activeTasks}\n`;
    report += `  • Total Results: ${stats.totalResults}\n`;
    report += `  • Processing: ${stats.isProcessing ? '🟢 Yes' : '🔴 No'}\n\n`;
    
    report += `🔧 Parser Statistics:\n`;
    report += `  • Total Parsed: ${parserStats.totalParsed}\n`;
    report += `  • Success Rate: ${((parserStats.successfulParsed / parserStats.totalParsed) * 100).toFixed(1)}%\n`;
    report += `  • Average Parse Time: ${parserStats.averageParseTime.toFixed(2)}ms\n\n`;
    
    report += `🌐 Supported Languages:\n`;
    this.getSupportedLanguages().forEach(lang => {
      const count = parserStats.languageStats[lang] || 0;
      report += `  • ${lang}: ${count} files parsed\n`;
    });
    
    return report;
  }

  /**
   * 分析项目依赖关系
   */
  public async analyzeDependencies(): Promise<DependencyGraph> {
    console.log('🔍 Starting project dependency analysis...');
    
    // 获取所有解析结果
    const parseResults = new Map<string, ParseResult>();
    
    for (const [fileUri, results] of this.analysisResults) {
      const latestResult = results[results.length - 1];
      if (latestResult.success && latestResult.data?.parseResult) {
        parseResults.set(fileUri, latestResult.data.parseResult);
      }
    }

    // 执行依赖分析
    this.lastDependencyGraph = await this.dependencyAnalyzer.analyzeProject(parseResults);
    
    console.log(`✅ Dependency analysis completed: ${this.lastDependencyGraph.statistics.totalNodes} nodes, ${this.lastDependencyGraph.statistics.circularDependencies} cycles`);
    
    return this.lastDependencyGraph;
  }

  /**
   * 获取依赖分析器
   */
  public getDependencyAnalyzer(): DependencyAnalyzer {
    return this.dependencyAnalyzer;
  }

  /**
   * 获取最新的依赖图
   */
  public getLastDependencyGraph(): DependencyGraph | null {
    return this.lastDependencyGraph;
  }

  /**
   * 获取循环依赖
   */
  public getCircularDependencies() {
    return this.lastDependencyGraph?.circularDependencies || [];
  }

  /**
   * 生成依赖分析报告
   */
  public generateDependencyReport(): string {
    if (!this.lastDependencyGraph) {
      return '⚠️ No dependency analysis available. Please run dependency analysis first.';
    }

    return this.dependencyAnalyzer.generateReport();
  }

  /**
   * 分类遗留代码
   */
  public async classifyLegacyCode(): Promise<Map<string, CodeClassification>> {
    console.log('🏗️ Starting legacy code classification...');
    
    // 获取所有解析结果
    const parseResults = new Map<string, ParseResult>();
    
    for (const [fileUri, results] of this.analysisResults) {
      const latestResult = results[results.length - 1];
      if (latestResult.success && latestResult.data?.parseResult) {
        parseResults.set(fileUri, latestResult.data.parseResult);
      }
    }

    // 确保有依赖图
    if (!this.lastDependencyGraph) {
      await this.analyzeDependencies();
    }

    // 执行代码分类
    this.lastClassifications = await this.legacyCodeClassifier.classifyProject(
      parseResults, 
      this.lastDependencyGraph!
    );
    
    console.log(`✅ Legacy code classification completed: ${this.lastClassifications.size} files classified`);
    
    return this.lastClassifications;
  }

  /**
   * 获取遗留代码分类器
   */
  public getLegacyCodeClassifier(): LegacyCodeClassifier {
    return this.legacyCodeClassifier;
  }

  /**
   * 获取最新的代码分类结果
   */
  public getLastClassifications(): Map<string, CodeClassification> | null {
    return this.lastClassifications;
  }

  /**
   * 生成代码分类报告
   */
  public generateClassificationReport(): string {
    if (!this.lastClassifications) {
      return '⚠️ No code classification available. Please run legacy code classification first.';
    }

    return this.legacyCodeClassifier.generateReport();
  }

  /**
   * 比对蓝图与代码
   */
  public async compareBlueprintWithCode(blueprint: any): Promise<Map<string, BlueprintComparison>> {
    console.log('🔍 Starting blueprint-code comparison...');
    
    // 获取所有解析结果
    const parseResults = new Map<string, ParseResult>();
    
    for (const [fileUri, results] of this.analysisResults) {
      const latestResult = results[results.length - 1];
      if (latestResult.success && latestResult.data?.parseResult) {
        parseResults.set(fileUri, latestResult.data.parseResult);
      }
    }

    // 确保有依赖图和代码分类
    if (!this.lastDependencyGraph) {
      await this.analyzeDependencies();
    }
    
    if (!this.lastClassifications) {
      await this.classifyLegacyCode();
    }

    // 执行蓝图比对
    this.lastComparisons = await this.blueprintComparator.compareProject(
      blueprint,
      parseResults,
      this.lastClassifications!,
      this.lastDependencyGraph!
    );
    
    console.log(`✅ Blueprint comparison completed: ${this.lastComparisons.size} files compared`);
    
    return this.lastComparisons;
  }

  /**
   * 获取蓝图代码比对器
   */
  public getBlueprintComparator(): BlueprintCodeComparator {
    return this.blueprintComparator;
  }

  /**
   * 获取最新的比对结果
   */
  public getLastComparisons(): Map<string, BlueprintComparison> | null {
    return this.lastComparisons;
  }

  /**
   * 生成蓝图比对报告
   */
  public generateComparisonReport(): string {
    if (!this.lastComparisons) {
      return '⚠️ No blueprint comparison available. Please run blueprint comparison first.';
    }

    return this.blueprintComparator.generateReport();
  }

  /**
   * 睡眠函数
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}