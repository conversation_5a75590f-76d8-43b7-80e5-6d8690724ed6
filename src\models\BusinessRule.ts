import {
  BaseEntity,
  LogicCondition,
  LogicAction,
  TraceabilityInfo,
  RuleConflict,
} from './interfaces';
import { BusinessRuleType, LogicOperator, ValidationSeverity } from './enums';
import { ValidationResult, ValidationResultImpl } from './ValidationResult';

/**
 * 业务规则类
 * 定义项目的业务逻辑"铁律"，作为"宪法"的核心组成部分
 */
export class BusinessRule implements BaseEntity {
  public id: string;
  public name: string;
  public description?: string;
  public createdAt: Date;
  public updatedAt: Date;
  public version: string;
  public type: BusinessRuleType;
  public condition: LogicCondition;
  public action: LogicAction;
  public priority: number;
  public isActive: boolean;
  public conflicts?: string[];
  public traceability?: TraceabilityInfo;

  constructor(data: Partial<BusinessRule> = {}) {
    this.id = data.id || this.generateId();
    this.name = data.name || '';
    this.description = data.description;
    this.createdAt = data.createdAt || new Date();
    this.updatedAt = data.updatedAt || new Date();
    this.version = data.version || '1.0.0';
    this.type = data.type || BusinessRuleType.VALIDATION;
    this.condition = data.condition || this.createDefaultCondition();
    this.action = data.action || this.createDefaultAction();
    this.priority = data.priority || 0;
    this.isActive = data.isActive !== undefined ? data.isActive : true;
    this.conflicts = data.conflicts;
    this.traceability = data.traceability;
  }

  /**
   * 验证业务规则的完整性
   */
  validate(): ValidationResult {
    const result = new ValidationResultImpl();

    // 验证基本信息
    if (!this.name || this.name.trim() === '') {
      result.addError('Business rule name is required', 'name', 'REQUIRED');
    }

    if (!/^[a-zA-Z][a-zA-Z0-9_\s]*$/.test(this.name)) {
      result.addError(
        'Business rule name must start with a letter and contain only letters, numbers, underscores, and spaces',
        'name',
        'INVALID_FORMAT'
      );
    }

    // 验证优先级
    if (this.priority < 0 || this.priority > 100) {
      result.addError(
        'Priority must be between 0 and 100',
        'priority',
        'INVALID_RANGE'
      );
    }

    // 验证条件
    const conditionValidation = this.validateCondition(this.condition);
    result.merge(conditionValidation);

    // 验证动作
    const actionValidation = this.validateAction(this.action);
    result.merge(actionValidation);

    return result;
  }

  /**
   * 验证逻辑条件
   */
  private validateCondition(condition: LogicCondition): ValidationResult {
    const result = new ValidationResultImpl();

    if (!condition.operator) {
      result.addError(
        'Logic condition must have an operator',
        'condition.operator',
        'REQUIRED'
      );
      return result;
    }

    if (!condition.operands || condition.operands.length === 0) {
      result.addError(
        'Logic condition must have operands',
        'condition.operands',
        'REQUIRED'
      );
      return result;
    }

    // 验证操作符和操作数的匹配
    switch (condition.operator) {
      case LogicOperator.NOT:
        if (condition.operands.length !== 1) {
          result.addError(
            'NOT operator requires exactly one operand',
            'condition.operands',
            'INVALID_OPERAND_COUNT'
          );
        }
        break;
      case LogicOperator.AND:
      case LogicOperator.OR:
        if (condition.operands.length < 2) {
          result.addError(
            `${condition.operator} operator requires at least two operands`,
            'condition.operands',
            'INVALID_OPERAND_COUNT'
          );
        }
        break;
      case LogicOperator.EQUALS:
      case LogicOperator.NOT_EQUALS:
      case LogicOperator.GREATER_THAN:
      case LogicOperator.LESS_THAN:
      case LogicOperator.GREATER_EQUAL:
      case LogicOperator.LESS_EQUAL:
        if (condition.operands.length !== 2) {
          result.addError(
            `${condition.operator} operator requires exactly two operands`,
            'condition.operands',
            'INVALID_OPERAND_COUNT'
          );
        }
        break;
    }

    // 递归验证嵌套条件
    for (let i = 0; i < condition.operands.length; i++) {
      const operand = condition.operands[i];
      if ('operator' in operand) {
        const nestedValidation = this.validateCondition(
          operand as LogicCondition
        );
        result.merge(nestedValidation);
      }
    }

    return result;
  }

  /**
   * 验证逻辑动作
   */
  private validateAction(action: LogicAction): ValidationResult {
    const result = new ValidationResultImpl();

    if (!action.type) {
      result.addError(
        'Logic action must have a type',
        'action.type',
        'REQUIRED'
      );
    }

    if (!action.target) {
      result.addError(
        'Logic action must have a target',
        'action.target',
        'REQUIRED'
      );
    }

    return result;
  }

  /**
   * 检测与其他规则的冲突
   */
  detectConflicts(otherRules: BusinessRule[]): RuleConflict[] {
    const conflicts: RuleConflict[] = [];

    for (const otherRule of otherRules) {
      if (otherRule.id === this.id) continue;

      // 检测逻辑矛盾
      const logicalConflict = this.detectLogicalConflict(otherRule);
      if (logicalConflict) {
        conflicts.push(logicalConflict);
      }

      // 检测优先级冲突
      const priorityConflict = this.detectPriorityConflict(otherRule);
      if (priorityConflict) {
        conflicts.push(priorityConflict);
      }

      // 检测作用域重叠
      const scopeConflict = this.detectScopeConflict(otherRule);
      if (scopeConflict) {
        conflicts.push(scopeConflict);
      }
    }

    return conflicts;
  }

  /**
   * 检测逻辑矛盾
   */
  private detectLogicalConflict(otherRule: BusinessRule): RuleConflict | null {
    // 简化的逻辑矛盾检测
    // 实际实现需要更复杂的逻辑分析
    if (this.isDirectlyContradictory(this.condition, otherRule.condition)) {
      return {
        id: this.generateId(),
        conflictingRuleIds: [this.id, otherRule.id],
        conflictType: 'LOGICAL_CONTRADICTION',
        description: `Rules "${this.name}" and "${otherRule.name}" have contradictory conditions`,
        severity: ValidationSeverity.ERROR,
        suggestedResolutions: [
          {
            id: this.generateId(),
            description: 'Modify one of the rule conditions',
            action: 'MODIFY_RULE',
            parameters: { ruleId: this.id },
          },
          {
            id: this.generateId(),
            description: 'Delete one of the conflicting rules',
            action: 'DELETE_RULE',
            parameters: { ruleId: otherRule.id },
          },
        ],
      };
    }

    return null;
  }

  /**
   * 检测优先级冲突
   */
  private detectPriorityConflict(otherRule: BusinessRule): RuleConflict | null {
    if (
      this.priority === otherRule.priority &&
      this.hasOverlappingScope(otherRule)
    ) {
      return {
        id: this.generateId(),
        conflictingRuleIds: [this.id, otherRule.id],
        conflictType: 'PRIORITY_CONFLICT',
        description: `Rules "${this.name}" and "${otherRule.name}" have the same priority but overlapping scope`,
        severity: ValidationSeverity.WARNING,
        suggestedResolutions: [
          {
            id: this.generateId(),
            description: 'Change priority of one rule',
            action: 'CHANGE_PRIORITY',
            parameters: { ruleId: this.id, newPriority: this.priority + 1 },
          },
        ],
      };
    }

    return null;
  }

  /**
   * 检测作用域重叠
   */
  private detectScopeConflict(otherRule: BusinessRule): RuleConflict | null {
    if (
      this.hasOverlappingScope(otherRule) &&
      this.hasConflictingActions(otherRule)
    ) {
      return {
        id: this.generateId(),
        conflictingRuleIds: [this.id, otherRule.id],
        conflictType: 'SCOPE_OVERLAP',
        description: `Rules "${this.name}" and "${otherRule.name}" have overlapping scope with conflicting actions`,
        severity: ValidationSeverity.WARNING,
        suggestedResolutions: [
          {
            id: this.generateId(),
            description: 'Add conditions to narrow scope',
            action: 'ADD_CONDITION',
            parameters: { ruleId: this.id },
          },
        ],
      };
    }

    return null;
  }

  /**
   * 检查是否直接矛盾
   */
  private isDirectlyContradictory(
    _condition1: LogicCondition,
    _condition2: LogicCondition
  ): boolean {
    // 简化的矛盾检测逻辑
    // 实际实现需要更复杂的逻辑分析
    return false;
  }

  /**
   * 检查是否有重叠的作用域
   */
  private hasOverlappingScope(otherRule: BusinessRule): boolean {
    // 简化的作用域重叠检测
    // 实际实现需要分析条件和目标的重叠
    return this.action.target === otherRule.action.target;
  }

  /**
   * 检查是否有冲突的动作
   */
  private hasConflictingActions(otherRule: BusinessRule): boolean {
    // 简化的动作冲突检测
    return (
      this.action.type !== otherRule.action.type &&
      this.action.target === otherRule.action.target
    );
  }

  /**
   * 评估规则是否适用于给定的上下文
   */
  evaluate(context: Record<string, any>): boolean {
    return this.evaluateCondition(this.condition, context);
  }

  /**
   * 评估逻辑条件
   */
  private evaluateCondition(
    condition: LogicCondition,
    context: Record<string, any>
  ): boolean {
    switch (condition.operator) {
      case LogicOperator.AND:
        return condition.operands.every((operand) =>
          'operator' in operand
            ? this.evaluateCondition(operand as LogicCondition, context)
            : this.evaluateValue(operand, context)
        );

      case LogicOperator.OR:
        return condition.operands.some((operand) =>
          'operator' in operand
            ? this.evaluateCondition(operand as LogicCondition, context)
            : this.evaluateValue(operand, context)
        );

      case LogicOperator.NOT:
        const operand = condition.operands[0];
        return !('operator' in operand)
          ? !this.evaluateValue(operand, context)
          : !this.evaluateCondition(operand as LogicCondition, context);

      case LogicOperator.EQUALS:
        const [left, right] = condition.operands;
        return this.getValue(left, context) === this.getValue(right, context);

      // 其他操作符的实现...
      default:
        return false;
    }
  }

  /**
   * 评估逻辑值
   */
  private evaluateValue(value: any, context: Record<string, any>): boolean {
    const actualValue = this.getValue(value, context);
    return Boolean(actualValue);
  }

  /**
   * 获取值
   */
  private getValue(value: any, context: Record<string, any>): any {
    if (typeof value === 'object' && value.type) {
      switch (value.type) {
        case 'FIELD':
          return context[value.value];
        case 'CONSTANT':
          return value.value;
        case 'FUNCTION':
          // 函数调用的实现
          return this.callFunction(value.value, context);
        default:
          return value.value;
      }
    }
    return value;
  }

  /**
   * 调用函数
   */
  private callFunction(
    functionName: string,
    context: Record<string, any>
  ): any {
    // 简化的函数调用实现
    // 实际实现需要支持更多的内置函数
    switch (functionName) {
      case 'NOW':
        return new Date();
      case 'TODAY':
        return new Date().toDateString();
      default:
        return null;
    }
  }

  /**
   * 执行规则动作
   */
  executeAction(context: Record<string, any>): Record<string, any> {
    const result = { ...context };

    switch (this.action.type) {
      case 'SET_VALUE':
        result[this.action.target] = this.action.value;
        break;
      case 'SHOW_MESSAGE':
        // 在实际实现中，这里会触发UI消息显示
        console.log(`Message: ${this.action.value}`);
        break;
      case 'TRIGGER_EVENT':
        // 在实际实现中，这里会触发事件
        console.log(`Event triggered: ${this.action.target}`);
        break;
      case 'CALL_FUNCTION':
        // 在实际实现中，这里会调用指定的函数
        console.log(`Function called: ${this.action.target}`);
        break;
    }

    return result;
  }

  /**
   * 创建默认条件
   */
  private createDefaultCondition(): LogicCondition {
    return {
      operator: LogicOperator.EQUALS,
      operands: [
        { type: 'CONSTANT', value: true },
        { type: 'CONSTANT', value: true },
      ],
    };
  }

  /**
   * 创建默认动作
   */
  private createDefaultAction(): LogicAction {
    return {
      type: 'SHOW_MESSAGE',
      target: 'user',
      value: 'Rule executed',
    };
  }

  /**
   * 序列化为JSON
   */
  toJSON(): Record<string, any> {
    return {
      id: this.id,
      name: this.name,
      description: this.description,
      createdAt: this.createdAt.toISOString(),
      updatedAt: this.updatedAt.toISOString(),
      version: this.version,
      type: this.type,
      condition: this.condition,
      action: this.action,
      priority: this.priority,
      isActive: this.isActive,
      conflicts: this.conflicts,
      traceability: this.traceability,
    };
  }

  /**
   * 从JSON反序列化
   */
  static fromJSON(json: Record<string, any>): BusinessRule {
    return new BusinessRule({
      ...json,
      createdAt: new Date(json.createdAt),
      updatedAt: new Date(json.updatedAt),
    });
  }

  /**
   * 克隆规则
   */
  clone(): BusinessRule {
    return BusinessRule.fromJSON(this.toJSON());
  }

  /**
   * 更新时间戳
   */
  private touch(): void {
    this.updatedAt = new Date();
  }

  /**
   * 生成唯一ID
   */
  private generateId(): string {
    return `rule_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
