import {
  BaseEntity,
  Relationship,
  BlueprintMetadata,
  DataRequirement,
} from './interfaces';
import { ModuleType, ModuleStatus } from './enums';
import { ValidationResult, ValidationResultImpl } from './ValidationResult';
import { DataEntity } from './DataEntity';
import { BusinessRule } from './BusinessRule';
import { UIComponent } from './UIComponent';

/**
 * 功能模块接口
 */
export interface FunctionalModule extends BaseEntity {
  type: ModuleType;
  subModules: FunctionalModule[];
  uiComponents: UIComponent[];
  dataRequirements: DataRequirement[];
  businessRules: string[]; // 引用BusinessRule的ID
  status: ModuleStatus;
  dependencies: string[]; // 依赖的其他模块ID
  estimatedEffort: number; // 预估工作量（小时）
}

/**
 * 功能模块实现类
 */
export class FunctionalModuleImpl implements FunctionalModule {
  public id: string;
  public name: string;
  public description?: string;
  public createdAt: Date;
  public updatedAt: Date;
  public version: string;
  public type: ModuleType;
  public subModules: FunctionalModule[];
  public uiComponents: UIComponent[];
  public dataRequirements: DataRequirement[];
  public businessRules: string[];
  public status: ModuleStatus;
  public dependencies: string[];
  public estimatedEffort: number;

  constructor(data: Partial<FunctionalModule> = {}) {
    this.id = data.id || this.generateId();
    this.name = data.name || '';
    this.description = data.description;
    this.createdAt = data.createdAt || new Date();
    this.updatedAt = data.updatedAt || new Date();
    this.version = data.version || '1.0.0';
    this.type = data.type || ModuleType.BUSINESS_LOGIC;
    this.subModules = data.subModules || [];
    this.uiComponents = data.uiComponents || [];
    this.dataRequirements = data.dataRequirements || [];
    this.businessRules = data.businessRules || [];
    this.status = data.status || ModuleStatus.PLANNED;
    this.dependencies = data.dependencies || [];
    this.estimatedEffort = data.estimatedEffort || 0;
  }

  /**
   * 添加子模块
   */
  addSubModule(subModule: FunctionalModule): void {
    this.subModules.push(subModule);
    this.touch();
  }

  /**
   * 删除子模块
   */
  removeSubModule(subModuleId: string): boolean {
    const initialLength = this.subModules.length;
    this.subModules = this.subModules.filter((m) => m.id !== subModuleId);
    if (this.subModules.length < initialLength) {
      this.touch();
      return true;
    }
    return false;
  }

  /**
   * 添加UI组件
   */
  addUIComponent(component: UIComponent): void {
    component.parentModuleId = this.id;
    this.uiComponents.push(component);
    this.touch();
  }

  /**
   * 删除UI组件
   */
  removeUIComponent(componentId: string): boolean {
    const initialLength = this.uiComponents.length;
    this.uiComponents = this.uiComponents.filter((c) => c.id !== componentId);
    if (this.uiComponents.length < initialLength) {
      this.touch();
      return true;
    }
    return false;
  }

  /**
   * 添加数据需求
   */
  addDataRequirement(requirement: DataRequirement): void {
    this.dataRequirements.push(requirement);
    this.touch();
  }

  /**
   * 删除数据需求
   */
  removeDataRequirement(entityId: string): boolean {
    const initialLength = this.dataRequirements.length;
    this.dataRequirements = this.dataRequirements.filter(
      (r) => r.entityId !== entityId
    );
    if (this.dataRequirements.length < initialLength) {
      this.touch();
      return true;
    }
    return false;
  }

  /**
   * 添加业务规则引用
   */
  addBusinessRule(ruleId: string): void {
    if (!this.businessRules.includes(ruleId)) {
      this.businessRules.push(ruleId);
      this.touch();
    }
  }

  /**
   * 删除业务规则引用
   */
  removeBusinessRule(ruleId: string): boolean {
    const initialLength = this.businessRules.length;
    this.businessRules = this.businessRules.filter((id) => id !== ruleId);
    if (this.businessRules.length < initialLength) {
      this.touch();
      return true;
    }
    return false;
  }

  /**
   * 添加依赖
   */
  addDependency(moduleId: string): void {
    if (!this.dependencies.includes(moduleId)) {
      this.dependencies.push(moduleId);
      this.touch();
    }
  }

  /**
   * 删除依赖
   */
  removeDependency(moduleId: string): boolean {
    const initialLength = this.dependencies.length;
    this.dependencies = this.dependencies.filter((id) => id !== moduleId);
    if (this.dependencies.length < initialLength) {
      this.touch();
      return true;
    }
    return false;
  }

  /**
   * 更新状态
   */
  updateStatus(status: ModuleStatus): void {
    this.status = status;
    this.touch();
  }

  /**
   * 验证模块
   */
  validate(): ValidationResult {
    const result = new ValidationResultImpl();

    // 验证基本信息
    if (!this.name || this.name.trim() === '') {
      result.addError('Module name is required', 'name', 'REQUIRED');
    }

    // 验证模块类型特定的要求
    const typeValidation = this.validateModuleType();
    result.merge(typeValidation);

    // 验证子模块
    for (const subModule of this.subModules) {
      if (subModule instanceof FunctionalModuleImpl) {
        const subModuleValidation = subModule.validate();
        result.merge(subModuleValidation);
      }
    }

    // 验证UI组件
    for (const component of this.uiComponents) {
      const componentValidation = component.validate();
      result.merge(componentValidation);
    }

    return result;
  }

  /**
   * 验证模块类型特定的要求
   */
  private validateModuleType(): ValidationResult {
    const result = new ValidationResultImpl();

    switch (this.type) {
      case ModuleType.AUTHENTICATION:
        if (this.dataRequirements.length === 0) {
          result.addWarning(
            'Authentication module should have user data requirements',
            'dataRequirements',
            'MISSING_DATA_REQUIREMENTS'
          );
        }
        break;

      case ModuleType.UI_COMPONENT:
        if (this.uiComponents.length === 0) {
          result.addError(
            'UI component module must contain UI components',
            'uiComponents',
            'MISSING_UI_COMPONENTS'
          );
        }
        break;

      case ModuleType.API_LAYER:
        if (this.dataRequirements.length === 0) {
          result.addWarning(
            'API layer should define data requirements',
            'dataRequirements',
            'MISSING_DATA_REQUIREMENTS'
          );
        }
        break;
    }

    return result;
  }

  /**
   * 获取所有UI组件（包括子模块的）
   */
  getAllUIComponents(): UIComponent[] {
    const components = [...this.uiComponents];
    for (const subModule of this.subModules) {
      if (subModule instanceof FunctionalModuleImpl) {
        components.push(...subModule.getAllUIComponents());
      }
    }
    return components;
  }

  /**
   * 获取所有数据需求（包括子模块的）
   */
  getAllDataRequirements(): DataRequirement[] {
    const requirements = [...this.dataRequirements];
    for (const subModule of this.subModules) {
      if (subModule instanceof FunctionalModuleImpl) {
        requirements.push(...subModule.getAllDataRequirements());
      }
    }
    return requirements;
  }

  /**
   * 计算总工作量（包括子模块）
   */
  getTotalEffort(): number {
    let total = this.estimatedEffort;
    for (const subModule of this.subModules) {
      if (subModule instanceof FunctionalModuleImpl) {
        total += subModule.getTotalEffort();
      }
    }
    return total;
  }

  /**
   * 序列化为JSON
   */
  toJSON(): Record<string, any> {
    return {
      id: this.id,
      name: this.name,
      description: this.description,
      createdAt: this.createdAt.toISOString(),
      updatedAt: this.updatedAt.toISOString(),
      version: this.version,
      type: this.type,
      subModules: this.subModules.map((m) =>
        m instanceof FunctionalModuleImpl ? m.toJSON() : m
      ),
      uiComponents: this.uiComponents.map((c) => c.toJSON()),
      dataRequirements: this.dataRequirements,
      businessRules: this.businessRules,
      status: this.status,
      dependencies: this.dependencies,
      estimatedEffort: this.estimatedEffort,
    };
  }

  /**
   * 从JSON反序列化
   */
  static fromJSON(json: Record<string, any>): FunctionalModuleImpl {
    const module = new FunctionalModuleImpl({
      ...json,
      createdAt: new Date(json.createdAt),
      updatedAt: new Date(json.updatedAt),
      subModules:
        json.subModules?.map((m: any) => FunctionalModuleImpl.fromJSON(m)) ||
        [],
      uiComponents:
        json.uiComponents?.map((c: any) => UIComponent.fromJSON(c)) || [],
    });
    return module;
  }

  /**
   * 更新时间戳
   */
  private touch(): void {
    this.updatedAt = new Date();
  }

  /**
   * 生成唯一ID
   */
  private generateId(): string {
    return `module_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

/**
 * 蓝图类
 * 项目的核心"宪法"文档，定义整个项目的结构和规范
 */
export class Blueprint implements BaseEntity {
  public id: string;
  public name: string;
  public description?: string;
  public createdAt: Date;
  public updatedAt: Date;
  public version: string;
  public modules: FunctionalModule[];
  public uiComponents: UIComponent[];
  public dataEntities: DataEntity[];
  public businessRules: BusinessRule[];
  public relationships: Relationship[];
  public metadata: BlueprintMetadata;

  constructor(data: Partial<Blueprint> = {}) {
    this.id = data.id || this.generateId();
    this.name = data.name || '';
    this.description = data.description;
    this.createdAt = data.createdAt || new Date();
    this.updatedAt = data.updatedAt || new Date();
    this.version = data.version || '1.0.0';
    this.modules = data.modules || [];
    this.uiComponents = data.uiComponents || [];
    this.dataEntities = data.dataEntities || [];
    this.businessRules = data.businessRules || [];
    this.relationships = data.relationships || [];
    this.metadata = data.metadata || this.createDefaultMetadata();
  }

  /**
   * 添加模块
   */
  addModule(module: FunctionalModule): void {
    this.modules.push(module);
    this.touch();
  }

  /**
   * 删除模块
   */
  removeModule(moduleId: string): boolean {
    const initialLength = this.modules.length;
    this.modules = this.modules.filter((m) => m.id !== moduleId);
    if (this.modules.length < initialLength) {
      this.touch();
      return true;
    }
    return false;
  }

  /**
   * 根据ID查找模块
   */
  getModuleById(moduleId: string): FunctionalModule | undefined {
    return this.findModuleRecursive(this.modules, moduleId);
  }

  /**
   * 递归查找模块
   */
  private findModuleRecursive(
    modules: FunctionalModule[],
    moduleId: string
  ): FunctionalModule | undefined {
    for (const module of modules) {
      if (module.id === moduleId) {
        return module;
      }
      const found = this.findModuleRecursive(module.subModules, moduleId);
      if (found) {
        return found;
      }
    }
    return undefined;
  }

  /**
   * 添加数据实体
   */
  addDataEntity(entity: DataEntity): void {
    this.dataEntities.push(entity);
    this.touch();
  }

  /**
   * 删除数据实体
   */
  removeDataEntity(entityId: string): boolean {
    const initialLength = this.dataEntities.length;
    this.dataEntities = this.dataEntities.filter((e) => e.id !== entityId);
    if (this.dataEntities.length < initialLength) {
      this.touch();
      return true;
    }
    return false;
  }

  /**
   * 根据ID查找数据实体
   */
  getDataEntityById(entityId: string): DataEntity | undefined {
    return this.dataEntities.find((e) => e.id === entityId);
  }

  /**
   * 添加业务规则
   */
  addBusinessRule(rule: BusinessRule): void {
    this.businessRules.push(rule);
    this.touch();
  }

  /**
   * 删除业务规则
   */
  removeBusinessRule(ruleId: string): boolean {
    const initialLength = this.businessRules.length;
    this.businessRules = this.businessRules.filter((r) => r.id !== ruleId);
    if (this.businessRules.length < initialLength) {
      this.touch();
      return true;
    }
    return false;
  }

  /**
   * 根据ID查找业务规则
   */
  getBusinessRuleById(ruleId: string): BusinessRule | undefined {
    return this.businessRules.find((r) => r.id === ruleId);
  }

  /**
   * 添加关系
   */
  addRelationship(relationship: Relationship): void {
    this.relationships.push(relationship);
    this.touch();
  }

  /**
   * 删除关系
   */
  removeRelationship(relationshipId: string): boolean {
    const initialLength = this.relationships.length;
    this.relationships = this.relationships.filter(
      (r) => r.id !== relationshipId
    );
    if (this.relationships.length < initialLength) {
      this.touch();
      return true;
    }
    return false;
  }

  /**
   * 验证蓝图的完整性
   */
  validate(): ValidationResult {
    const result = new ValidationResultImpl();

    // 验证基本信息
    if (!this.name || this.name.trim() === '') {
      result.addError('Blueprint name is required', 'name', 'REQUIRED');
    }

    // 验证模块
    if (this.modules.length === 0) {
      result.addWarning(
        'Blueprint has no modules defined',
        'modules',
        'EMPTY_MODULES'
      );
    }

    for (const module of this.modules) {
      if (module instanceof FunctionalModuleImpl) {
        const moduleValidation = module.validate();
        result.merge(moduleValidation);
      }
    }

    // 验证数据实体
    for (const entity of this.dataEntities) {
      const entityValidation = entity.validate();
      result.merge(entityValidation);
    }

    // 验证业务规则
    for (const rule of this.businessRules) {
      const ruleValidation = rule.validate();
      result.merge(ruleValidation);
    }

    // 验证业务规则之间的冲突
    const conflictValidation = this.validateRuleConflicts();
    result.merge(conflictValidation);

    // 验证关系的完整性
    const relationshipValidation = this.validateRelationships();
    result.merge(relationshipValidation);

    return result;
  }

  /**
   * 验证业务规则冲突
   */
  private validateRuleConflicts(): ValidationResult {
    const result = new ValidationResultImpl();

    for (const rule of this.businessRules) {
      const conflicts = rule.detectConflicts(this.businessRules);
      for (const conflict of conflicts) {
        result.addError(
          conflict.description,
          'businessRules',
          conflict.conflictType
        );
      }
    }

    return result;
  }

  /**
   * 验证关系
   */
  private validateRelationships(): ValidationResult {
    const result = new ValidationResultImpl();

    for (const relationship of this.relationships) {
      // 验证关系的源和目标是否存在
      const sourceExists = this.entityExists(relationship.sourceId);
      const targetExists = this.entityExists(relationship.targetId);

      if (!sourceExists) {
        result.addError(
          `Relationship source ${relationship.sourceId} does not exist`,
          'relationships',
          'INVALID_SOURCE'
        );
      }

      if (!targetExists) {
        result.addError(
          `Relationship target ${relationship.targetId} does not exist`,
          'relationships',
          'INVALID_TARGET'
        );
      }
    }

    return result;
  }

  /**
   * 检查实体是否存在
   */
  private entityExists(entityId: string): boolean {
    return (
      this.modules.some((m) => m.id === entityId) ||
      this.dataEntities.some((e) => e.id === entityId) ||
      this.businessRules.some((r) => r.id === entityId) ||
      this.uiComponents.some((c) => c.id === entityId)
    );
  }

  /**
   * 获取所有UI组件（包括模块中的）
   */
  getAllUIComponents(): UIComponent[] {
    const components = [...this.uiComponents];
    for (const module of this.modules) {
      if (module instanceof FunctionalModuleImpl) {
        components.push(...module.getAllUIComponents());
      }
    }
    return components;
  }

  /**
   * 获取所有数据需求
   */
  getAllDataRequirements(): DataRequirement[] {
    const requirements: DataRequirement[] = [];
    for (const module of this.modules) {
      if (module instanceof FunctionalModuleImpl) {
        requirements.push(...module.getAllDataRequirements());
      }
    }
    return requirements;
  }

  /**
   * 计算项目总工作量
   */
  getTotalEffort(): number {
    return this.modules.reduce((total, module) => {
      if (module instanceof FunctionalModuleImpl) {
        return total + module.getTotalEffort();
      }
      return total;
    }, 0);
  }

  /**
   * 创建默认元数据
   */
  private createDefaultMetadata(): BlueprintMetadata {
    return {
      author: 'Unknown',
      tags: [],
      customProperties: {},
      projectType: this.metadata?.projectType || ('WEB_APPLICATION' as any),
      targetLanguages: [],
      estimatedComplexity: 'MEDIUM',
      dependencies: [],
    };
  }

  /**
   * 序列化为JSON
   */
  toJSON(): Record<string, any> {
    return {
      id: this.id,
      name: this.name,
      description: this.description,
      createdAt: this.createdAt.toISOString(),
      updatedAt: this.updatedAt.toISOString(),
      version: this.version,
      modules: this.modules.map((m) =>
        m instanceof FunctionalModuleImpl ? m.toJSON() : m
      ),
      uiComponents: this.uiComponents.map((c) => c.toJSON()),
      dataEntities: this.dataEntities.map((e) => e.toJSON()),
      businessRules: this.businessRules.map((r) => r.toJSON()),
      relationships: this.relationships,
      metadata: this.metadata,
    };
  }

  /**
   * 从JSON反序列化
   */
  static fromJSON(json: Record<string, any>): Blueprint {
    return new Blueprint({
      ...json,
      createdAt: new Date(json.createdAt),
      updatedAt: new Date(json.updatedAt),
      modules:
        json.modules?.map((m: any) => FunctionalModuleImpl.fromJSON(m)) || [],
      uiComponents:
        json.uiComponents?.map((c: any) => UIComponent.fromJSON(c)) || [],
      dataEntities:
        json.dataEntities?.map((e: any) => DataEntity.fromJSON(e)) || [],
      businessRules:
        json.businessRules?.map((r: any) => BusinessRule.fromJSON(r)) || [],
    });
  }

  /**
   * 克隆蓝图
   */
  clone(): Blueprint {
    return Blueprint.fromJSON(this.toJSON());
  }

  /**
   * 更新时间戳
   */
  private touch(): void {
    this.updatedAt = new Date();
  }

  /**
   * 生成唯一ID
   */
  private generateId(): string {
    return `blueprint_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

// 导出FunctionalModule接口（避免重复导出）
// export type { FunctionalModule };
