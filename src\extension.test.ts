import { describe, it, expect, vi } from 'vitest';

// Mock vscode module
vi.mock('vscode', () => ({
  commands: {
    registerCommand: vi.fn(),
  },
  window: {
    createTreeView: vi.fn(),
    createWebviewPanel: vi.fn(),
    showInformationMessage: vi.fn(),
  },
  ViewColumn: {
    One: 1,
  },
  TreeItemCollapsibleState: {
    None: 0,
    Collapsed: 1,
    Expanded: 2,
  },
  TreeItem: class MockTreeItem {
    label: string;
    collapsibleState?: number;
    constructor(label: string, collapsibleState?: number) {
      this.label = label;
      this.collapsibleState = collapsibleState;
    }
  },
  EventEmitter: vi.fn(() => ({
    event: vi.fn(),
    fire: vi.fn(),
    dispose: vi.fn(),
  })),
  Uri: {
    joinPath: vi.fn(),
  },
}));

describe('Extension', () => {
  it('should be defined', () => {
    expect(true).toBe(true);
  });

  it('should have activate function', async () => {
    const { activate } = await import('./extension');
    expect(typeof activate).toBe('function');
  });

  it('should have deactivate function', async () => {
    const { deactivate } = await import('./extension');
    expect(typeof deactivate).toBe('function');
  });
});
