import * as vscode from 'vscode';
import * as path from 'path';

/**
 * 文件变更事件接口
 */
export interface FileChangeEvent {
  type: 'created' | 'changed' | 'deleted';
  uri: vscode.Uri;
  timestamp: number;
  relativePath: string;
  fileType: 'code' | 'config' | 'other';
  language?: string;
}

/**
 * 文件系统监听器配置
 */
export interface FileWatcherConfig {
  enableCodeFileWatching: boolean;
  enableConfigFileWatching: boolean;
  watchPatterns: string[];
  excludePatterns: string[];
  debounceDelay: number;
  maxEventsPerSecond: number;
}

/**
 * 文件系统监听器
 * 监听项目文件变更并触发分析引擎更新
 */
export class FileSystemWatcher {
  private watchers: vscode.FileSystemWatcher[] = [];
  private config: FileWatcherConfig;
  private eventHandlers: ((event: FileChangeEvent) => void)[] = [];
  private eventQueue: FileChangeEvent[] = [];
  private debounceTimer: NodeJS.Timeout | null = null;
  private lastEventTime = 0;
  private eventCount = 0;

  constructor(config: Partial<FileWatcherConfig> = {}) {
    this.config = {
      enableCodeFileWatching: true,
      enableConfigFileWatching: true,
      watchPatterns: [
        '**/*.{ts,js,tsx,jsx,py,java,cs,cpp,c,h,hpp}', // 代码文件
        '**/*.{json,yaml,yml,xml,toml,ini}', // 配置文件
        '**/package.json',
        '**/tsconfig.json',
        '**/webpack.config.js',
        '**/.env*'
      ],
      excludePatterns: [
        '**/node_modules/**',
        '**/dist/**',
        '**/out/**',
        '**/build/**',
        '**/.git/**',
        '**/.vscode/**',
        '**/coverage/**',
        '**/*.log'
      ],
      debounceDelay: 500,
      maxEventsPerSecond: 10,
      ...config
    };
  }

  /**
   * 启动文件系统监听
   */
  public start(): void {
    this.stop(); // 先停止现有的监听器

    if (this.config.enableCodeFileWatching || this.config.enableConfigFileWatching) {
      this.setupWatchers();
    }

    console.log('🔍 FileSystemWatcher started with config:', this.config);
  }

  /**
   * 停止文件系统监听
   */
  public stop(): void {
    this.watchers.forEach(watcher => watcher.dispose());
    this.watchers = [];

    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
      this.debounceTimer = null;
    }

    console.log('🛑 FileSystemWatcher stopped');
  }

  /**
   * 添加事件处理器
   */
  public onFileChange(handler: (event: FileChangeEvent) => void): vscode.Disposable {
    this.eventHandlers.push(handler);

    return new vscode.Disposable(() => {
      const index = this.eventHandlers.indexOf(handler);
      if (index > -1) {
        this.eventHandlers.splice(index, 1);
      }
    });
  }

  /**
   * 设置监听器
   */
  private setupWatchers(): void {
    this.config.watchPatterns.forEach(pattern => {
      const watcher = vscode.workspace.createFileSystemWatcher(
        new vscode.RelativePattern(vscode.workspace.workspaceFolders![0], pattern)
      );

      // 文件创建
      watcher.onDidCreate(uri => {
        this.handleFileEvent('created', uri);
      });

      // 文件修改
      watcher.onDidChange(uri => {
        this.handleFileEvent('changed', uri);
      });

      // 文件删除
      watcher.onDidDelete(uri => {
        this.handleFileEvent('deleted', uri);
      });

      this.watchers.push(watcher);
    });
  }

  /**
   * 处理文件事件
   */
  private handleFileEvent(type: 'created' | 'changed' | 'deleted', uri: vscode.Uri): void {
    // 检查是否应该忽略此文件
    if (this.shouldIgnoreFile(uri)) {
      return;
    }

    // 限流检查
    if (!this.checkRateLimit()) {
      console.warn('⚠️ File event rate limit exceeded, skipping event');
      return;
    }

    const workspaceFolder = vscode.workspace.getWorkspaceFolder(uri);
    if (!workspaceFolder) {
      return;
    }

    const relativePath = path.relative(workspaceFolder.uri.fsPath, uri.fsPath);
    const fileType = this.getFileType(uri);
    const language = this.getLanguageFromFile(uri);

    const event: FileChangeEvent = {
      type,
      uri,
      timestamp: Date.now(),
      relativePath,
      fileType,
      language
    };

    this.queueEvent(event);
  }

  /**
   * 检查是否应该忽略文件
   */
  private shouldIgnoreFile(uri: vscode.Uri): boolean {
    const relativePath = vscode.workspace.asRelativePath(uri);

    // 检查排除模式
    for (const pattern of this.config.excludePatterns) {
      const glob = new vscode.RelativePattern(
        vscode.workspace.workspaceFolders![0],
        pattern
      );
      
      // 简化的模式匹配
      const relativePathNormalized = relativePath.replace(/\\/g, '/');
      const patternNormalized = pattern.replace(/\*\*/g, '.*').replace(/\*/g, '[^/]*');
      const regex = new RegExp(patternNormalized);
      
      if (regex.test(relativePathNormalized)) {
        return true;
      }
    }

    return false;
  }

  /**
   * 检查事件频率限制
   */
  private checkRateLimit(): boolean {
    const now = Date.now();
    
    // 重置计数器（每秒）
    if (now - this.lastEventTime > 1000) {
      this.eventCount = 0;
      this.lastEventTime = now;
    }

    this.eventCount++;
    return this.eventCount <= this.config.maxEventsPerSecond;
  }

  /**
   * 将事件加入队列并防抖处理
   */
  private queueEvent(event: FileChangeEvent): void {
    this.eventQueue.push(event);

    // 防抖处理
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
    }

    this.debounceTimer = setTimeout(() => {
      this.processEventQueue();
    }, this.config.debounceDelay);
  }

  /**
   * 处理事件队列
   */
  private processEventQueue(): void {
    if (this.eventQueue.length === 0) {
      return;
    }

    // 合并相同文件的事件
    const mergedEvents = this.mergeEvents(this.eventQueue);
    
    // 触发事件处理器
    mergedEvents.forEach(event => {
      this.eventHandlers.forEach(handler => {
        try {
          handler(event);
        } catch (error) {
          console.error('Error in file change handler:', error);
        }
      });
    });

    // 清空队列
    this.eventQueue = [];
    this.debounceTimer = null;

    console.log(`📁 Processed ${mergedEvents.length} file change events`);
  }

  /**
   * 合并相同文件的事件
   */
  private mergeEvents(events: FileChangeEvent[]): FileChangeEvent[] {
    const eventMap = new Map<string, FileChangeEvent>();

    events.forEach(event => {
      const key = event.uri.toString();
      const existing = eventMap.get(key);

      if (!existing) {
        eventMap.set(key, event);
      } else {
        // 保留最新的事件类型和时间戳
        existing.type = event.type;
        existing.timestamp = event.timestamp;
      }
    });

    return Array.from(eventMap.values());
  }

  /**
   * 获取文件类型
   */
  private getFileType(uri: vscode.Uri): 'code' | 'config' | 'other' {
    const fileName = path.basename(uri.fsPath);
    const extension = path.extname(uri.fsPath).toLowerCase();

    // 配置文件
    const configFiles = [
      'package.json', 'tsconfig.json', 'webpack.config.js', 
      'babel.config.js', '.eslintrc', '.prettierrc'
    ];
    
    const configExtensions = ['.json', '.yaml', '.yml', '.xml', '.toml', '.ini'];

    if (configFiles.includes(fileName) || 
        configExtensions.includes(extension) ||
        fileName.startsWith('.env')) {
      return 'config';
    }

    // 代码文件
    const codeExtensions = [
      '.ts', '.js', '.tsx', '.jsx', '.py', '.java', '.cs', 
      '.cpp', '.c', '.h', '.hpp', '.go', '.rs', '.php'
    ];

    if (codeExtensions.includes(extension)) {
      return 'code';
    }

    return 'other';
  }

  /**
   * 从文件获取语言标识
   */
  private getLanguageFromFile(uri: vscode.Uri): string | undefined {
    const extension = path.extname(uri.fsPath).toLowerCase();
    
    const languageMap: { [key: string]: string } = {
      '.ts': 'typescript',
      '.tsx': 'typescriptreact',
      '.js': 'javascript',
      '.jsx': 'javascriptreact',
      '.py': 'python',
      '.java': 'java',
      '.cs': 'csharp',
      '.cpp': 'cpp',
      '.c': 'c',
      '.h': 'c',
      '.hpp': 'cpp',
      '.go': 'go',
      '.rs': 'rust',
      '.php': 'php',
      '.json': 'json',
      '.yaml': 'yaml',
      '.yml': 'yaml',
      '.xml': 'xml'
    };

    return languageMap[extension];
  }

  /**
   * 获取当前配置
   */
  public getConfig(): FileWatcherConfig {
    return { ...this.config };
  }

  /**
   * 更新配置
   */
  public updateConfig(newConfig: Partial<FileWatcherConfig>): void {
    this.config = { ...this.config, ...newConfig };
    
    // 重启监听器以应用新配置
    if (this.watchers.length > 0) {
      this.start();
    }
  }

  /**
   * 获取统计信息
   */
  public getStats(): {
    watcherCount: number;
    queuedEvents: number;
    totalHandlers: number;
    isActive: boolean;
  } {
    return {
      watcherCount: this.watchers.length,
      queuedEvents: this.eventQueue.length,
      totalHandlers: this.eventHandlers.length,
      isActive: this.watchers.length > 0
    };
  }
}