import * as d3 from 'd3';
import { Blueprint } from '../models/Blueprint';
import { DataEntity } from '../models/DataEntity';
import { BusinessRule } from '../models/BusinessRule';
import { UIComponent } from '../models/UIComponent';
import { NodeStatus, NodeType } from '../models/enums';

/**
 * 白板渲染引擎 - 基于D3.js的无边界画布渲染系统
 */
export class WhiteboardRenderer {
  private container: HTMLElement;
  private svg: d3.Selection<SVGSVGElement, unknown, null, undefined>;
  private g: d3.Selection<SVGGElement, unknown, null, undefined>;
  private zoom: d3.ZoomBehavior<SVGSVGElement, unknown>;
  private nodes: Map<string, WhiteboardNode> = new Map();
  private edges: Map<string, WhiteboardEdge> = new Map();
  private viewport: Viewport = { x: 0, y: 0, scale: 1 };
  private interactionMode: InteractionMode = 'SELECT';

  constructor(container: HTMLElement) {
    this.container = container;
    this.initializeCanvas();
  }

  /**
   * 初始化画布
   */
  private initializeCanvas(): void {
    // 创建SVG容器
    this.svg = d3.select(this.container)
      .append('svg')
      .attr('width', '100%')
      .attr('height', '100%')
      .style('background-color', 'var(--vscode-editor-background)')
      .style('cursor', 'grab');

    // 创建主绘图组
    this.g = this.svg.append('g');

    // 初始化缩放和平移
    this.zoom = d3.zoom<SVGSVGElement, unknown>()
      .scaleExtent([0.1, 5])
      .on('zoom', (event) => {
        this.g.attr('transform', event.transform);
        this.viewport = {
          x: event.transform.x,
          y: event.transform.y,
          scale: event.transform.k
        };
      });

    this.svg.call(this.zoom);

    // 添加网格背景
    this.addGridPattern();

    // 初始化图层
    this.initializeLayers();
  }

  /**
   * 添加网格背景
   */
  private addGridPattern(): void {
    const defs = this.svg.append('defs');
    
    const pattern = defs.append('pattern')
      .attr('id', 'grid')
      .attr('width', 20)
      .attr('height', 20)
      .attr('patternUnits', 'userSpaceOnUse');

    pattern.append('path')
      .attr('d', 'M 20 0 L 0 0 0 20')
      .attr('fill', 'none')
      .attr('stroke', 'var(--vscode-editorWidget-border)')
      .attr('stroke-width', 0.5)
      .attr('opacity', 0.3);

    // 添加网格背景矩形
    this.g.append('rect')
      .attr('width', '100%')
      .attr('height', '100%')
      .attr('fill', 'url(#grid)')
      .style('pointer-events', 'none');
  }

  /**
   * 初始化图层
   */
  private initializeLayers(): void {
    // 边线层（在节点下方）
    this.g.append('g').attr('class', 'edges-layer');
    
    // 节点层
    this.g.append('g').attr('class', 'nodes-layer');
    
    // 标签层（在最上方）
    this.g.append('g').attr('class', 'labels-layer');
  }

  /**
   * 渲染蓝图
   */
  public renderBlueprint(blueprint: Blueprint): void {
    this.clearCanvas();

    // 渲染数据实体
    blueprint.dataEntities.forEach(entity => {
      this.addNode({
        id: entity.id,
        type: 'DATA_ENTITY',
        position: this.getEntityPosition(entity),
        size: { width: 150, height: 80 },
        data: entity,
        status: 'BLUE',
        style: this.getNodeStyle('DATA_ENTITY', 'BLUE')
      });
    });

    // 渲染业务规则
    blueprint.businessRules.forEach(rule => {
      this.addNode({
        id: rule.id,
        type: 'BUSINESS_RULE',
        position: this.getRulePosition(rule),
        size: { width: 180, height: 100 },
        data: rule,
        status: 'BLUE',
        style: this.getNodeStyle('BUSINESS_RULE', 'BLUE')
      });
    });

    // 渲染UI组件
    if (blueprint.uiComponents) {
      blueprint.uiComponents.forEach(component => {
        this.addNode({
          id: component.id,
          type: 'UI_COMPONENT',
          position: this.getComponentPosition(component),
          size: { width: 120, height: 60 },
          data: component,
          status: 'BLUE',
          style: this.getNodeStyle('UI_COMPONENT', 'BLUE')
        });
      });
    }

    // 渲染关系连线
    this.renderRelationships(blueprint);

    // 自动布局
    this.autoLayout();
  }

  /**
   * 添加节点
   */
  public addNode(node: WhiteboardNode): void {
    this.nodes.set(node.id, node);
    this.renderNode(node);
  }

  /**
   * 渲染单个节点
   */
  private renderNode(node: WhiteboardNode): void {
    const nodesLayer = this.g.select('.nodes-layer');
    
    const nodeGroup = nodesLayer.append('g')
      .attr('class', 'node')
      .attr('id', `node-${node.id}`)
      .attr('transform', `translate(${node.position.x}, ${node.position.y})`);

    // 节点背景
    const rect = nodeGroup.append('rect')
      .attr('width', node.size.width)
      .attr('height', node.size.height)
      .attr('rx', 8)
      .attr('ry', 8)
      .style('fill', node.style.backgroundColor)
      .style('stroke', node.style.borderColor)
      .style('stroke-width', node.style.borderWidth)
      .style('filter', 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))');

    // 状态指示器
    nodeGroup.append('circle')
      .attr('cx', node.size.width - 10)
      .attr('cy', 10)
      .attr('r', 6)
      .style('fill', this.getStatusColor(node.status))
      .style('stroke', '#fff')
      .style('stroke-width', 2);

    // 节点图标
    const icon = this.getNodeIcon(node.type);
    nodeGroup.append('text')
      .attr('x', 12)
      .attr('y', 25)
      .attr('font-size', '16px')
      .text(icon);

    // 节点标题
    nodeGroup.append('text')
      .attr('x', 35)
      .attr('y', 25)
      .attr('font-size', '14px')
      .attr('font-weight', 'bold')
      .style('fill', node.style.textColor)
      .text(this.getNodeTitle(node));

    // 节点描述
    const description = this.getNodeDescription(node);
    if (description) {
      nodeGroup.append('text')
        .attr('x', 12)
        .attr('y', 45)
        .attr('font-size', '12px')
        .style('fill', node.style.textColor)
        .style('opacity', 0.8)
        .text(description);
    }

    // 添加交互事件
    this.addNodeInteractions(nodeGroup, node);
  }

  /**
   * 添加节点交互事件
   */
  private addNodeInteractions(nodeGroup: d3.Selection<SVGGElement, unknown, null, undefined>, node: WhiteboardNode): void {
    let isDragging = false;
    let dragStart = { x: 0, y: 0 };

    nodeGroup
      .style('cursor', 'pointer')
      .on('mousedown', (event) => {
        if (this.interactionMode === 'SELECT' || this.interactionMode === 'DRAG') {
          isDragging = true;
          dragStart = { x: event.x, y: event.y };
          event.stopPropagation();
        }
      })
      .on('mousemove', (event) => {
        if (isDragging) {
          const dx = event.x - dragStart.x;
          const dy = event.y - dragStart.y;
          
          node.position.x += dx;
          node.position.y += dy;
          
          nodeGroup.attr('transform', `translate(${node.position.x}, ${node.position.y})`);
          
          // 更新连接的边线
          this.updateConnectedEdges(node.id);
          
          dragStart = { x: event.x, y: event.y };
        }
      })
      .on('mouseup', () => {
        isDragging = false;
      })
      .on('click', (event) => {
        this.handleNodeClick(node.id);
        event.stopPropagation();
      })
      .on('dblclick', (event) => {
        this.handleNodeDoubleClick(node.id);
        event.stopPropagation();
      });
  }

  /**
   * 添加边线
   */
  public addEdge(edge: WhiteboardEdge): void {
    this.edges.set(edge.id, edge);
    this.renderEdge(edge);
  }

  /**
   * 渲染边线
   */
  private renderEdge(edge: WhiteboardEdge): void {
    const edgesLayer = this.g.select('.edges-layer');
    
    const sourceNode = this.nodes.get(edge.sourceId);
    const targetNode = this.nodes.get(edge.targetId);
    
    if (!sourceNode || !targetNode) {
      return;
    }

    const edgeGroup = edgesLayer.append('g')
      .attr('class', 'edge')
      .attr('id', `edge-${edge.id}`);

    // 计算连接点
    const sourcePoint = this.getConnectionPoint(sourceNode, targetNode);
    const targetPoint = this.getConnectionPoint(targetNode, sourceNode);

    // 绘制连线
    const path = edgeGroup.append('path')
      .attr('d', this.createEdgePath(sourcePoint, targetPoint, edge.type))
      .style('fill', 'none')
      .style('stroke', edge.style.color)
      .style('stroke-width', edge.style.width)
      .style('stroke-dasharray', edge.style.dashArray)
      .style('marker-end', 'url(#arrowhead)');

    // 添加箭头标记
    this.addArrowMarker();

    // 边线标签
    if (edge.label) {
      const midPoint = this.getMidPoint(sourcePoint, targetPoint);
      edgeGroup.append('text')
        .attr('x', midPoint.x)
        .attr('y', midPoint.y - 5)
        .attr('text-anchor', 'middle')
        .attr('font-size', '10px')
        .style('fill', edge.style.color)
        .text(edge.label);
    }
  }

  /**
   * 添加箭头标记
   */
  private addArrowMarker(): void {
    if (!this.svg.select('#arrowhead').empty()) {
      return;
    }

    const defs = this.svg.select('defs').empty() ? this.svg.append('defs') : this.svg.select('defs');
    
    defs.append('marker')
      .attr('id', 'arrowhead')
      .attr('viewBox', '0 -5 10 10')
      .attr('refX', 8)
      .attr('refY', 0)
      .attr('markerWidth', 6)
      .attr('markerHeight', 6)
      .attr('orient', 'auto')
      .append('path')
      .attr('d', 'M0,-5L10,0L0,5')
      .style('fill', 'var(--vscode-editor-foreground)');
  }

  /**
   * 创建边线路径
   */
  private createEdgePath(source: Position, target: Position, type: EdgeType): string {
    switch (type) {
      case 'STRAIGHT':
        return `M ${source.x} ${source.y} L ${target.x} ${target.y}`;
      
      case 'CURVED':
        const dx = target.x - source.x;
        const dy = target.y - source.y;
        const dr = Math.sqrt(dx * dx + dy * dy);
        return `M ${source.x} ${source.y} A ${dr} ${dr} 0 0 1 ${target.x} ${target.y}`;
      
      case 'ORTHOGONAL':
        const midX = (source.x + target.x) / 2;
        return `M ${source.x} ${source.y} L ${midX} ${source.y} L ${midX} ${target.y} L ${target.x} ${target.y}`;
      
      default:
        return `M ${source.x} ${source.y} L ${target.x} ${target.y}`;
    }
  }

  /**
   * 获取连接点
   */
  private getConnectionPoint(fromNode: WhiteboardNode, toNode: WhiteboardNode): Position {
    const fromCenter = {
      x: fromNode.position.x + fromNode.size.width / 2,
      y: fromNode.position.y + fromNode.size.height / 2
    };
    
    const toCenter = {
      x: toNode.position.x + toNode.size.width / 2,
      y: toNode.position.y + toNode.size.height / 2
    };

    // 计算方向向量
    const dx = toCenter.x - fromCenter.x;
    const dy = toCenter.y - fromCenter.y;
    const length = Math.sqrt(dx * dx + dy * dy);
    
    if (length === 0) return fromCenter;

    const unitX = dx / length;
    const unitY = dy / length;

    // 计算边界交点
    const halfWidth = fromNode.size.width / 2;
    const halfHeight = fromNode.size.height / 2;

    let intersectX, intersectY;

    if (Math.abs(unitX) * halfHeight > Math.abs(unitY) * halfWidth) {
      // 与左右边相交
      intersectX = fromCenter.x + (unitX > 0 ? halfWidth : -halfWidth);
      intersectY = fromCenter.y + (intersectX - fromCenter.x) * unitY / unitX;
    } else {
      // 与上下边相交
      intersectY = fromCenter.y + (unitY > 0 ? halfHeight : -halfHeight);
      intersectX = fromCenter.x + (intersectY - fromCenter.y) * unitX / unitY;
    }

    return { x: intersectX, y: intersectY };
  }

  /**
   * 获取中点
   */
  private getMidPoint(source: Position, target: Position): Position {
    return {
      x: (source.x + target.x) / 2,
      y: (source.y + target.y) / 2
    };
  }

  /**
   * 更新连接的边线
   */
  private updateConnectedEdges(nodeId: string): void {
    this.edges.forEach((edge, edgeId) => {
      if (edge.sourceId === nodeId || edge.targetId === nodeId) {
        // 重新渲染边线
        this.g.select(`#edge-${edgeId}`).remove();
        this.renderEdge(edge);
      }
    });
  }

  /**
   * 自动布局
   */
  public autoLayout(): void {
    if (this.nodes.size === 0) return;

    // 使用力导向布局算法
    const simulation = d3.forceSimulation(Array.from(this.nodes.values()))
      .force('link', d3.forceLink(Array.from(this.edges.values()))
        .id((d: any) => d.id)
        .distance(150))
      .force('charge', d3.forceManyBody().strength(-300))
      .force('center', d3.forceCenter(400, 300))
      .force('collision', d3.forceCollide().radius(80));

    simulation.on('tick', () => {
      // 更新节点位置
      this.nodes.forEach((node, nodeId) => {
        const nodeElement = this.g.select(`#node-${nodeId}`);
        nodeElement.attr('transform', `translate(${node.position.x}, ${node.position.y})`);
      });

      // 更新边线
      this.edges.forEach((edge, edgeId) => {
        const edgeElement = this.g.select(`#edge-${edgeId}`);
        const sourceNode = this.nodes.get(edge.sourceId);
        const targetNode = this.nodes.get(edge.targetId);
        
        if (sourceNode && targetNode) {
          const sourcePoint = this.getConnectionPoint(sourceNode, targetNode);
          const targetPoint = this.getConnectionPoint(targetNode, sourceNode);
          const path = this.createEdgePath(sourcePoint, targetPoint, edge.type);
          edgeElement.select('path').attr('d', path);
        }
      });
    });

    // 运行布局算法
    simulation.alpha(1).restart();
  }

  /**
   * 清空画布
   */
  public clearCanvas(): void {
    this.g.select('.nodes-layer').selectAll('*').remove();
    this.g.select('.edges-layer').selectAll('*').remove();
    this.g.select('.labels-layer').selectAll('*').remove();
    this.nodes.clear();
    this.edges.clear();
  }

  /**
   * 处理节点点击
   */
  private handleNodeClick(nodeId: string): void {
    // 触发节点点击事件
    this.container.dispatchEvent(new CustomEvent('nodeClick', {
      detail: { nodeId }
    }));
  }

  /**
   * 处理节点双击
   */
  private handleNodeDoubleClick(nodeId: string): void {
    // 触发节点双击事件
    this.container.dispatchEvent(new CustomEvent('nodeDoubleClick', {
      detail: { nodeId }
    }));
  }

  // 辅助方法
  private getEntityPosition(entity: DataEntity): Position {
    return { x: Math.random() * 400 + 100, y: Math.random() * 300 + 100 };
  }

  private getRulePosition(rule: BusinessRule): Position {
    return { x: Math.random() * 400 + 100, y: Math.random() * 300 + 100 };
  }

  private getComponentPosition(component: UIComponent): Position {
    return { x: Math.random() * 400 + 100, y: Math.random() * 300 + 100 };
  }



  private getStatusColor(status: NodeStatus): string {
    switch (status) {
      case 'GREEN': return '#28a745';
      case 'YELLOW': return '#ffc107';
      case 'RED': return '#dc3545';
      case 'PURPLE': return '#6f42c1';
      case 'BLUE': return '#007bff';
      default: return '#6c757d';
    }
  }

  private getNodeIcon(type: NodeType): string {
    switch (type) {
      case 'DATA_ENTITY': return '📊';
      case 'BUSINESS_RULE': return '⚖️';
      case 'UI_COMPONENT': return '🖼️';
      case 'FUNCTIONAL_MODULE': return '📦';
      default: return '📝';
    }
  }

  private getNodeTitle(node: WhiteboardNode): string {
    if (node.data) {
      return node.data.name || node.data.title || `Node ${node.id}`;
    }
    return `Node ${node.id}`;
  }

  private getNodeDescription(node: WhiteboardNode): string {
    if (node.data && node.data.description) {
      return node.data.description.length > 30 
        ? node.data.description.substring(0, 30) + '...'
        : node.data.description;
    }
    return '';
  }

  private renderRelationships(blueprint: Blueprint): void {
    // 渲染数据实体之间的关系
    blueprint.dataEntities.forEach(entity => {
      if (entity.relationships) {
        entity.relationships.forEach(rel => {
          this.addEdge({
            id: `${entity.id}-${rel.targetEntityId}`,
            sourceId: entity.id,
            targetId: rel.targetEntityId,
            type: 'STRAIGHT',
            label: rel.type,
            style: {
              color: 'var(--vscode-editor-foreground)',
              width: 2,
              dashArray: rel.type === 'OPTIONAL' ? '5,5' : 'none'
            }
          });
        });
      }
    });
  }

  /**
   * 设置交互模式
   */
  public setInteractionMode(mode: InteractionMode): void {
    this.interactionMode = mode;
  }

  /**
   * 获取当前视口
   */
  public getViewport(): Viewport {
    return this.viewport;
  }

  /**
   * 设置视口
   */
  public setViewport(viewport: Viewport): void {
    this.viewport = viewport;
    this.svg.call(
      this.zoom.transform,
      d3.zoomIdentity.translate(viewport.x, viewport.y).scale(viewport.scale)
    );
  }

  /**
   * 获取节点样式（公共方法）
   */
  public getNodeStyle(type: NodeType, status: NodeStatus): NodeStyle {
    const baseStyle = {
      backgroundColor: 'var(--vscode-button-background)',
      borderColor: 'var(--vscode-button-border)',
      borderWidth: 2,
      textColor: 'var(--vscode-button-foreground)'
    };

    // 根据状态调整颜色
    switch (status) {
      case 'GREEN':
        baseStyle.borderColor = '#28a745';
        break;
      case 'YELLOW':
        baseStyle.borderColor = '#ffc107';
        break;
      case 'RED':
        baseStyle.borderColor = '#dc3545';
        break;
      case 'PURPLE':
        baseStyle.borderColor = '#6f42c1';
        break;
      case 'BLUE':
        baseStyle.borderColor = '#007bff';
        break;
    }

    return baseStyle;
  }
}

// 类型定义
export interface WhiteboardNode {
  id: string;
  type: NodeType;
  position: Position;
  size: Size;
  data: any;
  status: NodeStatus;
  style: NodeStyle;
}

export interface WhiteboardEdge {
  id: string;
  sourceId: string;
  targetId: string;
  type: EdgeType;
  label?: string;
  style: EdgeStyle;
}

export interface Position {
  x: number;
  y: number;
}

export interface Size {
  width: number;
  height: number;
}

export interface Viewport {
  x: number;
  y: number;
  scale: number;
}

export interface NodeStyle {
  backgroundColor: string;
  borderColor: string;
  borderWidth: number;
  textColor: string;
}

export interface EdgeStyle {
  color: string;
  width: number;
  dashArray: string;
}

export type InteractionMode = 'SELECT' | 'DRAG' | 'CONNECT' | 'CREATE';
export type EdgeType = 'STRAIGHT' | 'CURVED' | 'ORTHOGONAL';