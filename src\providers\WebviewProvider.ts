import * as vscode from 'vscode';
import * as path from 'path';

/**
 * Webview提供者 - 管理白板界面的Webview容器
 */
export class WebviewProvider {
  private panel: vscode.WebviewPanel | undefined;
  private context: vscode.ExtensionContext;

  constructor(context: vscode.ExtensionContext) {
    this.context = context;
  }

  /**
   * 创建或显示Webview面板
   */
  public createOrShow(): void {
    const column = vscode.window.activeTextEditor
      ? vscode.window.activeTextEditor.viewColumn
      : undefined;

    // 如果面板已存在，显示它
    if (this.panel) {
      this.panel.reveal(column);
      return;
    }

    // 创建新的Webview面板
    this.panel = vscode.window.createWebviewPanel(
      'visualIntentContract',
      'Visual Intent Contract Whiteboard',
      column || vscode.ViewColumn.One,
      {
        enableScripts: true,
        retainContextWhenHidden: true,
        localResourceRoots: [
          vscode.Uri.file(path.join(this.context.extensionPath, 'media')),
          vscode.Uri.file(path.join(this.context.extensionPath, 'out'))
        ]
      }
    );

    // 设置HTML内容
    this.panel.webview.html = this.getWebviewContent();

    // 处理面板关闭事件
    this.panel.onDidDispose(() => {
      this.panel = undefined;
    });

    // 处理来自Webview的消息
    this.panel.webview.onDidReceiveMessage(
      message => {
        this.handleWebviewMessage(message);
      },
      undefined,
      this.context.subscriptions
    );
  }

  /**
   * 发送消息到Webview
   */
  public postMessage(message: any): void {
    if (this.panel) {
      this.panel.webview.postMessage(message);
    }
  }

  /**
   * 处理来自Webview的消息
   */
  private handleWebviewMessage(message: any): void {
    switch (message.command) {
      case 'ready':
        // Webview已准备就绪
        this.postMessage({
          command: 'initialize',
          data: {
            theme: vscode.window.activeColorTheme.kind
          }
        });
        break;

      case 'createNode':
        // 创建新节点
        this.handleCreateNode(message.data);
        break;

      case 'updateNode':
        // 更新节点
        this.handleUpdateNode(message.data);
        break;

      case 'deleteNode':
        // 删除节点
        this.handleDeleteNode(message.data);
        break;

      case 'createConnection':
        // 创建连接
        this.handleCreateConnection(message.data);
        break;

      case 'error':
        // 错误处理
        vscode.window.showErrorMessage(`Webview Error: ${message.data.message}`);
        break;

      case 'saveBlueprint':
        // TODO: 实现蓝图保存逻辑
        console.log('Saving blueprint:', message.data);
        
        this.postMessage({
          command: 'blueprintSaved',
          data: {
            success: true
          }
        });
        break;

      case 'openFile':
        // 打开文件
        if (message.data.filePath) {
          vscode.workspace.openTextDocument(message.data.filePath).then(doc => {
            vscode.window.showTextDocument(doc);
          });
        }
        break;

      default:
        console.log('Unknown message from webview:', message);
    }
  }

  /**
   * 处理创建节点请求
   */
  private handleCreateNode(data: any): void {
    // TODO: 实现节点创建逻辑
    console.log('Creating node:', data);
    
    // 发送确认消息回Webview
    this.postMessage({
      command: 'nodeCreated',
      data: {
        id: data.id,
        success: true
      }
    });
  }

  /**
   * 处理更新节点请求
   */
  private handleUpdateNode(data: any): void {
    // TODO: 实现节点更新逻辑
    console.log('Updating node:', data);
    
    this.postMessage({
      command: 'nodeUpdated',
      data: {
        id: data.id,
        success: true
      }
    });
  }

  /**
   * 处理删除节点请求
   */
  private handleDeleteNode(data: any): void {
    // TODO: 实现节点删除逻辑
    console.log('Deleting node:', data);
    
    this.postMessage({
      command: 'nodeDeleted',
      data: {
        id: data.id,
        success: true
      }
    });
  }

  /**
   * 处理创建连接请求
   */
  private handleCreateConnection(data: any): void {
    // TODO: 实现连接创建逻辑
    console.log('Creating connection:', data);
    
    this.postMessage({
      command: 'connectionCreated',
      data: {
        id: data.id,
        success: true
      }
    });
  }

  /**
   * 获取Webview的HTML内容
   */
  private getWebviewContent(): string {
    // 获取D3.js的CDN链接
    const d3ScriptUri = 'https://d3js.org/d3.v7.min.js';
    
    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Visual Intent Contract Whiteboard</title>
    <script src="${d3ScriptUri}"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            background-color: var(--vscode-editor-background);
            color: var(--vscode-editor-foreground);
            font-family: var(--vscode-font-family);
            overflow: hidden;
        }
        
        #whiteboard {
            width: 100vw;
            height: 100vh;
            position: relative;
            overflow: hidden;
        }
        
        #whiteboard svg {
            width: 100%;
            height: 100%;
        }
        
        .node {
            position: absolute;
            background: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            border: 2px solid var(--vscode-button-border);
            border-radius: 8px;
            padding: 12px;
            min-width: 120px;
            min-height: 60px;
            cursor: move;
            user-select: none;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: all 0.2s ease;
        }
        
        .node:hover {
            transform: scale(1.02);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }
        
        .node.selected {
            border-color: var(--vscode-focusBorder);
            box-shadow: 0 0 0 2px var(--vscode-focusBorder);
        }
        
        .node-title {
            font-weight: bold;
            margin-bottom: 4px;
        }
        
        .node-content {
            font-size: 0.9em;
            opacity: 0.8;
        }
        
        .toolbar {
            position: fixed;
            top: 10px;
            left: 10px;
            background: var(--vscode-panel-background);
            border: 1px solid var(--vscode-panel-border);
            border-radius: 6px;
            padding: 8px;
            display: flex;
            gap: 8px;
            z-index: 1000;
        }
        
        .toolbar button {
            background: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            border: none;
            border-radius: 4px;
            padding: 6px 12px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .toolbar button:hover {
            background: var(--vscode-button-hoverBackground);
        }
        
        .status-bar {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: var(--vscode-statusBar-background);
            color: var(--vscode-statusBar-foreground);
            padding: 4px 12px;
            font-size: 12px;
            border-top: 1px solid var(--vscode-statusBar-border);
        }
        
        .loading {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
        }
        
        .spinner {
            border: 3px solid var(--vscode-progressBar-background);
            border-top: 3px solid var(--vscode-progressBar-foreground);
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="loading" id="loading">
        <div class="spinner"></div>
        <div>Loading Visual Intent Contract...</div>
    </div>
    
    <div id="whiteboard" style="display: none;">
        <div class="toolbar">
            <button onclick="createNode()">📝 Add Entity</button>
            <button onclick="createRule()">⚖️ Add Rule</button>
            <button onclick="createModule()">📦 Add Module</button>
            <button onclick="toggleMode()">🔄 Toggle Mode</button>
            <button onclick="saveBlueprint()">💾 Save</button>
        </div>
        
        <div class="status-bar">
            <span id="status">Ready - Visual Intent Contract Whiteboard</span>
        </div>
    </div>

    <script>
        // VS Code API
        const vscode = acquireVsCodeApi();
        
        // 全局状态
        let whiteboardRenderer = null;
        let nodes = [];
        let connections = [];
        let selectedNode = null;
        let isSimpleMode = true;
        
        // 初始化
        window.addEventListener('load', () => {
            setTimeout(() => {
                document.getElementById('loading').style.display = 'none';
                document.getElementById('whiteboard').style.display = 'block';
                
                // 初始化白板渲染器
                initializeWhiteboard();
                
                // 通知扩展Webview已准备就绪
                vscode.postMessage({
                    command: 'ready'
                });
                
                updateStatus('Whiteboard initialized');
            }, 1000);
        });
        
        // 初始化白板
        function initializeWhiteboard() {
            const container = document.getElementById('whiteboard');
            
            // 创建SVG容器
            const svg = d3.select(container)
                .append('svg')
                .attr('width', '100%')
                .attr('height', '100%')
                .style('background-color', 'var(--vscode-editor-background)');
            
            // 创建主绘图组
            const g = svg.append('g');
            
            // 初始化缩放和平移
            const zoom = d3.zoom()
                .scaleExtent([0.1, 5])
                .on('zoom', (event) => {
                    g.attr('transform', event.transform);
                });
            
            svg.call(zoom);
            
            // 添加网格背景
            addGridPattern(svg);
            
            // 初始化图层
            g.append('g').attr('class', 'edges-layer');
            g.append('g').attr('class', 'nodes-layer');
            g.append('g').attr('class', 'labels-layer');
            
            // 保存引用
            whiteboardRenderer = { svg, g, zoom };
            
            updateStatus('D3.js whiteboard initialized');
        }
        
        // 添加网格背景
        function addGridPattern(svg) {
            const defs = svg.append('defs');
            
            const pattern = defs.append('pattern')
                .attr('id', 'grid')
                .attr('width', 20)
                .attr('height', 20)
                .attr('patternUnits', 'userSpaceOnUse');
            
            pattern.append('path')
                .attr('d', 'M 20 0 L 0 0 0 20')
                .attr('fill', 'none')
                .attr('stroke', 'var(--vscode-editorWidget-border)')
                .attr('stroke-width', 0.5)
                .attr('opacity', 0.3);
            
            // 添加网格背景矩形
            svg.append('rect')
                .attr('width', '100%')
                .attr('height', '100%')
                .attr('fill', 'url(#grid)')
                .style('pointer-events', 'none');
        }
        
        // 监听来自扩展的消息
        window.addEventListener('message', event => {
            const message = event.data;
            
            switch (message.command) {
                case 'initialize':
                    handleInitialize(message.data);
                    break;
                case 'nodeCreated':
                    handleNodeCreated(message.data);
                    break;
                case 'nodeUpdated':
                    handleNodeUpdated(message.data);
                    break;
                case 'nodeDeleted':
                    handleNodeDeleted(message.data);
                    break;
                case 'connectionCreated':
                    handleConnectionCreated(message.data);
                    break;
            }
        });
        
        // 处理初始化
        function handleInitialize(data) {
            updateStatus('Connected to VS Code extension');
            
            // 根据主题调整样式
            if (data.theme === 1) { // Dark theme
                document.body.classList.add('dark-theme');
            }
        }
        
        // 创建节点
        function createNode(type = 'entity') {
            if (!whiteboardRenderer) return;
            
            const id = 'node_' + Date.now();
            const node = {
                id: id,
                type: type,
                x: Math.random() * 400 + 100,
                y: Math.random() * 300 + 100,
                title: type === 'entity' ? 'New Entity' : 'New Node',
                content: 'Click to edit...'
            };
            
            nodes.push(node);
            renderNodeD3(node);
            
            // 通知扩展
            vscode.postMessage({
                command: 'createNode',
                data: node
            });
            
            updateStatus(\`Created \${type} node: \${node.title}\`);
        }
        
        // 创建规则节点
        function createRule() {
            createNode('rule');
        }
        
        // 创建模块节点
        function createModule() {
            createNode('module');
        }
        
        // 使用D3渲染节点
        function renderNodeD3(node) {
            if (!whiteboardRenderer) return;
            
            const nodesLayer = whiteboardRenderer.g.select('.nodes-layer');
            
            const nodeGroup = nodesLayer.append('g')
                .attr('class', 'node')
                .attr('id', \`node-\${node.id}\`)
                .attr('transform', \`translate(\${node.x}, \${node.y})\`);
            
            // 节点背景
            nodeGroup.append('rect')
                .attr('width', 150)
                .attr('height', 80)
                .attr('rx', 8)
                .attr('ry', 8)
                .style('fill', 'var(--vscode-button-background)')
                .style('stroke', 'var(--vscode-button-border)')
                .style('stroke-width', 2)
                .style('filter', 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))');
            
            // 状态指示器
            nodeGroup.append('circle')
                .attr('cx', 140)
                .attr('cy', 10)
                .attr('r', 6)
                .style('fill', '#007bff')
                .style('stroke', '#fff')
                .style('stroke-width', 2);
            
            // 节点图标
            const icon = getNodeIcon(node.type);
            nodeGroup.append('text')
                .attr('x', 12)
                .attr('y', 25)
                .attr('font-size', '16px')
                .text(icon);
            
            // 节点标题
            nodeGroup.append('text')
                .attr('x', 35)
                .attr('y', 25)
                .attr('font-size', '14px')
                .attr('font-weight', 'bold')
                .style('fill', 'var(--vscode-button-foreground)')
                .text(node.title);
            
            // 节点描述
            nodeGroup.append('text')
                .attr('x', 12)
                .attr('y', 45)
                .attr('font-size', '12px')
                .style('fill', 'var(--vscode-button-foreground)')
                .style('opacity', 0.8)
                .text(node.content);
            
            // 添加交互事件
            addNodeInteractions(nodeGroup, node);
        }
        
        // 获取节点图标
        function getNodeIcon(type) {
            switch (type) {
                case 'entity': return '📊';
                case 'rule': return '⚖️';
                case 'module': return '📦';
                default: return '📝';
            }
        }
        
        // 添加节点交互事件
        function addNodeInteractions(nodeGroup, node) {
            let isDragging = false;
            let dragStart = { x: 0, y: 0 };
            
            nodeGroup
                .style('cursor', 'pointer')
                .on('mousedown', (event) => {
                    isDragging = true;
                    dragStart = { x: event.x, y: event.y };
                    event.stopPropagation();
                })
                .on('mousemove', (event) => {
                    if (isDragging) {
                        const dx = event.x - dragStart.x;
                        const dy = event.y - dragStart.y;
                        
                        node.x += dx;
                        node.y += dy;
                        
                        nodeGroup.attr('transform', \`translate(\${node.x}, \${node.y})\`);
                        
                        dragStart = { x: event.x, y: event.y };
                    }
                })
                .on('mouseup', () => {
                    isDragging = false;
                })
                .on('click', (event) => {
                    selectNodeD3(node);
                    event.stopPropagation();
                })
                .on('dblclick', (event) => {
                    editNodeD3(node);
                    event.stopPropagation();
                });
        }
        
        // 选择节点（D3版本）
        function selectNodeD3(node) {
            // 清除之前的选择
            whiteboardRenderer.g.selectAll('.node rect').style('stroke-width', 2);
            
            // 选择当前节点
            whiteboardRenderer.g.select(\`#node-\${node.id} rect\`).style('stroke-width', 4);
            
            selectedNode = node;
            updateStatus(\`Selected: \${node.title}\`);
        }
        
        // 编辑节点（D3版本）
        function editNodeD3(node) {
            const newTitle = prompt('Enter node title:', node.title);
            if (newTitle && newTitle !== node.title) {
                node.title = newTitle;
                
                // 更新显示
                whiteboardRenderer.g.select(\`#node-\${node.id}\`)
                    .select('text:nth-of-type(2)')
                    .text(newTitle);
                
                // 通知扩展
                vscode.postMessage({
                    command: 'updateNode',
                    data: node
                });
                
                updateStatus(\`Updated node: \${newTitle}\`);
            }
        }
        

        
        // 切换模式
        function toggleMode() {
            isSimpleMode = !isSimpleMode;
            updateStatus(\`Switched to \${isSimpleMode ? 'Simple' : 'Expert'} mode\`);
        }
        
        // 保存蓝图
        function saveBlueprint() {
            const blueprint = {
                nodes: nodes,
                connections: connections,
                mode: isSimpleMode ? 'simple' : 'expert',
                timestamp: new Date().toISOString()
            };
            
            vscode.postMessage({
                command: 'saveBlueprint',
                data: blueprint
            });
            
            updateStatus('Blueprint saved');
        }
        
        // 更新状态栏
        function updateStatus(message) {
            document.getElementById('status').textContent = message;
        }
        
        // 处理节点创建响应
        function handleNodeCreated(data) {
            if (data.success) {
                updateStatus(\`Node created successfully: \${data.id}\`);
            } else {
                updateStatus(\`Failed to create node: \${data.error}\`);
            }
        }
        
        // 处理节点更新响应
        function handleNodeUpdated(data) {
            if (data.success) {
                updateStatus(\`Node updated successfully: \${data.id}\`);
            } else {
                updateStatus(\`Failed to update node: \${data.error}\`);
            }
        }
        
        // 处理节点删除响应
        function handleNodeDeleted(data) {
            if (data.success) {
                // 从D3中移除节点
                if (whiteboardRenderer) {
                    whiteboardRenderer.g.select(\`#node-\${data.id}\`).remove();
                }
                
                // 从数组中移除节点
                nodes = nodes.filter(n => n.id !== data.id);
                
                updateStatus(\`Node deleted successfully: \${data.id}\`);
            } else {
                updateStatus(\`Failed to delete node: \${data.error}\`);
            }
        }
        
        // 处理连接创建响应
        function handleConnectionCreated(data) {
            if (data.success) {
                updateStatus(\`Connection created successfully: \${data.id}\`);
            } else {
                updateStatus(\`Failed to create connection: \${data.error}\`);
            }
        }
        
        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Delete' && selectedNode) {
                if (confirm(\`Delete node "\${selectedNode.title}"?\`)) {
                    vscode.postMessage({
                        command: 'deleteNode',
                        data: { id: selectedNode.id }
                    });
                }
            }
        });
        
        // 错误处理
        window.addEventListener('error', (e) => {
            vscode.postMessage({
                command: 'error',
                data: {
                    message: e.message,
                    filename: e.filename,
                    lineno: e.lineno
                }
            });
        });
    </script>
</body>
</html>`;
  }
}