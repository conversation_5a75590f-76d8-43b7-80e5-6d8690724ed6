/**
 * 项目类型枚举
 */
export enum ProjectType {
  WEB_APPLICATION = 'WEB_APPLICATION',
  MOBILE_APP = 'MOBILE_APP',
  DESKTOP_APP = 'DESKTOP_APP',
  API_SERVICE = 'API_SERVICE',
  LIBRARY = 'LIBRARY',
  MICROSERVICE = 'MICROSERVICE',
  BLOG_SYSTEM = 'BLOG_SYSTEM',
  ECOMMERCE = 'ECOMMERCE',
  ENTERPRISE_APP = 'ENTERPRISE_APP',
}

/**
 * 模块类型枚举
 */
export enum ModuleType {
  AUTHENTICATION = 'AUTHENTICATION',
  USER_MANAGEMENT = 'USER_MANAGEMENT',
  DATA_MANAGEMENT = 'DATA_MANAGEMENT',
  API_LAYER = 'API_LAYER',
  UI_COMPONENT = 'UI_COMPONENT',
  BUSINESS_LOGIC = 'BUSINESS_LOGIC',
  INTEGRATION = 'INTEGRATION',
  UTILITY = 'UTILITY',
}

/**
 * 模块状态枚举
 */
export enum ModuleStatus {
  PLANNED = 'PLANNED',
  IN_PROGRESS = 'IN_PROGRESS',
  IMPLEMENTED = 'IMPLEMENTED',
  TESTED = 'TESTED',
  DEPLOYED = 'DEPLOYED',
  DEPRECATED = 'DEPRECATED',
}

/**
 * UI组件类型枚举
 */
export enum ComponentType {
  BUTTON = 'BUTTON',
  INPUT = 'INPUT',
  SELECT = 'SELECT',
  CHECKBOX = 'CHECKBOX',
  RADIO = 'RADIO',
  TEXTAREA = 'TEXTAREA',
  TABLE = 'TABLE',
  FORM = 'FORM',
  MODAL = 'MODAL',
  NAVIGATION = 'NAVIGATION',
  LAYOUT = 'LAYOUT',
  CUSTOM = 'CUSTOM',
}

/**
 * 数据字段类型枚举
 */
export enum DataFieldType {
  STRING = 'STRING',
  NUMBER = 'NUMBER',
  BOOLEAN = 'BOOLEAN',
  DATE = 'DATE',
  DATETIME = 'DATETIME',
  EMAIL = 'EMAIL',
  URL = 'URL',
  JSON = 'JSON',
  ARRAY = 'ARRAY',
  OBJECT = 'OBJECT',
  ENUM = 'ENUM',
  REFERENCE = 'REFERENCE',
}

/**
 * 业务规则类型枚举
 */
export enum BusinessRuleType {
  VALIDATION = 'VALIDATION',
  CALCULATION = 'CALCULATION',
  WORKFLOW = 'WORKFLOW',
  AUTHORIZATION = 'AUTHORIZATION',
  NOTIFICATION = 'NOTIFICATION',
  INTEGRATION = 'INTEGRATION',
}

/**
 * 逻辑操作符枚举
 */
export enum LogicOperator {
  AND = 'AND',
  OR = 'OR',
  NOT = 'NOT',
  EQUALS = 'EQUALS',
  NOT_EQUALS = 'NOT_EQUALS',
  GREATER_THAN = 'GREATER_THAN',
  LESS_THAN = 'LESS_THAN',
  GREATER_EQUAL = 'GREATER_EQUAL',
  LESS_EQUAL = 'LESS_EQUAL',
  CONTAINS = 'CONTAINS',
  STARTS_WITH = 'STARTS_WITH',
  ENDS_WITH = 'ENDS_WITH',
  IN = 'IN',
  NOT_IN = 'NOT_IN',
}

/**
 * 验证严重程度枚举
 */
export enum ValidationSeverity {
  ERROR = 'ERROR',
  WARNING = 'WARNING',
  INFO = 'INFO',
}

/**
 * 节点状态枚举（用于可视化）
 */
export enum NodeStatus {
  GREEN = 'GREEN', // 完全匹配
  YELLOW = 'YELLOW', // 部分匹配
  RED = 'RED', // 违背/缺失
  PURPLE = 'PURPLE', // AI幻觉
  BLUE = 'BLUE', // 待深度验证
  GRAY = 'GRAY', // 遗留代码待改造
}

/**
 * 编程语言枚举
 */
export enum Language {
  JAVASCRIPT = 'JAVASCRIPT',
  TYPESCRIPT = 'TYPESCRIPT',
  PYTHON = 'PYTHON',
  JAVA = 'JAVA',
  CSHARP = 'CSHARP',
  CPP = 'CPP',
  GO = 'GO',
  RUST = 'RUST',
  PHP = 'PHP',
  RUBY = 'RUBY',
}

/**
 * 关系类型枚举
 */
export enum RelationshipType {
  DEPENDS_ON = 'DEPENDS_ON',
  CONTAINS = 'CONTAINS',
  IMPLEMENTS = 'IMPLEMENTS',
  EXTENDS = 'EXTENDS',
  USES = 'USES',
  CALLS = 'CALLS',
  REFERENCES = 'REFERENCES',
  COMPOSED_OF = 'COMPOSED_OF',
}
