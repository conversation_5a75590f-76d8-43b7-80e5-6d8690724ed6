import {
  BaseEntity,
  ConstitutionMetadata,
  ConstitutionChange,
} from './interfaces';
import { ValidationResult, ValidationResultImpl } from './ValidationResult';
import { Blueprint } from './Blueprint';
import { BusinessRule } from './BusinessRule';
import { DataEntity } from './DataEntity';

/**
 * 项目宪法类
 * 整个项目的"最高法律"，包含所有蓝图、规则和数据定义
 */
export class ProjectConstitution implements BaseEntity {
  public id: string;
  public name: string;
  public description?: string;
  public createdAt: Date;
  public updatedAt: Date;
  public version: string;
  public blueprint: Blueprint;
  public businessRules: BusinessRule[];
  public dataEntities: DataEntity[];
  public metadata: ConstitutionMetadata;
  public history: ConstitutionChange[];
  public isLocked: boolean; // 是否锁定，防止意外修改

  constructor(data: Partial<ProjectConstitution> = {}) {
    this.id = data.id || this.generateId();
    this.name = data.name || '';
    this.description = data.description;
    this.createdAt = data.createdAt || new Date();
    this.updatedAt = data.updatedAt || new Date();
    this.version = data.version || '1.0.0';
    this.blueprint = data.blueprint || new Blueprint();
    this.businessRules = data.businessRules || [];
    this.dataEntities = data.dataEntities || [];
    this.metadata = data.metadata || this.createDefaultMetadata();
    this.history = data.history || [];
    this.isLocked = data.isLocked || false;
  }

  /**
   * 更新蓝图
   */
  updateBlueprint(blueprint: Blueprint): ValidationResult {
    if (this.isLocked) {
      return ValidationResultImpl.failure(
        'Constitution is locked and cannot be modified',
        'blueprint',
        'LOCKED'
      );
    }

    const validation = blueprint.validate();
    if (!validation.isValid) {
      return validation;
    }

    // const oldBlueprint = this.blueprint;
    this.blueprint = blueprint;
    this.recordChange('UPDATE', 'Blueprint updated', [blueprint.id]);
    this.touch();

    return ValidationResultImpl.success();
  }

  /**
   * 添加业务规则
   */
  addBusinessRule(rule: BusinessRule): ValidationResult {
    if (this.isLocked) {
      return ValidationResultImpl.failure(
        'Constitution is locked and cannot be modified',
        'businessRules',
        'LOCKED'
      );
    }

    const validation = rule.validate();
    if (!validation.isValid) {
      return validation;
    }

    // 检查规则冲突
    const conflicts = rule.detectConflicts(this.businessRules);
    if (conflicts.length > 0) {
      const result = new ValidationResultImpl();
      for (const conflict of conflicts) {
        result.addError(
          conflict.description,
          'businessRules',
          conflict.conflictType
        );
      }
      return result;
    }

    this.businessRules.push(rule);
    this.recordChange('CREATE', `Business rule "${rule.name}" added`, [
      rule.id,
    ]);
    this.touch();

    return ValidationResultImpl.success();
  }

  /**
   * 更新业务规则
   */
  updateBusinessRule(
    ruleId: string,
    updatedRule: BusinessRule
  ): ValidationResult {
    if (this.isLocked) {
      return ValidationResultImpl.failure(
        'Constitution is locked and cannot be modified',
        'businessRules',
        'LOCKED'
      );
    }

    const ruleIndex = this.businessRules.findIndex((r) => r.id === ruleId);
    if (ruleIndex === -1) {
      return ValidationResultImpl.failure(
        'Business rule not found',
        'businessRules',
        'NOT_FOUND'
      );
    }

    const validation = updatedRule.validate();
    if (!validation.isValid) {
      return validation;
    }

    // 检查与其他规则的冲突（排除自己）
    const otherRules = this.businessRules.filter((r) => r.id !== ruleId);
    const conflicts = updatedRule.detectConflicts(otherRules);
    if (conflicts.length > 0) {
      const result = new ValidationResultImpl();
      for (const conflict of conflicts) {
        result.addError(
          conflict.description,
          'businessRules',
          conflict.conflictType
        );
      }
      return result;
    }

    // const oldRule = this.businessRules[ruleIndex];
    this.businessRules[ruleIndex] = updatedRule;
    this.recordChange('UPDATE', `Business rule "${updatedRule.name}" updated`, [
      updatedRule.id,
    ]);
    this.touch();

    return ValidationResultImpl.success();
  }

  /**
   * 删除业务规则
   */
  removeBusinessRule(ruleId: string): ValidationResult {
    if (this.isLocked) {
      return ValidationResultImpl.failure(
        'Constitution is locked and cannot be modified',
        'businessRules',
        'LOCKED'
      );
    }

    const ruleIndex = this.businessRules.findIndex((r) => r.id === ruleId);
    if (ruleIndex === -1) {
      return ValidationResultImpl.failure(
        'Business rule not found',
        'businessRules',
        'NOT_FOUND'
      );
    }

    const rule = this.businessRules[ruleIndex];
    this.businessRules.splice(ruleIndex, 1);
    this.recordChange('DELETE', `Business rule "${rule.name}" removed`, [
      ruleId,
    ]);
    this.touch();

    return ValidationResultImpl.success();
  }

  /**
   * 添加数据实体
   */
  addDataEntity(entity: DataEntity): ValidationResult {
    if (this.isLocked) {
      return ValidationResultImpl.failure(
        'Constitution is locked and cannot be modified',
        'dataEntities',
        'LOCKED'
      );
    }

    const validation = entity.validate();
    if (!validation.isValid) {
      return validation;
    }

    // 检查实体名称是否重复
    if (this.dataEntities.some((e) => e.name === entity.name)) {
      return ValidationResultImpl.failure(
        `Data entity with name "${entity.name}" already exists`,
        'dataEntities',
        'DUPLICATE_NAME'
      );
    }

    this.dataEntities.push(entity);
    this.recordChange('CREATE', `Data entity "${entity.name}" added`, [
      entity.id,
    ]);
    this.touch();

    return ValidationResultImpl.success();
  }

  /**
   * 更新数据实体
   */
  updateDataEntity(
    entityId: string,
    updatedEntity: DataEntity
  ): ValidationResult {
    if (this.isLocked) {
      return ValidationResultImpl.failure(
        'Constitution is locked and cannot be modified',
        'dataEntities',
        'LOCKED'
      );
    }

    const entityIndex = this.dataEntities.findIndex((e) => e.id === entityId);
    if (entityIndex === -1) {
      return ValidationResultImpl.failure(
        'Data entity not found',
        'dataEntities',
        'NOT_FOUND'
      );
    }

    const validation = updatedEntity.validate();
    if (!validation.isValid) {
      return validation;
    }

    // 检查实体名称是否与其他实体重复（排除自己）
    if (
      this.dataEntities.some(
        (e) => e.id !== entityId && e.name === updatedEntity.name
      )
    ) {
      return ValidationResultImpl.failure(
        `Data entity with name "${updatedEntity.name}" already exists`,
        'dataEntities',
        'DUPLICATE_NAME'
      );
    }

    // const oldEntity = this.dataEntities[entityIndex];
    this.dataEntities[entityIndex] = updatedEntity;
    this.recordChange('UPDATE', `Data entity "${updatedEntity.name}" updated`, [
      updatedEntity.id,
    ]);
    this.touch();

    return ValidationResultImpl.success();
  }

  /**
   * 删除数据实体
   */
  removeDataEntity(entityId: string): ValidationResult {
    if (this.isLocked) {
      return ValidationResultImpl.failure(
        'Constitution is locked and cannot be modified',
        'dataEntities',
        'LOCKED'
      );
    }

    const entityIndex = this.dataEntities.findIndex((e) => e.id === entityId);
    if (entityIndex === -1) {
      return ValidationResultImpl.failure(
        'Data entity not found',
        'dataEntities',
        'NOT_FOUND'
      );
    }

    // 检查是否有其他实体依赖此实体
    const dependencies = this.findEntityDependencies(entityId);
    if (dependencies.length > 0) {
      return ValidationResultImpl.failure(
        `Cannot delete entity. It is referenced by: ${dependencies.join(', ')}`,
        'dataEntities',
        'HAS_DEPENDENCIES'
      );
    }

    const entity = this.dataEntities[entityIndex];
    this.dataEntities.splice(entityIndex, 1);
    this.recordChange('DELETE', `Data entity "${entity.name}" removed`, [
      entityId,
    ]);
    this.touch();

    return ValidationResultImpl.success();
  }

  /**
   * 查找实体依赖
   */
  private findEntityDependencies(entityId: string): string[] {
    const dependencies: string[] = [];

    // 检查其他实体的关系
    for (const entity of this.dataEntities) {
      if (entity.id !== entityId) {
        for (const relationship of entity.relationships) {
          if (relationship.targetEntityId === entityId) {
            dependencies.push(`Entity: ${entity.name}`);
            break;
          }
        }
      }
    }

    // 检查蓝图中的数据需求
    const allDataRequirements = this.blueprint.getAllDataRequirements();
    for (const requirement of allDataRequirements) {
      if (requirement.entityId === entityId) {
        dependencies.push('Blueprint data requirements');
        break;
      }
    }

    return dependencies;
  }

  /**
   * 验证整个宪法的完整性
   */
  validate(): ValidationResult {
    const result = new ValidationResultImpl();

    // 验证基本信息
    if (!this.name || this.name.trim() === '') {
      result.addError('Constitution name is required', 'name', 'REQUIRED');
    }

    // 验证蓝图
    const blueprintValidation = this.blueprint.validate();
    result.merge(blueprintValidation);

    // 验证业务规则
    for (const rule of this.businessRules) {
      const ruleValidation = rule.validate();
      result.merge(ruleValidation);
    }

    // 验证数据实体
    for (const entity of this.dataEntities) {
      const entityValidation = entity.validate();
      result.merge(entityValidation);
    }

    // 验证业务规则之间的冲突
    const conflictValidation = this.validateRuleConflicts();
    result.merge(conflictValidation);

    // 验证数据一致性
    const consistencyValidation = this.validateDataConsistency();
    result.merge(consistencyValidation);

    return result;
  }

  /**
   * 验证业务规则冲突
   */
  private validateRuleConflicts(): ValidationResult {
    const result = new ValidationResultImpl();

    for (const rule of this.businessRules) {
      const conflicts = rule.detectConflicts(this.businessRules);
      for (const conflict of conflicts) {
        result.addError(
          conflict.description,
          'businessRules',
          conflict.conflictType
        );
      }
    }

    return result;
  }

  /**
   * 验证数据一致性
   */
  private validateDataConsistency(): ValidationResult {
    const result = new ValidationResultImpl();

    // 验证蓝图中的数据需求是否都有对应的数据实体
    const allDataRequirements = this.blueprint.getAllDataRequirements();
    for (const requirement of allDataRequirements) {
      const entity = this.dataEntities.find(
        (e) => e.id === requirement.entityId
      );
      if (!entity) {
        result.addError(
          `Data requirement references non-existent entity: ${requirement.entityId}`,
          'dataConsistency',
          'MISSING_ENTITY'
        );
      }
    }

    // 验证实体关系的完整性
    for (const entity of this.dataEntities) {
      for (const relationship of entity.relationships) {
        const targetEntity = this.dataEntities.find(
          (e) => e.id === relationship.targetEntityId
        );
        if (!targetEntity) {
          result.addError(
            `Entity "${entity.name}" references non-existent entity: ${relationship.targetEntityId}`,
            'dataConsistency',
            'INVALID_RELATIONSHIP'
          );
        }
      }
    }

    return result;
  }

  /**
   * 锁定宪法
   */
  lock(): void {
    this.isLocked = true;
    this.recordChange('AMENDMENT', 'Constitution locked', []);
    this.touch();
  }

  /**
   * 解锁宪法
   */
  unlock(): void {
    this.isLocked = false;
    this.recordChange('AMENDMENT', 'Constitution unlocked', []);
    this.touch();
  }

  /**
   * 创建宪法修正案
   */
  createAmendment(description: string, changes: ConstitutionChange[]): void {
    this.recordChange(
      'AMENDMENT',
      description,
      changes.map((c) => c.id)
    );
    this.history.push(...changes);
    this.metadata.lastAmendment = new Date();
    this.touch();
  }

  /**
   * 记录变更
   */
  private recordChange(
    changeType: ConstitutionChange['changeType'],
    description: string,
    affectedElements: string[]
  ): void {
    const change: ConstitutionChange = {
      id: this.generateId(),
      timestamp: new Date(),
      changeType,
      description,
      author: this.metadata.author,
      affectedElements,
    };

    this.history.push(change);
    this.metadata.amendmentHistory = this.metadata.amendmentHistory || [];
    this.metadata.amendmentHistory.push(change);
  }

  /**
   * 获取变更历史
   */
  getChangeHistory(elementId?: string): ConstitutionChange[] {
    if (elementId) {
      return this.history.filter((change) =>
        change.affectedElements.includes(elementId)
      );
    }
    return [...this.history];
  }

  /**
   * 回滚到指定版本
   */
  rollbackToVersion(version: string): ValidationResult {
    if (this.isLocked) {
      return ValidationResultImpl.failure(
        'Constitution is locked and cannot be rolled back',
        'version',
        'LOCKED'
      );
    }

    // 在实际实现中，这里需要从版本历史中恢复数据
    // 目前只是一个占位符实现
    this.recordChange('AMENDMENT', `Rolled back to version ${version}`, []);
    this.touch();

    return ValidationResultImpl.success();
  }

  /**
   * 创建默认元数据
   */
  private createDefaultMetadata(): ConstitutionMetadata {
    return {
      author: 'Unknown',
      tags: [],
      customProperties: {},
      constitutionVersion: this.version,
      lastAmendment: this.createdAt,
      amendmentHistory: [],
    };
  }

  /**
   * 序列化为JSON
   */
  toJSON(): Record<string, any> {
    return {
      id: this.id,
      name: this.name,
      description: this.description,
      createdAt: this.createdAt.toISOString(),
      updatedAt: this.updatedAt.toISOString(),
      version: this.version,
      blueprint: this.blueprint.toJSON(),
      businessRules: this.businessRules.map((r) => r.toJSON()),
      dataEntities: this.dataEntities.map((e) => e.toJSON()),
      metadata: {
        ...this.metadata,
        lastAmendment: this.metadata.lastAmendment.toISOString(),
        amendmentHistory: this.metadata.amendmentHistory.map((change) => ({
          ...change,
          timestamp: change.timestamp.toISOString(),
        })),
      },
      history: this.history.map((change) => ({
        ...change,
        timestamp: change.timestamp.toISOString(),
      })),
      isLocked: this.isLocked,
    };
  }

  /**
   * 从JSON反序列化
   */
  static fromJSON(json: Record<string, any>): ProjectConstitution {
    return new ProjectConstitution({
      ...json,
      createdAt: new Date(json.createdAt),
      updatedAt: new Date(json.updatedAt),
      blueprint: Blueprint.fromJSON(json.blueprint),
      businessRules:
        json.businessRules?.map((r: any) => BusinessRule.fromJSON(r)) || [],
      dataEntities:
        json.dataEntities?.map((e: any) => DataEntity.fromJSON(e)) || [],
      metadata: {
        ...json.metadata,
        lastAmendment: new Date(json.metadata.lastAmendment),
        amendmentHistory:
          json.metadata.amendmentHistory?.map((change: any) => ({
            ...change,
            timestamp: new Date(change.timestamp),
          })) || [],
      },
      history:
        json.history?.map((change: any) => ({
          ...change,
          timestamp: new Date(change.timestamp),
        })) || [],
    });
  }

  /**
   * 克隆宪法
   */
  clone(): ProjectConstitution {
    return ProjectConstitution.fromJSON(this.toJSON());
  }

  /**
   * 更新时间戳
   */
  private touch(): void {
    this.updatedAt = new Date();
  }

  /**
   * 生成唯一ID
   */
  private generateId(): string {
    return `constitution_${Date.now()}_${Math.random()
      .toString(36)
      .substr(2, 9)}`;
  }
}
