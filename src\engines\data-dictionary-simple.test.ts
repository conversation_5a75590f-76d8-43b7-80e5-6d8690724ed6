import { describe, it, expect, beforeEach } from 'vitest';
import { DataDictionaryEngine } from './DataDictionaryEngine';
import { DataFieldType } from '../models/enums';

describe('DataDictionaryEngine - Core Functions', () => {
  let engine: DataDictionaryEngine;

  beforeEach(() => {
    engine = new DataDictionaryEngine({
      enableStrictValidation: false, // 禁用严格验证以简化测试
      enableRelationshipValidation: false, // 禁用关系验证以简化测试
      maxEntityCount: 10,
      maxFieldsPerEntity: 20,
    });
  });

  it('should create entity successfully', () => {
    const result = engine.createEntity({
      name: 'User',
      description: 'User entity',
    });

    expect(result.success).toBe(true);
    expect(result.entity).toBeDefined();
    expect(result.entity!.name).toBe('User');
  });

  it('should add field to entity', () => {
    const entityResult = engine.createEntity({
      name: 'User',
      description: 'User entity',
    });

    const fieldResult = engine.addFieldToEntity(entityResult.entity!, {
      name: 'email',
      type: DataFieldType.EMAIL,
      required: true,
    });

    expect(fieldResult.success).toBe(true);
    expect(entityResult.entity!.fields).toHaveLength(1);
    expect(entityResult.entity!.fields[0].name).toBe('email');
  });

  it('should get entity by name', () => {
    engine.createEntity({
      name: 'User',
      description: 'User entity',
    });

    const entity = engine.getEntityByName('User');
    expect(entity).toBeDefined();
    expect(entity!.name).toBe('User');
  });

  it('should search entities', () => {
    engine.createEntity({
      name: 'User',
      description: 'User entity for authentication',
    });

    engine.createEntity({
      name: 'Order',
      description: 'Order entity for e-commerce',
    });

    const results = engine.searchEntities('user');
    expect(results).toHaveLength(1);
    expect(results[0].name).toBe('User');
  });

  it('should export and import data dictionary', () => {
    engine.createEntity({
      name: 'User',
      description: 'User entity',
    });

    const exported = engine.exportDataDictionary();
    expect(exported.entities).toHaveLength(1);

    // 清空并导入
    engine.clear();
    expect(engine.getAllEntities()).toHaveLength(0);

    const importResult = engine.importDataDictionary(exported);
    expect(importResult.success).toBe(true);
    expect(engine.getAllEntities()).toHaveLength(1);
  });

  it('should provide statistics', () => {
    const userResult = engine.createEntity({
      name: 'User',
      description: 'User entity',
    });

    engine.addFieldToEntity(userResult.entity!, {
      name: 'id',
      type: DataFieldType.NUMBER,
      required: true,
    });

    engine.addFieldToEntity(userResult.entity!, {
      name: 'email',
      type: DataFieldType.EMAIL,
      required: true,
    });

    const stats = engine.getStatistics();
    expect(stats.entityCount).toBe(1);
    expect(stats.totalFields).toBe(2);
    expect(stats.averageFieldsPerEntity).toBe(2);
  });

  it('should check data integrity', () => {
    engine.createEntity({
      name: 'User',
      description: 'User entity',
    });

    const report = engine.checkDataIntegrity();
    expect(report).toBeDefined();
    expect(report.isValid).toBe(true);
  });
});