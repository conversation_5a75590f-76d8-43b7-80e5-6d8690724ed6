import { WhiteboardRenderer, WhiteboardNode, WhiteboardEdge } from '../renderers/WhiteboardRenderer';
import { Blueprint } from '../models/Blueprint';
import { ProjectAnalysis } from '../services/AnalysisEngine';
import { ComparisonResult } from '../comparators/BlueprintCodeComparator';
import { NodeStatus, NodeType } from '../models/enums';

/**
 * 白板管理器 - 管理白板的状态和交互
 */
export class WhiteboardManager {
  private renderer: WhiteboardRenderer;
  private currentBlueprint: Blueprint | null = null;
  private currentAnalysis: ProjectAnalysis | null = null;
  private comparisonResult: ComparisonResult | null = null;
  private mode: WhiteboardMode = 'BLUEPRINT';

  constructor(container: HTMLElement) {
    this.renderer = new WhiteboardRenderer(container);
    this.setupEventListeners(container);
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(container: HTMLElement): void {
    // 节点点击事件
    container.addEventListener('nodeClick', (event: CustomEvent) => {
      this.handleNodeClick(event.detail.nodeId);
    });

    // 节点双击事件
    container.addEventListener('nodeDoubleClick', (event: CustomEvent) => {
      this.handleNodeDoubleClick(event.detail.nodeId);
    });
  }

  /**
   * 渲染蓝图模式
   */
  public renderBlueprint(blueprint: Blueprint): void {
    this.currentBlueprint = blueprint;
    this.mode = 'BLUEPRINT';
    this.renderer.clearCanvas();
    this.renderer.renderBlueprint(blueprint);
  }

  /**
   * 渲染比对模式
   */
  public renderComparison(blueprint: Blueprint, analysis: ProjectAnalysis, comparison: ComparisonResult): void {
    this.currentBlueprint = blueprint;
    this.currentAnalysis = analysis;
    this.comparisonResult = comparison;
    this.mode = 'COMPARISON';
    
    this.renderer.clearCanvas();
    this.renderComparisonNodes(blueprint, comparison);
    this.renderComparisonEdges(blueprint, comparison);
  }

  /**
   * 渲染比对节点
   */
  private renderComparisonNodes(blueprint: Blueprint, comparison: ComparisonResult): void {
    // 渲染蓝图中的数据实体
    blueprint.dataEntities.forEach(entity => {
      const matchResult = comparison.entityMatches.find(m => m.blueprintId === entity.id);
      const status = this.getNodeStatusFromMatch(matchResult);
      
      this.renderer.addNode({
        id: entity.id,
        type: 'DATA_ENTITY',
        position: this.calculateNodePosition(entity.id, 'DATA_ENTITY'),
        size: { width: 150, height: 80 },
        data: entity,
        status: status,
        style: this.renderer.getNodeStyle('DATA_ENTITY', status)
      });
    });

    // 渲染蓝图中的业务规则
    blueprint.businessRules.forEach(rule => {
      const matchResult = comparison.ruleMatches.find(m => m.blueprintId === rule.id);
      const status = this.getNodeStatusFromMatch(matchResult);
      
      this.renderer.addNode({
        id: rule.id,
        type: 'BUSINESS_RULE',
        position: this.calculateNodePosition(rule.id, 'BUSINESS_RULE'),
        size: { width: 180, height: 100 },
        data: rule,
        status: status,
        style: this.renderer.getNodeStyle('BUSINESS_RULE', status)
      });
    });

    // 渲染代码中发现的额外实现（紫色节点）
    if (this.currentAnalysis) {
      this.currentAnalysis.entities.forEach(entity => {
        const isInBlueprint = blueprint.dataEntities.some(be => be.name === entity.name);
        if (!isInBlueprint) {
          this.renderer.addNode({
            id: `code-entity-${entity.name}`,
            type: 'DATA_ENTITY',
            position: this.calculateNodePosition(`code-entity-${entity.name}`, 'DATA_ENTITY'),
            size: { width: 150, height: 80 },
            data: entity,
            status: 'PURPLE',
            style: this.renderer.getNodeStyle('DATA_ENTITY', 'PURPLE')
          });
        }
      });
    }
  }

  /**
   * 渲染比对边线
   */
  private renderComparisonEdges(blueprint: Blueprint, comparison: ComparisonResult): void {
    // 渲染蓝图中定义的关系
    blueprint.dataEntities.forEach(entity => {
      if (entity.relationships) {
        entity.relationships.forEach(rel => {
          const edgeStatus = this.getEdgeStatusFromComparison(entity.id, rel.targetEntityId, comparison);
          
          this.renderer.addEdge({
            id: `${entity.id}-${rel.targetEntityId}`,
            sourceId: entity.id,
            targetId: rel.targetEntityId,
            type: 'STRAIGHT',
            label: rel.type,
            style: {
              color: this.getEdgeColorFromStatus(edgeStatus),
              width: 2,
              dashArray: edgeStatus === 'MISSING' ? '5,5' : 'none'
            }
          });
        });
      }
    });
  }

  /**
   * 从匹配结果获取节点状态
   */
  private getNodeStatusFromMatch(matchResult: any): NodeStatus {
    if (!matchResult) {
      return 'RED'; // 缺失
    }

    if (matchResult.confidence >= 0.9) {
      return 'GREEN'; // 完美匹配
    } else if (matchResult.confidence >= 0.7) {
      return 'YELLOW'; // 部分匹配
    } else if (matchResult.confidence >= 0.5) {
      return 'PURPLE'; // 高质量但设计不同
    } else {
      return 'RED'; // 严重不匹配
    }
  }

  /**
   * 从比对结果获取边线状态
   */
  private getEdgeStatusFromComparison(sourceId: string, targetId: string, comparison: ComparisonResult): EdgeStatus {
    // 检查关系是否在代码中实现
    if (this.currentAnalysis) {
      const sourceEntity = this.currentAnalysis.entities.find(e => e.name === sourceId);
      const targetEntity = this.currentAnalysis.entities.find(e => e.name === targetId);
      
      if (sourceEntity && targetEntity) {
        // 检查是否存在依赖关系
        const hasDependency = this.currentAnalysis.dependencies.some(dep => 
          dep.source === sourceEntity.filePath && dep.target === targetEntity.filePath
        );
        
        return hasDependency ? 'IMPLEMENTED' : 'MISSING';
      }
    }
    
    return 'MISSING';
  }

  /**
   * 从状态获取边线颜色
   */
  private getEdgeColorFromStatus(status: EdgeStatus): string {
    switch (status) {
      case 'IMPLEMENTED':
        return '#28a745'; // 绿色
      case 'MISSING':
        return '#dc3545'; // 红色
      case 'PARTIAL':
        return '#ffc107'; // 黄色
      default:
        return '#6c757d'; // 灰色
    }
  }

  /**
   * 计算节点位置
   */
  private calculateNodePosition(nodeId: string, type: NodeType): { x: number; y: number } {
    // 使用简单的网格布局
    const hash = this.hashString(nodeId);
    const gridSize = 200;
    const cols = 4;
    
    const col = hash % cols;
    const row = Math.floor(hash / cols);
    
    let baseX = 100;
    let baseY = 100;
    
    // 根据类型调整基础位置
    switch (type) {
      case 'DATA_ENTITY':
        baseX = 100;
        baseY = 100;
        break;
      case 'BUSINESS_RULE':
        baseX = 100;
        baseY = 300;
        break;
      case 'UI_COMPONENT':
        baseX = 100;
        baseY = 500;
        break;
      case 'FUNCTIONAL_MODULE':
        baseX = 400;
        baseY = 100;
        break;
    }
    
    return {
      x: baseX + col * gridSize,
      y: baseY + row * gridSize
    };
  }

  /**
   * 字符串哈希函数
   */
  private hashString(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash);
  }

  /**
   * 处理节点点击
   */
  private handleNodeClick(nodeId: string): void {
    console.log(`Node clicked: ${nodeId}`);
    
    if (this.mode === 'COMPARISON') {
      this.showNodeComparisonDetails(nodeId);
    } else {
      this.showNodeDetails(nodeId);
    }
  }

  /**
   * 处理节点双击
   */
  private handleNodeDoubleClick(nodeId: string): void {
    console.log(`Node double-clicked: ${nodeId}`);
    
    if (this.mode === 'COMPARISON') {
      this.showJudgmentDialog(nodeId);
    } else {
      this.editNode(nodeId);
    }
  }

  /**
   * 显示节点比对详情
   */
  private showNodeComparisonDetails(nodeId: string): void {
    if (!this.comparisonResult) return;

    // 查找匹配结果
    const entityMatch = this.comparisonResult.entityMatches.find(m => m.blueprintId === nodeId);
    const ruleMatch = this.comparisonResult.ruleMatches.find(m => m.blueprintId === nodeId);
    
    const matchResult = entityMatch || ruleMatch;
    
    if (matchResult) {
      // 触发显示比对详情事件
      const event = new CustomEvent('showComparisonDetails', {
        detail: {
          nodeId,
          matchResult,
          suggestions: matchResult.suggestions
        }
      });
      
      document.dispatchEvent(event);
    }
  }

  /**
   * 显示节点详情
   */
  private showNodeDetails(nodeId: string): void {
    // 触发显示节点详情事件
    const event = new CustomEvent('showNodeDetails', {
      detail: { nodeId }
    });
    
    document.dispatchEvent(event);
  }

  /**
   * 显示裁决对话框
   */
  private showJudgmentDialog(nodeId: string): void {
    // 触发显示裁决对话框事件
    const event = new CustomEvent('showJudgmentDialog', {
      detail: { nodeId }
    });
    
    document.dispatchEvent(event);
  }

  /**
   * 编辑节点
   */
  private editNode(nodeId: string): void {
    // 触发编辑节点事件
    const event = new CustomEvent('editNode', {
      detail: { nodeId }
    });
    
    document.dispatchEvent(event);
  }

  /**
   * 设置交互模式
   */
  public setInteractionMode(mode: 'SELECT' | 'DRAG' | 'CONNECT' | 'CREATE'): void {
    this.renderer.setInteractionMode(mode);
  }

  /**
   * 自动布局
   */
  public autoLayout(): void {
    this.renderer.autoLayout();
  }

  /**
   * 清空画布
   */
  public clearCanvas(): void {
    this.renderer.clearCanvas();
    this.currentBlueprint = null;
    this.currentAnalysis = null;
    this.comparisonResult = null;
  }

  /**
   * 获取当前模式
   */
  public getMode(): WhiteboardMode {
    return this.mode;
  }

  /**
   * 导出画布为图片
   */
  public exportAsImage(): string {
    // TODO: 实现导出功能
    return '';
  }

  /**
   * 保存当前状态
   */
  public saveState(): WhiteboardState {
    return {
      mode: this.mode,
      blueprint: this.currentBlueprint,
      analysis: this.currentAnalysis,
      comparison: this.comparisonResult,
      viewport: this.renderer.getViewport()
    };
  }

  /**
   * 恢复状态
   */
  public restoreState(state: WhiteboardState): void {
    this.mode = state.mode;
    this.currentBlueprint = state.blueprint;
    this.currentAnalysis = state.analysis;
    this.comparisonResult = state.comparison;
    
    if (state.viewport) {
      this.renderer.setViewport(state.viewport);
    }
    
    // 重新渲染
    if (this.mode === 'COMPARISON' && this.currentBlueprint && this.currentAnalysis && this.comparisonResult) {
      this.renderComparison(this.currentBlueprint, this.currentAnalysis, this.comparisonResult);
    } else if (this.mode === 'BLUEPRINT' && this.currentBlueprint) {
      this.renderBlueprint(this.currentBlueprint);
    }
  }
}

// 类型定义
export type WhiteboardMode = 'BLUEPRINT' | 'COMPARISON' | 'ANALYSIS';
export type EdgeStatus = 'IMPLEMENTED' | 'MISSING' | 'PARTIAL';

export interface WhiteboardState {
  mode: WhiteboardMode;
  blueprint: Blueprint | null;
  analysis: ProjectAnalysis | null;
  comparison: ComparisonResult | null;
  viewport: any;
}