import { describe, it, expect, beforeEach } from 'vitest';
import { ConstitutionalEngine } from './ConstitutionalEngine';
import { BusinessRule, DataEntity } from '../models';
import {
  ProjectType,
  BusinessRuleType,
  LogicOperator,
  DataFieldType,
} from '../models/enums';

describe('ConstitutionalEngine', () => {
  let engine: ConstitutionalEngine;

  beforeEach(() => {
    engine = new ConstitutionalEngine({
      enableStrictValidation: true,
      enableConflictDetection: true,
      enableAutoResolution: false,
      maxRuleComplexity: 10,
      maxEntityCount: 100,
    });
  });

  describe('Blueprint Management', () => {
    it('should create a new blueprint successfully', () => {
      const result = engine.createBlueprint({
        projectType: ProjectType.WEB_APPLICATION,
        name: 'Test Project',
        description: 'A test project for validation',
        author: 'Test Author',
        targetLanguages: ['TypeScript', 'JavaScript'],
        estimatedComplexity: 'MEDIUM',
      });

      expect(result.success).toBe(true);
      expect(result.constitution).toBeDefined();
      expect(result.constitution.blueprint.name).toBe('Test Project');
      expect(result.validation.isValid).toBe(true);
    });

    it('should validate blueprint complexity', () => {
      // 创建一个复杂的蓝图
      const result = engine.createBlueprint({
        projectType: ProjectType.ENTERPRISE_APP,
        name: 'Complex Project',
        description: 'A complex enterprise application',
        author: 'Test Author',
      });

      expect(result.success).toBe(true);
      expect(result.constitution).toBeDefined();
    });

    it('should update existing blueprint', () => {
      // 先创建蓝图
      const createResult = engine.createBlueprint({
        projectType: ProjectType.WEB_APPLICATION,
        name: 'Test Project',
        author: 'Test Author',
      });

      expect(createResult.success).toBe(true);

      // 更新蓝图
      const blueprint = createResult.constitution.blueprint;
      blueprint.description = 'Updated description';

      const updateResult = engine.updateBlueprint(blueprint);

      expect(updateResult.success).toBe(true);
      expect(updateResult.constitution.blueprint.description).toBe(
        'Updated description'
      );
    });

    it('should fail to update blueprint without constitution', () => {
      const emptyEngine = new ConstitutionalEngine();
      const blueprint = engine.createBlueprint({
        projectType: ProjectType.WEB_APPLICATION,
        name: 'Test',
        author: 'Test',
      }).constitution.blueprint;

      const result = emptyEngine.updateBlueprint(blueprint);

      expect(result.success).toBe(false);
      expect(result.validation.errors.some(e => e.code === 'NOT_FOUND')).toBe(
        true
      );
    });
  });

  describe('Business Rule Management', () => {
    beforeEach(() => {
      // 创建基础蓝图
      engine.createBlueprint({
        projectType: ProjectType.WEB_APPLICATION,
        name: 'Test Project',
        author: 'Test Author',
      });
    });

    it('should add business rule successfully', () => {
      const rule = new BusinessRule({
        name: 'Email Validation Rule',
        type: BusinessRuleType.VALIDATION,
        condition: {
          operator: LogicOperator.EQUALS,
          operands: [
            { type: 'FIELD', value: 'email' },
            { type: 'CONSTANT', value: 'valid' },
          ],
        },
        action: {
          type: 'SHOW_MESSAGE',
          target: 'user',
          value: 'Email is valid',
        },
        priority: 10,
      });

      const result = engine.addBusinessRule(rule);

      expect(result.success).toBe(true);
      expect(result.constitution.businessRules).toHaveLength(1);
      expect(result.constitution.businessRules[0].name).toBe(
        'Email Validation Rule'
      );
    });

    it('should detect rule conflicts', () => {
      // 添加第一个规则
      const rule1 = new BusinessRule({
        name: 'Rule 1',
        type: BusinessRuleType.VALIDATION,
        condition: {
          operator: LogicOperator.EQUALS,
          operands: [
            { type: 'FIELD', value: 'status' },
            { type: 'CONSTANT', value: 'active' },
          ],
        },
        action: {
          type: 'SET_VALUE',
          target: 'result',
          value: 'approved',
        },
        priority: 10,
      });

      // 添加冲突的规则
      const rule2 = new BusinessRule({
        name: 'Rule 2',
        type: BusinessRuleType.VALIDATION,
        condition: {
          operator: LogicOperator.EQUALS,
          operands: [
            { type: 'FIELD', value: 'status' },
            { type: 'CONSTANT', value: 'active' },
          ],
        },
        action: {
          type: 'SET_VALUE',
          target: 'result', // 相同目标
          value: 'rejected', // 不同值
        },
        priority: 10, // 相同优先级
      });

      engine.addBusinessRule(rule1);
      const result = engine.addBusinessRule(rule2);

      expect(result.conflicts).toBeDefined();
      expect(result.conflicts!.conflicts.length).toBeGreaterThan(0);
    });

    it('should analyze business rule complexity', () => {
      const complexRule = new BusinessRule({
        name: 'Complex Rule',
        type: BusinessRuleType.WORKFLOW,
        condition: {
          operator: LogicOperator.AND,
          operands: [
            {
              operator: LogicOperator.EQUALS,
              operands: [
                { type: 'FIELD', value: 'status' },
                { type: 'CONSTANT', value: 'pending' },
              ],
            },
            {
              operator: LogicOperator.OR,
              operands: [
                {
                  operator: LogicOperator.GREATER_THAN,
                  operands: [
                    { type: 'FIELD', value: 'amount' },
                    { type: 'CONSTANT', value: 1000 },
                  ],
                },
                {
                  operator: LogicOperator.EQUALS,
                  operands: [
                    { type: 'FIELD', value: 'priority' },
                    { type: 'CONSTANT', value: 'high' },
                  ],
                },
              ],
            },
          ],
        },
        action: {
          type: 'TRIGGER_EVENT',
          target: 'approval_workflow',
        },
        priority: 50,
      });

      const result = engine.addBusinessRule(complexRule);

      expect(result.success).toBe(true);
      // 复杂规则应该有建议
      expect(result.validation.infos.length).toBeGreaterThan(0);
    });
  });

  describe('Data Entity Management', () => {
    beforeEach(() => {
      // 创建基础蓝图
      engine.createBlueprint({
        projectType: ProjectType.WEB_APPLICATION,
        name: 'Test Project',
        author: 'Test Author',
      });
    });

    it('should add data entity successfully', () => {
      const entity = new DataEntity({
        name: 'User',
        description: 'User entity for authentication',
      });

      entity.addField({
        name: 'email',
        type: DataFieldType.EMAIL,
        required: true,
        constraints: [],
      });

      entity.addField({
        name: 'password',
        type: DataFieldType.STRING,
        required: true,
        constraints: [
          {
            type: 'LENGTH',
            value: 8,
            message: 'Password must be at least 8 characters',
          },
        ],
      });

      const result = engine.addDataEntity(entity);

      expect(result.success).toBe(true);
      expect(result.constitution.dataEntities).toHaveLength(1);
      expect(result.constitution.dataEntities[0].name).toBe('User');
      expect(result.constitution.dataEntities[0].fields).toHaveLength(2);
    });

    it('should validate data entity integrity', () => {
      const invalidEntity = new DataEntity({
        name: '', // Invalid name
        description: 'Invalid entity',
      });

      const result = engine.addDataEntity(invalidEntity);

      expect(result.success).toBe(false);
      expect(result.validation.errors.length).toBeGreaterThan(0);
    });
  });

  describe('Constitution Validation', () => {
    it('should validate complete constitution', () => {
      // 创建完整的宪法
      const createResult = engine.createBlueprint({
        projectType: ProjectType.WEB_APPLICATION,
        name: 'Complete Project',
        author: 'Test Author',
      });

      expect(createResult.success).toBe(true);

      // 添加数据实体
      const userEntity = new DataEntity({
        name: 'User',
        description: 'User entity',
      });
      userEntity.addField({
        name: 'email',
        type: DataFieldType.EMAIL,
        required: true,
        constraints: [],
      });

      engine.addDataEntity(userEntity);

      // 添加业务规则
      const validationRule = new BusinessRule({
        name: 'Email Validation',
        type: BusinessRuleType.VALIDATION,
        condition: {
          operator: LogicOperator.EQUALS,
          operands: [
            { type: 'FIELD', value: 'email' },
            { type: 'CONSTANT', value: 'valid' },
          ],
        },
        action: {
          type: 'SHOW_MESSAGE',
          target: 'user',
          value: 'Valid email',
        },
        priority: 10,
      });

      engine.addBusinessRule(validationRule);

      // 验证整个宪法
      const validationResult = engine.validateConstitution();

      expect(validationResult.success).toBe(true);
      expect(validationResult.constitution.dataEntities).toHaveLength(1);
      expect(validationResult.constitution.businessRules).toHaveLength(1);
    });

    it('should handle constitution without blueprint', () => {
      const emptyEngine = new ConstitutionalEngine();
      const result = emptyEngine.validateConstitution();

      expect(result.success).toBe(false);
      expect(result.validation.errors.some(e => e.code === 'NOT_FOUND')).toBe(
        true
      );
    });
  });

  describe('Conflict Resolution', () => {
    beforeEach(() => {
      // 创建基础蓝图
      engine.createBlueprint({
        projectType: ProjectType.WEB_APPLICATION,
        name: 'Test Project',
        author: 'Test Author',
      });
    });

    it('should resolve rule conflicts', () => {
      // 创建冲突的规则
      const rule1 = new BusinessRule({
        name: 'Rule 1',
        type: BusinessRuleType.VALIDATION,
        action: {
          type: 'SET_VALUE',
          target: 'status',
          value: 'approved',
        },
        priority: 10,
      });

      const rule2 = new BusinessRule({
        name: 'Rule 2',
        type: BusinessRuleType.VALIDATION,
        action: {
          type: 'SET_VALUE',
          target: 'status',
          value: 'rejected',
        },
        priority: 10, // 相同优先级，会产生冲突
      });

      engine.addBusinessRule(rule1);
      const addResult = engine.addBusinessRule(rule2);

      expect(addResult.conflicts).toBeDefined();
      expect(addResult.conflicts!.conflicts.length).toBeGreaterThan(0);

      // 解决冲突
      const conflict = addResult.conflicts!.conflicts[0];
      const resolution = conflict.suggestedResolutions[0];

      const resolveResult = engine.resolveRuleConflict(conflict.id, resolution);

      expect(resolveResult.success).toBe(true);
    });
  });

  describe('Configuration', () => {
    it('should respect strict validation setting', () => {
      const strictEngine = new ConstitutionalEngine({
        enableStrictValidation: true,
      });

      const lenientEngine = new ConstitutionalEngine({
        enableStrictValidation: false,
      });

      // 创建有问题的实体
      const invalidEntity = new DataEntity({
        name: '', // Invalid name
      });

      // 严格模式应该拒绝
      strictEngine.createBlueprint({
        projectType: ProjectType.WEB_APPLICATION,
        name: 'Test',
        author: 'Test',
      });

      const strictResult = strictEngine.addDataEntity(invalidEntity);
      expect(strictResult.success).toBe(false);

      // 宽松模式可能接受（取决于具体实现）
      lenientEngine.createBlueprint({
        projectType: ProjectType.WEB_APPLICATION,
        name: 'Test',
        author: 'Test',
      });

      const lenientResult = lenientEngine.addDataEntity(invalidEntity);
      // 在这个例子中，由于实体验证失败，两种模式都会失败
      // 但在实际实现中，宽松模式可能会有不同的行为
      expect(lenientResult.success).toBe(false);
    });

    it('should respect conflict detection setting', () => {
      const conflictEngine = new ConstitutionalEngine({
        enableConflictDetection: true,
      });

      const noConflictEngine = new ConstitutionalEngine({
        enableConflictDetection: false,
      });

      // 创建基础蓝图
      conflictEngine.createBlueprint({
        projectType: ProjectType.WEB_APPLICATION,
        name: 'Test',
        author: 'Test',
      });

      noConflictEngine.createBlueprint({
        projectType: ProjectType.WEB_APPLICATION,
        name: 'Test',
        author: 'Test',
      });

      // 添加冲突规则
      const rule1 = new BusinessRule({
        name: 'Rule 1',
        action: { type: 'SET_VALUE', target: 'status', value: 'approved' },
        priority: 10,
      });

      const rule2 = new BusinessRule({
        name: 'Rule 2',
        action: { type: 'SET_VALUE', target: 'status', value: 'rejected' },
        priority: 10,
      });

      conflictEngine.addBusinessRule(rule1);
      const conflictResult = conflictEngine.addBusinessRule(rule2);

      noConflictEngine.addBusinessRule(rule1);
      const noConflictResult = noConflictEngine.addBusinessRule(rule2);

      // 启用冲突检测的引擎应该检测到冲突
      expect(conflictResult.conflicts).toBeDefined();

      // 禁用冲突检测的引擎不应该检测冲突
      expect(noConflictResult.conflicts).toBeUndefined();
    });
  });
});