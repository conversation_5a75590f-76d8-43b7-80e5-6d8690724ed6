import * as vscode from 'vscode';
import { ParseResult, CodeMetrics, SymbolInfo } from '../parsers/BaseParser';
import { DependencyGraph, DependencyNode } from '../analyzers/DependencyAnalyzer';

/**
 * 代码质量等级
 */
export type CodeQualityLevel = 'excellent' | 'good' | 'fair' | 'poor' | 'critical';

/**
 * 代码分类结果
 */
export interface CodeClassification {
  fileUri: string;
  filePath: string;
  relativePath: string;
  classification: 'green_zone' | 'yellow_zone' | 'red_zone' | 'purple_zone';
  qualityLevel: CodeQualityLevel;
  score: number;
  reasons: string[];
  suggestions: string[];
  metrics: CodeQualityMetrics;
  issues: CodeIssue[];
}

/**
 * 代码质量度量
 */
export interface CodeQualityMetrics {
  maintainabilityIndex: number;
  technicalDebt: number;
  cyclomaticComplexity: number;
  cognitiveComplexity: number;
  testCoverage?: number;
  documentationCoverage: number;
  dependencyComplexity: number;
  codeSmells: number;
  duplicatedLines: number;
  linesOfCode: number;
}

/**
 * 代码问题
 */
export interface CodeIssue {
  type: 'complexity' | 'dependency' | 'duplication' | 'documentation' | 'naming' | 'structure';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  suggestion: string;
  location?: vscode.Range;
}

/**
 * 分类配置
 */
export interface ClassificationConfig {
  complexityThresholds: {
    low: number;
    medium: number;
    high: number;
  };
  maintainabilityThresholds: {
    excellent: number;
    good: number;
    fair: number;
    poor: number;
  };
  technicalDebtThresholds: {
    low: number;
    medium: number;
    high: number;
  };
  dependencyThresholds: {
    maxDependencies: number;
    maxDependents: number;
    maxCircularDependencies: number;
  };
  documentationThreshold: number;
  duplicatedLinesThreshold: number;
  enableStrictMode: boolean;
}

/**
 * 遗留代码分类器
 * 基于代码质量指标对代码进行智能分类
 */
export class LegacyCodeClassifier {
  private config: ClassificationConfig;
  private classifications = new Map<string, CodeClassification>();

  constructor(config: Partial<ClassificationConfig> = {}) {
    this.config = {
      complexityThresholds: {
        low: 10,
        medium: 20,
        high: 30
      },
      maintainabilityThresholds: {
        excellent: 80,
        good: 60,
        fair: 40,
        poor: 20
      },
      technicalDebtThresholds: {
        low: 30,
        medium: 60,
        high: 120
      },
      dependencyThresholds: {
        maxDependencies: 10,
        maxDependents: 15,
        maxCircularDependencies: 0
      },
      documentationThreshold: 0.5,
      duplicatedLinesThreshold: 10,
      enableStrictMode: false,
      ...config
    };
  }

  /**
   * 分类项目代码
   */
  public async classifyProject(
    parseResults: Map<string, ParseResult>,
    dependencyGraph: DependencyGraph
  ): Promise<Map<string, CodeClassification>> {
    console.log('🔍 Starting legacy code classification...');
    
    this.classifications.clear();

    for (const [fileUri, parseResult] of parseResults) {
      if (!parseResult.success) continue;

      const dependencyNode = dependencyGraph.nodes.get(fileUri);
      const classification = await this.classifyFile(parseResult, dependencyNode, dependencyGraph);
      
      this.classifications.set(fileUri, classification);
    }

    console.log(`✅ Code classification completed: ${this.classifications.size} files classified`);
    
    return this.classifications;
  }

  /**
   * 分类单个文件
   */
  private async classifyFile(
    parseResult: ParseResult,
    dependencyNode: DependencyNode | undefined,
    dependencyGraph: DependencyGraph
  ): Promise<CodeClassification> {
    const fileUri = dependencyNode?.id || '';
    const filePath = dependencyNode?.filePath || '';
    const relativePath = dependencyNode?.relativePath || '';

    // 计算质量度量
    const qualityMetrics = this.calculateQualityMetrics(parseResult, dependencyNode, dependencyGraph);
    
    // 检测代码问题
    const issues = this.detectCodeIssues(parseResult, dependencyNode, qualityMetrics);
    
    // 计算质量分数
    const score = this.calculateQualityScore(qualityMetrics, issues);
    
    // 确定质量等级
    const qualityLevel = this.determineQualityLevel(score);
    
    // 确定分类区域
    const classification = this.determineClassification(qualityLevel, issues, qualityMetrics);
    
    // 生成原因和建议
    const reasons = this.generateReasons(qualityLevel, issues, qualityMetrics);
    const suggestions = this.generateSuggestions(issues, qualityMetrics);

    return {
      fileUri,
      filePath,
      relativePath,
      classification,
      qualityLevel,
      score,
      reasons,
      suggestions,
      metrics: qualityMetrics,
      issues
    };
  }

  /**
   * 计算代码质量度量
   */
  private calculateQualityMetrics(
    parseResult: ParseResult,
    dependencyNode: DependencyNode | undefined,
    dependencyGraph: DependencyGraph
  ): CodeQualityMetrics {
    const metrics = parseResult.metrics;
    
    // 文档覆盖率
    const documentationCoverage = this.calculateDocumentationCoverage(parseResult.symbols);
    
    // 依赖复杂度
    const dependencyComplexity = this.calculateDependencyComplexity(dependencyNode, dependencyGraph);
    
    // 代码异味数量
    const codeSmells = this.countCodeSmells(parseResult, dependencyNode);

    return {
      maintainabilityIndex: metrics.maintainabilityIndex,
      technicalDebt: metrics.technicalDebt,
      cyclomaticComplexity: metrics.cyclomaticComplexity,
      cognitiveComplexity: metrics.cognitiveComplexity,
      documentationCoverage,
      dependencyComplexity,
      codeSmells,
      duplicatedLines: metrics.duplicatedLines,
      linesOfCode: metrics.linesOfCode
    };
  }

  /**
   * 计算文档覆盖率
   */
  private calculateDocumentationCoverage(symbols: SymbolInfo[]): number {
    if (symbols.length === 0) return 1.0;

    const documentedSymbols = symbols.filter(symbol => 
      symbol.documentation && symbol.documentation.trim().length > 0
    );

    return documentedSymbols.length / symbols.length;
  }

  /**
   * 计算依赖复杂度
   */
  private calculateDependencyComplexity(
    dependencyNode: DependencyNode | undefined,
    dependencyGraph: DependencyGraph
  ): number {
    if (!dependencyNode) return 0;

    const dependencies = dependencyNode.dependencies.length;
    const dependents = dependencyNode.dependents.length;
    
    // 检查是否在循环依赖中
    const inCircularDependency = dependencyGraph.circularDependencies.some(cycle =>
      cycle.cycle.includes(dependencyNode.id)
    );

    let complexity = dependencies + dependents;
    
    if (inCircularDependency) {
      complexity *= 2; // 循环依赖增加复杂度
    }

    return complexity;
  }

  /**
   * 计算代码异味数量
   */
  private countCodeSmells(parseResult: ParseResult, dependencyNode: DependencyNode | undefined): number {
    let smells = 0;
    const metrics = parseResult.metrics;

    // 长方法/函数
    const longFunctions = parseResult.symbols.filter(symbol => 
      (symbol.type === 'function' || symbol.type === 'method') &&
      symbol.range && (symbol.range.end.line - symbol.range.start.line) > 50
    );
    smells += longFunctions.length;

    // 大类
    const largeClasses = parseResult.symbols.filter(symbol => 
      symbol.type === 'class' &&
      symbol.range && (symbol.range.end.line - symbol.range.start.line) > 200
    );
    smells += largeClasses.length;

    // 高复杂度
    if (metrics.cyclomaticComplexity > this.config.complexityThresholds.high) {
      smells += Math.floor(metrics.cyclomaticComplexity / 10);
    }

    // 重复代码
    if (metrics.duplicatedLines > this.config.duplicatedLinesThreshold) {
      smells += Math.floor(metrics.duplicatedLines / 20);
    }

    // 过多依赖
    if (dependencyNode) {
      if (dependencyNode.dependencies.length > this.config.dependencyThresholds.maxDependencies) {
        smells += 1;
      }
      if (dependencyNode.dependents.length > this.config.dependencyThresholds.maxDependents) {
        smells += 1;
      }
    }

    return smells;
  }

  /**
   * 检测代码问题
   */
  private detectCodeIssues(
    parseResult: ParseResult,
    dependencyNode: DependencyNode | undefined,
    qualityMetrics: CodeQualityMetrics
  ): CodeIssue[] {
    const issues: CodeIssue[] = [];

    // 复杂度问题
    if (qualityMetrics.cyclomaticComplexity > this.config.complexityThresholds.high) {
      issues.push({
        type: 'complexity',
        severity: 'high',
        message: `High cyclomatic complexity: ${qualityMetrics.cyclomaticComplexity}`,
        suggestion: 'Consider breaking down complex functions into smaller, more manageable pieces'
      });
    } else if (qualityMetrics.cyclomaticComplexity > this.config.complexityThresholds.medium) {
      issues.push({
        type: 'complexity',
        severity: 'medium',
        message: `Medium cyclomatic complexity: ${qualityMetrics.cyclomaticComplexity}`,
        suggestion: 'Review complex logic and consider refactoring'
      });
    }

    // 认知复杂度问题
    if (qualityMetrics.cognitiveComplexity > 15) {
      issues.push({
        type: 'complexity',
        severity: qualityMetrics.cognitiveComplexity > 25 ? 'high' : 'medium',
        message: `High cognitive complexity: ${qualityMetrics.cognitiveComplexity}`,
        suggestion: 'Simplify nested logic and reduce cognitive load'
      });
    }

    // 依赖问题
    if (dependencyNode) {
      if (dependencyNode.dependencies.length > this.config.dependencyThresholds.maxDependencies) {
        issues.push({
          type: 'dependency',
          severity: 'medium',
          message: `Too many dependencies: ${dependencyNode.dependencies.length}`,
          suggestion: 'Consider reducing dependencies or using dependency injection'
        });
      }

      if (dependencyNode.dependents.length > this.config.dependencyThresholds.maxDependents) {
        issues.push({
          type: 'dependency',
          severity: 'medium',
          message: `Too many dependents: ${dependencyNode.dependents.length}`,
          suggestion: 'This file is heavily used, ensure it has good test coverage and documentation'
        });
      }
    }

    // 重复代码问题
    if (qualityMetrics.duplicatedLines > this.config.duplicatedLinesThreshold) {
      issues.push({
        type: 'duplication',
        severity: qualityMetrics.duplicatedLines > 50 ? 'high' : 'medium',
        message: `Code duplication detected: ${qualityMetrics.duplicatedLines} lines`,
        suggestion: 'Extract common code into reusable functions or modules'
      });
    }

    // 文档问题
    if (qualityMetrics.documentationCoverage < this.config.documentationThreshold) {
      issues.push({
        type: 'documentation',
        severity: qualityMetrics.documentationCoverage < 0.2 ? 'high' : 'medium',
        message: `Low documentation coverage: ${(qualityMetrics.documentationCoverage * 100).toFixed(1)}%`,
        suggestion: 'Add documentation for public functions, classes, and complex logic'
      });
    }

    // 技术债务问题
    if (qualityMetrics.technicalDebt > this.config.technicalDebtThresholds.high) {
      issues.push({
        type: 'structure',
        severity: 'high',
        message: `High technical debt: ${qualityMetrics.technicalDebt} minutes`,
        suggestion: 'Prioritize refactoring to reduce technical debt'
      });
    } else if (qualityMetrics.technicalDebt > this.config.technicalDebtThresholds.medium) {
      issues.push({
        type: 'structure',
        severity: 'medium',
        message: `Medium technical debt: ${qualityMetrics.technicalDebt} minutes`,
        suggestion: 'Consider refactoring when making changes to this file'
      });
    }

    // 命名问题（基于符号分析）
    const namingIssues = this.detectNamingIssues(parseResult.symbols);
    issues.push(...namingIssues);

    return issues;
  }

  /**
   * 检测命名问题
   */
  private detectNamingIssues(symbols: SymbolInfo[]): CodeIssue[] {
    const issues: CodeIssue[] = [];

    for (const symbol of symbols) {
      // 检查短名称
      if (symbol.name.length < 3 && !['id', 'x', 'y', 'i', 'j', 'k'].includes(symbol.name.toLowerCase())) {
        issues.push({
          type: 'naming',
          severity: 'low',
          message: `Short name: ${symbol.name}`,
          suggestion: 'Use more descriptive names for better code readability',
          location: symbol.range
        });
      }

      // 检查过长名称
      if (symbol.name.length > 50) {
        issues.push({
          type: 'naming',
          severity: 'low',
          message: `Very long name: ${symbol.name}`,
          suggestion: 'Consider shortening the name while keeping it descriptive',
          location: symbol.range
        });
      }

      // 检查命名约定
      if (symbol.type === 'class' && !/^[A-Z]/.test(symbol.name)) {
        issues.push({
          type: 'naming',
          severity: 'low',
          message: `Class name should start with uppercase: ${symbol.name}`,
          suggestion: 'Follow naming conventions for better code consistency',
          location: symbol.range
        });
      }
    }

    return issues;
  }

  /**
   * 计算质量分数
   */
  private calculateQualityScore(qualityMetrics: CodeQualityMetrics, issues: CodeIssue[]): number {
    let score = 100;

    // 基于可维护性指数
    score = Math.min(score, qualityMetrics.maintainabilityIndex);

    // 基于问题严重程度扣分
    for (const issue of issues) {
      switch (issue.severity) {
        case 'critical':
          score -= 20;
          break;
        case 'high':
          score -= 10;
          break;
        case 'medium':
          score -= 5;
          break;
        case 'low':
          score -= 2;
          break;
      }
    }

    // 基于代码异味扣分
    score -= qualityMetrics.codeSmells * 3;

    // 基于文档覆盖率调整
    score += (qualityMetrics.documentationCoverage - 0.5) * 20;

    return Math.max(0, Math.min(100, score));
  }

  /**
   * 确定质量等级
   */
  private determineQualityLevel(score: number): CodeQualityLevel {
    if (score >= this.config.maintainabilityThresholds.excellent) return 'excellent';
    if (score >= this.config.maintainabilityThresholds.good) return 'good';
    if (score >= this.config.maintainabilityThresholds.fair) return 'fair';
    if (score >= this.config.maintainabilityThresholds.poor) return 'poor';
    return 'critical';
  }

  /**
   * 确定分类区域
   */
  private determineClassification(
    qualityLevel: CodeQualityLevel,
    issues: CodeIssue[],
    qualityMetrics: CodeQualityMetrics
  ): 'green_zone' | 'yellow_zone' | 'red_zone' | 'purple_zone' {
    const criticalIssues = issues.filter(issue => issue.severity === 'critical').length;
    const highIssues = issues.filter(issue => issue.severity === 'high').length;

    // 紫色区域：特殊情况（高质量但有特殊考虑）
    if (qualityLevel === 'excellent' && qualityMetrics.dependencyComplexity > 20) {
      return 'purple_zone'; // 高质量但依赖复杂
    }

    // 红色区域：需要立即重构
    if (criticalIssues > 0 || qualityLevel === 'critical' || 
        (qualityLevel === 'poor' && highIssues > 2)) {
      return 'red_zone';
    }

    // 黄色区域：需要关注和改进
    if (qualityLevel === 'poor' || qualityLevel === 'fair' || highIssues > 0) {
      return 'yellow_zone';
    }

    // 绿色区域：高质量代码
    return 'green_zone';
  }

  /**
   * 生成分类原因
   */
  private generateReasons(
    qualityLevel: CodeQualityLevel,
    issues: CodeIssue[],
    qualityMetrics: CodeQualityMetrics
  ): string[] {
    const reasons: string[] = [];

    // 基于质量等级
    switch (qualityLevel) {
      case 'excellent':
        reasons.push('High maintainability index and low technical debt');
        break;
      case 'good':
        reasons.push('Good code quality with minor issues');
        break;
      case 'fair':
        reasons.push('Moderate code quality, some improvements needed');
        break;
      case 'poor':
        reasons.push('Low code quality, significant improvements required');
        break;
      case 'critical':
        reasons.push('Critical code quality issues, immediate attention required');
        break;
    }

    // 基于主要问题
    const majorIssues = issues.filter(issue => issue.severity === 'high' || issue.severity === 'critical');
    if (majorIssues.length > 0) {
      reasons.push(`${majorIssues.length} high-severity issues detected`);
    }

    // 基于复杂度
    if (qualityMetrics.cyclomaticComplexity > this.config.complexityThresholds.high) {
      reasons.push('High cyclomatic complexity');
    }

    // 基于技术债务
    if (qualityMetrics.technicalDebt > this.config.technicalDebtThresholds.high) {
      reasons.push('High technical debt');
    }

    // 基于文档覆盖率
    if (qualityMetrics.documentationCoverage < 0.3) {
      reasons.push('Low documentation coverage');
    }

    return reasons;
  }

  /**
   * 生成改进建议
   */
  private generateSuggestions(issues: CodeIssue[], qualityMetrics: CodeQualityMetrics): string[] {
    const suggestions: string[] = [];
    const suggestionSet = new Set<string>();

    // 从问题中提取建议
    for (const issue of issues) {
      if (!suggestionSet.has(issue.suggestion)) {
        suggestions.push(issue.suggestion);
        suggestionSet.add(issue.suggestion);
      }
    }

    // 基于度量添加通用建议
    if (qualityMetrics.linesOfCode > 500) {
      suggestions.push('Consider splitting large files into smaller, more focused modules');
    }

    if (qualityMetrics.codeSmells > 5) {
      suggestions.push('Address code smells to improve overall code quality');
    }

    if (qualityMetrics.dependencyComplexity > 15) {
      suggestions.push('Review and simplify dependency relationships');
    }

    return suggestions;
  }

  /**
   * 获取分类结果
   */
  public getClassifications(): Map<string, CodeClassification> {
    return this.classifications;
  }

  /**
   * 获取分类统计
   */
  public getClassificationStats(): {
    total: number;
    greenZone: number;
    yellowZone: number;
    redZone: number;
    purpleZone: number;
    averageScore: number;
    qualityDistribution: { [key in CodeQualityLevel]: number };
  } {
    const classifications = Array.from(this.classifications.values());
    const total = classifications.length;

    const stats = {
      total,
      greenZone: classifications.filter(c => c.classification === 'green_zone').length,
      yellowZone: classifications.filter(c => c.classification === 'yellow_zone').length,
      redZone: classifications.filter(c => c.classification === 'red_zone').length,
      purpleZone: classifications.filter(c => c.classification === 'purple_zone').length,
      averageScore: total > 0 ? classifications.reduce((sum, c) => sum + c.score, 0) / total : 0,
      qualityDistribution: {
        excellent: classifications.filter(c => c.qualityLevel === 'excellent').length,
        good: classifications.filter(c => c.qualityLevel === 'good').length,
        fair: classifications.filter(c => c.qualityLevel === 'fair').length,
        poor: classifications.filter(c => c.qualityLevel === 'poor').length,
        critical: classifications.filter(c => c.qualityLevel === 'critical').length
      }
    };

    return stats;
  }

  /**
   * 生成分类报告
   */
  public generateReport(): string {
    const stats = this.getClassificationStats();
    const classifications = Array.from(this.classifications.values());

    let report = '🏗️ Legacy Code Classification Report\n';
    report += '====================================\n\n';

    report += `📊 Overall Statistics:\n`;
    report += `  • Total Files: ${stats.total}\n`;
    report += `  • Average Quality Score: ${stats.averageScore.toFixed(1)}/100\n\n`;

    report += `🎯 Zone Distribution:\n`;
    report += `  • 🟢 Green Zone (High Quality): ${stats.greenZone} files (${((stats.greenZone / stats.total) * 100).toFixed(1)}%)\n`;
    report += `  • 🟡 Yellow Zone (Needs Attention): ${stats.yellowZone} files (${((stats.yellowZone / stats.total) * 100).toFixed(1)}%)\n`;
    report += `  • 🔴 Red Zone (Needs Refactoring): ${stats.redZone} files (${((stats.redZone / stats.total) * 100).toFixed(1)}%)\n`;
    report += `  • 🟣 Purple Zone (Special Cases): ${stats.purpleZone} files (${((stats.purpleZone / stats.total) * 100).toFixed(1)}%)\n\n`;

    report += `📈 Quality Level Distribution:\n`;
    Object.entries(stats.qualityDistribution).forEach(([level, count]) => {
      const percentage = ((count / stats.total) * 100).toFixed(1);
      const emoji = level === 'excellent' ? '⭐' : level === 'good' ? '👍' : 
                   level === 'fair' ? '👌' : level === 'poor' ? '👎' : '🚨';
      report += `  • ${emoji} ${level.charAt(0).toUpperCase() + level.slice(1)}: ${count} files (${percentage}%)\n`;
    });

    // 显示最需要关注的文件
    const redZoneFiles = classifications.filter(c => c.classification === 'red_zone')
      .sort((a, b) => a.score - b.score)
      .slice(0, 5);

    if (redZoneFiles.length > 0) {
      report += `\n🚨 Files Requiring Immediate Attention:\n`;
      redZoneFiles.forEach((file, index) => {
        report += `  ${index + 1}. ${file.relativePath} (Score: ${file.score.toFixed(1)})\n`;
        file.reasons.slice(0, 2).forEach(reason => {
          report += `     • ${reason}\n`;
        });
      });
    }

    // 显示高质量文件
    const greenZoneFiles = classifications.filter(c => c.classification === 'green_zone')
      .sort((a, b) => b.score - a.score)
      .slice(0, 3);

    if (greenZoneFiles.length > 0) {
      report += `\n✨ High Quality Files (Examples to Follow):\n`;
      greenZoneFiles.forEach((file, index) => {
        report += `  ${index + 1}. ${file.relativePath} (Score: ${file.score.toFixed(1)})\n`;
      });
    }

    return report;
  }

  /**
   * 获取配置
   */
  public getConfig(): ClassificationConfig {
    return { ...this.config };
  }

  /**
   * 更新配置
   */
  public updateConfig(newConfig: Partial<ClassificationConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * 清理资源
   */
  public dispose(): void {
    this.classifications.clear();
  }
}