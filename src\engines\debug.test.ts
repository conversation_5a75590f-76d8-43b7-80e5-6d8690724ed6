import { describe, it, expect } from 'vitest';

describe('Debug Import Test', () => {
  it('should import models correctly', async () => {
    try {
      const models = await import('../models');
      console.log('Models imported:', Object.keys(models));
      expect(models).toBeDefined();
    } catch (error) {
      console.error('Models import error:', error);
      throw error;
    }
  });

  it('should import ConstitutionalEngine correctly', async () => {
    try {
      const engineModule = await import('./ConstitutionalEngine');
      console.log('Engine module:', Object.keys(engineModule));
      expect(engineModule).toBeDefined();
      expect(engineModule.ConstitutionalEngine).toBeDefined();
    } catch (error) {
      console.error('Engine import error:', error);
      throw error;
    }
  });
});