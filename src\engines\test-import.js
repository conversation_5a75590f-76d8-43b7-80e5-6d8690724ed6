// 简单的导入测试
try {
  const fs = require('fs');
  const path = require('path');
  
  const enginePath = path.join(__dirname, 'ConstitutionalEngine.ts');
  const content = fs.readFileSync(enginePath, 'utf8');
  
  console.log('File exists:', fs.existsSync(enginePath));
  console.log('File size:', content.length);
  console.log('Contains export class ConstitutionalEngine:', content.includes('export class ConstitutionalEngine'));
  
  // 查找类定义的位置
  const classMatch = content.match(/export class ConstitutionalEngine[^{]*{/);
  if (classMatch) {
    console.log('Class definition found at position:', content.indexOf(classMatch[0]));
  } else {
    console.log('Class definition not found');
  }
  
} catch (error) {
  console.error('Error:', error.message);
}