import * as vscode from 'vscode';
import { 
  BaseParser, 
  ParseResult, 
  ASTNode, 
  SymbolInfo, 
  DependencyInfo, 
  ExportInfo, 
  ParseError,
  ParameterInfo
} from './BaseParser';

/**
 * Python解析器
 * 使用正则表达式和文本分析进行Python代码解析
 */
export class PythonParser extends BaseParser {

  /**
   * 解析文档
   */
  public async parse(document: vscode.TextDocument): Promise<ParseResult> {
    const content = document.getText();
    const lines = content.split('\n');

    try {
      // 构建AST
      const ast = this.buildAST(content, lines);
      
      // 提取符号信息
      const symbols = this.extractSymbols(lines);
      
      // 提取依赖信息
      const dependencies = this.extractDependencies(lines);
      
      // 提取导出信息（Python中主要是__all__）
      const exports = this.extractExports(lines);
      
      // 计算代码度量
      const metrics = this.config.calculateMetrics ? this.createBaseMetrics(content) : {
        linesOfCode: 0,
        cyclomaticComplexity: 0,
        cognitiveComplexity: 0,
        maintainabilityIndex: 0,
        technicalDebt: 0,
        duplicatedLines: 0
      };

      return {
        success: true,
        ast,
        symbols,
        dependencies,
        exports,
        errors: [],
        warnings: [],
        metrics
      };

    } catch (error) {
      const parseError = this.createError(
        `Parse error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        0,
        0,
        'PARSE_ERROR'
      );

      return {
        success: false,
        symbols: [],
        dependencies: [],
        exports: [],
        errors: [parseError],
        warnings: [],
        metrics: this.createBaseMetrics(content)
      };
    }
  }

  /**
   * 检查是否支持该语言
   */
  public supports(languageId: string): boolean {
    return languageId === 'python';
  }

  /**
   * 获取支持的语言列表
   */
  public getSupportedLanguages(): string[] {
    return ['python'];
  }

  /**
   * 构建AST
   */
  private buildAST(content: string, lines: string[]): ASTNode {
    const rootNode: ASTNode = {
      type: 'Module',
      range: this.createRange(0, 0, lines.length - 1, lines[lines.length - 1]?.length || 0),
      children: []
    };

    let currentIndent = 0;
    let currentClass: ASTNode | null = null;
    let currentFunction: ASTNode | null = null;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const trimmed = line.trim();
      const indent = line.length - line.trimStart().length;

      if (trimmed === '' || trimmed.startsWith('#')) {
        continue;
      }

      // 重置当前上下文基于缩进
      if (indent <= currentIndent) {
        if (indent === 0) {
          currentClass = null;
          currentFunction = null;
        } else if (currentClass && indent <= this.getNodeIndent(currentClass)) {
          currentClass = null;
        }
        if (currentFunction && indent <= this.getNodeIndent(currentFunction)) {
          currentFunction = null;
        }
      }

      currentIndent = indent;

      // 类定义
      const classMatch = trimmed.match(/^class\s+(\w+)(?:\([^)]*\))?:/);
      if (classMatch) {
        const classNode: ASTNode = {
          type: 'ClassDef',
          name: classMatch[1],
          range: this.createRange(i, indent, i, line.length),
          children: [],
          metadata: { indent }
        };

        if (currentClass) {
          currentClass.children.push(classNode);
        } else {
          rootNode.children.push(classNode);
        }
        currentClass = classNode;
        continue;
      }

      // 函数定义
      const funcMatch = trimmed.match(/^(?:async\s+)?def\s+(\w+)\s*\([^)]*\)(?:\s*->\s*[^:]+)?:/);
      if (funcMatch) {
        const funcNode: ASTNode = {
          type: 'FunctionDef',
          name: funcMatch[1],
          range: this.createRange(i, indent, i, line.length),
          children: [],
          metadata: { 
            indent,
            isAsync: trimmed.startsWith('async'),
            isMethod: !!currentClass
          }
        };

        if (currentClass) {
          currentClass.children.push(funcNode);
        } else if (currentFunction) {
          currentFunction.children.push(funcNode);
        } else {
          rootNode.children.push(funcNode);
        }
        currentFunction = funcNode;
        continue;
      }

      // 变量赋值
      const assignMatch = trimmed.match(/^(\w+)\s*[=:]/);
      if (assignMatch && !trimmed.includes('def ') && !trimmed.includes('class ')) {
        const varNode: ASTNode = {
          type: 'Assign',
          name: assignMatch[1],
          range: this.createRange(i, indent, i, line.length),
          children: [],
          metadata: { indent }
        };

        if (currentFunction) {
          currentFunction.children.push(varNode);
        } else if (currentClass) {
          currentClass.children.push(varNode);
        } else {
          rootNode.children.push(varNode);
        }
      }
    }

    return rootNode;
  }

  /**
   * 获取节点缩进
   */
  private getNodeIndent(node: ASTNode): number {
    return node.metadata?.indent || 0;
  }

  /**
   * 提取符号信息
   */
  private extractSymbols(lines: string[]): SymbolInfo[] {
    const symbols: SymbolInfo[] = [];
    let currentClass: string | null = null;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const trimmed = line.trim();
      const indent = line.length - line.trimStart().length;

      if (trimmed === '' || trimmed.startsWith('#')) {
        continue;
      }

      // 重置类上下文
      if (indent === 0) {
        currentClass = null;
      }

      // 类定义
      const classMatch = trimmed.match(/^class\s+(\w+)(?:\([^)]*\))?:/);
      if (classMatch) {
        currentClass = classMatch[1];
        symbols.push({
          name: classMatch[1],
          type: 'class',
          range: this.createRange(i, indent, i, line.length),
          visibility: this.getVisibilityFromName(classMatch[1]),
          documentation: this.extractPythonDocstring(lines, i + 1)
        });
        continue;
      }

      // 函数定义
      const funcMatch = trimmed.match(/^(?:async\s+)?(def\s+(\w+))\s*\(([^)]*)\)(?:\s*->\s*([^:]+))?:/);
      if (funcMatch) {
        const funcName = funcMatch[2];
        const paramsStr = funcMatch[3];
        const returnType = funcMatch[4];
        const isAsync = trimmed.startsWith('async');
        const isMethod = !!currentClass && indent > 0;

        const parameters = this.parseParameters(paramsStr);
        const fullName = currentClass ? `${currentClass}.${funcName}` : funcName;

        symbols.push({
          name: fullName,
          type: isMethod ? 'method' : 'function',
          range: this.createRange(i, indent, i, line.length),
          visibility: this.getVisibilityFromName(funcName),
          isAsync,
          isStatic: isMethod && parameters.length > 0 && parameters[0].name !== 'self',
          parameters,
          returnType,
          documentation: this.extractPythonDocstring(lines, i + 1)
        });
        continue;
      }

      // 变量赋值（类级别或模块级别）
      const assignMatch = trimmed.match(/^(\w+)\s*[:=]/);
      if (assignMatch && !trimmed.includes('def ') && !trimmed.includes('class ')) {
        const varName = assignMatch[1];
        const isConstant = varName === varName.toUpperCase();
        const fullName = currentClass ? `${currentClass}.${varName}` : varName;

        symbols.push({
          name: fullName,
          type: isConstant ? 'constant' : (currentClass ? 'property' : 'variable'),
          range: this.createRange(i, indent, i, line.length),
          visibility: this.getVisibilityFromName(varName),
          isStatic: !!currentClass
        });
      }
    }

    return symbols;
  }

  /**
   * 解析参数
   */
  private parseParameters(paramsStr: string): ParameterInfo[] {
    if (!paramsStr.trim()) {
      return [];
    }

    const params = paramsStr.split(',').map(p => p.trim()).filter(p => p);
    return params.map(param => {
      const parts = param.split(':');
      const nameAndDefault = parts[0].trim();
      const type = parts[1]?.trim();

      const defaultMatch = nameAndDefault.match(/^(\w+)(?:\s*=\s*(.+))?$/);
      if (defaultMatch) {
        return {
          name: defaultMatch[1],
          type,
          optional: !!defaultMatch[2],
          defaultValue: defaultMatch[2]
        };
      }

      return {
        name: nameAndDefault,
        type,
        optional: false
      };
    });
  }

  /**
   * 从名称获取可见性
   */
  private getVisibilityFromName(name: string): 'public' | 'private' | 'protected' | 'internal' {
    if (name.startsWith('__') && name.endsWith('__')) {
      return 'public'; // 魔术方法
    }
    if (name.startsWith('__')) {
      return 'private'; // 私有
    }
    if (name.startsWith('_')) {
      return 'protected'; // 受保护
    }
    return 'public';
  }

  /**
   * 提取Python文档字符串
   */
  private extractPythonDocstring(lines: string[], startIndex: number): string | undefined {
    if (!this.config.includeDocumentation || startIndex >= lines.length) {
      return undefined;
    }

    const line = lines[startIndex]?.trim();
    if (!line || (!line.startsWith('"""') && !line.startsWith("'''"))) {
      return undefined;
    }

    const quote = line.startsWith('"""') ? '"""' : "'''";
    let docstring = '';
    let i = startIndex;

    // 单行文档字符串
    if (line.length > 6 && line.endsWith(quote)) {
      return line.slice(3, -3).trim();
    }

    // 多行文档字符串
    docstring = line.slice(3);
    i++;

    while (i < lines.length) {
      const currentLine = lines[i];
      if (currentLine.trim().endsWith(quote)) {
        docstring += '\n' + currentLine.slice(0, currentLine.lastIndexOf(quote));
        break;
      }
      docstring += '\n' + currentLine;
      i++;
    }

    return docstring.trim();
  }

  /**
   * 提取依赖信息
   */
  private extractDependencies(lines: string[]): DependencyInfo[] {
    const dependencies: DependencyInfo[] = [];

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const trimmed = line.trim();

      if (trimmed === '' || trimmed.startsWith('#')) {
        continue;
      }

      // import语句
      const importMatch = trimmed.match(/^import\s+(.+)$/);
      if (importMatch) {
        const modules = importMatch[1].split(',').map(m => m.trim());
        modules.forEach(module => {
          const parts = module.split(' as ');
          dependencies.push({
            module: parts[0].trim(),
            type: 'import',
            range: this.createRange(i, 0, i, line.length),
            imported: ['*'],
            alias: parts[1]?.trim()
          });
        });
        continue;
      }

      // from...import语句
      const fromImportMatch = trimmed.match(/^from\s+(.+?)\s+import\s+(.+)$/);
      if (fromImportMatch) {
        const module = fromImportMatch[1].trim();
        const imports = fromImportMatch[2].trim();
        
        let imported: string[];
        if (imports === '*') {
          imported = ['*'];
        } else {
          imported = imports.split(',').map(imp => {
            const parts = imp.trim().split(' as ');
            return parts[0].trim();
          });
        }

        dependencies.push({
          module,
          type: 'import',
          range: this.createRange(i, 0, i, line.length),
          imported,
          alias: imports.includes(' as ') ? imports.split(' as ')[1]?.trim() : undefined
        });
      }
    }

    return dependencies;
  }

  /**
   * 提取导出信息
   */
  private extractExports(lines: string[]): ExportInfo[] {
    const exports: ExportInfo[] = [];

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const trimmed = line.trim();

      // __all__定义
      const allMatch = trimmed.match(/^__all__\s*=\s*\[([^\]]+)\]/);
      if (allMatch) {
        const items = allMatch[1].split(',').map(item => 
          item.trim().replace(/['"]/g, '')
        );

        items.forEach(item => {
          if (item) {
            exports.push({
              name: item,
              type: 'named',
              range: this.createRange(i, 0, i, line.length)
            });
          }
        });
      }
    }

    // 如果没有__all__，则导出所有公共符号
    if (exports.length === 0) {
      for (let i = 0; i < lines.length; i++) {
        const line = lines[i];
        const trimmed = line.trim();
        const indent = line.length - line.trimStart().length;

        // 只考虑模块级别的定义
        if (indent > 0) continue;

        // 类定义
        const classMatch = trimmed.match(/^class\s+(\w+)/);
        if (classMatch && !classMatch[1].startsWith('_')) {
          exports.push({
            name: classMatch[1],
            type: 'named',
            range: this.createRange(i, 0, i, line.length)
          });
        }

        // 函数定义
        const funcMatch = trimmed.match(/^(?:async\s+)?def\s+(\w+)/);
        if (funcMatch && !funcMatch[1].startsWith('_')) {
          exports.push({
            name: funcMatch[1],
            type: 'named',
            range: this.createRange(i, 0, i, line.length)
          });
        }

        // 变量定义
        const varMatch = trimmed.match(/^(\w+)\s*=/);
        if (varMatch && !varMatch[1].startsWith('_') && varMatch[1] === varMatch[1].toUpperCase()) {
          exports.push({
            name: varMatch[1],
            type: 'named',
            range: this.createRange(i, 0, i, line.length)
          });
        }
      }
    }

    return exports;
  }
}