import { ValidationSeverity } from './enums';

/**
 * 验证消息接口
 */
export interface ValidationMessage {
  id: string;
  message: string;
  severity: ValidationSeverity;
  field?: string;
  code?: string;
  context?: Record<string, unknown>;
}

/**
 * 验证结果接口
 */
export interface ValidationResult {
  isValid: boolean;
  messages: ValidationMessage[];
  errors: ValidationMessage[];
  warnings: ValidationMessage[];
  infos: ValidationMessage[];
}

/**
 * 验证结果实现类
 */
export class ValidationResultImpl implements ValidationResult {
  private _messages: ValidationMessage[] = [];

  constructor(messages: ValidationMessage[] = []) {
    this._messages = messages;
  }

  get isValid(): boolean {
    return this.errors.length === 0;
  }

  get messages(): ValidationMessage[] {
    return [...this._messages];
  }

  get errors(): ValidationMessage[] {
    return this._messages.filter(
      (m) => m.severity === ValidationSeverity.ERROR
    );
  }

  get warnings(): ValidationMessage[] {
    return this._messages.filter(
      (m) => m.severity === ValidationSeverity.WARNING
    );
  }

  get infos(): ValidationMessage[] {
    return this._messages.filter((m) => m.severity === ValidationSeverity.INFO);
  }

  /**
   * 添加验证消息
   */
  addMessage(message: ValidationMessage): void {
    this._messages.push(message);
  }

  /**
   * 添加错误消息
   */
  addError(message: string, field?: string, code?: string): void {
    this.addMessage({
      id: this.generateId(),
      message,
      severity: ValidationSeverity.ERROR,
      field,
      code,
    });
  }

  /**
   * 添加警告消息
   */
  addWarning(message: string, field?: string, code?: string): void {
    this.addMessage({
      id: this.generateId(),
      message,
      severity: ValidationSeverity.WARNING,
      field,
      code,
    });
  }

  /**
   * 添加信息消息
   */
  addInfo(message: string, field?: string, code?: string): void {
    this.addMessage({
      id: this.generateId(),
      message,
      severity: ValidationSeverity.INFO,
      field,
      code,
    });
  }

  /**
   * 合并其他验证结果
   */
  merge(other: ValidationResult): ValidationResult {
    const allMessages = [...this._messages, ...other.messages];
    return new ValidationResultImpl(allMessages);
  }

  /**
   * 清空所有消息
   */
  clear(): void {
    this._messages = [];
  }

  /**
   * 生成唯一ID
   */
  private generateId(): string {
    return `validation_${Date.now()}_${Math.random()
      .toString(36)
      .substr(2, 9)}`;
  }

  /**
   * 创建成功的验证结果
   */
  static success(): ValidationResult {
    return new ValidationResultImpl();
  }

  /**
   * 创建失败的验证结果
   */
  static failure(
    message: string,
    field?: string,
    code?: string
  ): ValidationResult {
    const result = new ValidationResultImpl();
    result.addError(message, field, code);
    return result;
  }

  /**
   * 创建带警告的验证结果
   */
  static warning(
    message: string,
    field?: string,
    code?: string
  ): ValidationResult {
    const result = new ValidationResultImpl();
    result.addWarning(message, field, code);
    return result;
  }
}
