import * as vscode from 'vscode';
import { BaseParser, ParseResult, ParserConfig } from './BaseParser';
import { TypeScriptParser } from './TypeScriptParser';
import { PythonParser } from './PythonParser';

/**
 * 解析器统计信息
 */
export interface ParserStats {
  totalParsed: number;
  successfulParsed: number;
  failedParsed: number;
  averageParseTime: number;
  languageStats: { [language: string]: number };
}

/**
 * 解析器管理器
 * 管理多种语言的代码解析器
 */
export class ParserManager {
  private parsers: Map<string, BaseParser> = new Map();
  private stats: ParserStats = {
    totalParsed: 0,
    successfulParsed: 0,
    failedParsed: 0,
    averageParseTime: 0,
    languageStats: {}
  };
  private totalParseTime = 0;

  constructor(config: Partial<ParserConfig> = {}) {
    this.initializeParsers(config);
  }

  /**
   * 初始化解析器
   */
  private initializeParsers(config: Partial<ParserConfig>): void {
    // TypeScript/JavaScript解析器
    const tsParser = new TypeScriptParser(config);
    tsParser.getSupportedLanguages().forEach(lang => {
      this.parsers.set(lang, tsParser);
    });

    // Python解析器
    const pythonParser = new PythonParser(config);
    pythonParser.getSupportedLanguages().forEach(lang => {
      this.parsers.set(lang, pythonParser);
    });

    console.log(`🔧 ParserManager initialized with ${this.parsers.size} language parsers`);
  }

  /**
   * 解析文档
   */
  public async parse(document: vscode.TextDocument): Promise<ParseResult> {
    const startTime = Date.now();
    const languageId = document.languageId;

    try {
      // 获取对应的解析器
      const parser = this.parsers.get(languageId);
      if (!parser) {
        return this.createUnsupportedLanguageResult(languageId);
      }

      // 执行解析
      const result = await parser.parse(document);
      
      // 更新统计信息
      this.updateStats(languageId, Date.now() - startTime, result.success);

      console.log(`📝 Parsed ${document.fileName} (${languageId}) in ${Date.now() - startTime}ms`);
      return result;

    } catch (error) {
      this.updateStats(languageId, Date.now() - startTime, false);
      
      console.error(`❌ Parse error for ${document.fileName}:`, error);
      
      return {
        success: false,
        symbols: [],
        dependencies: [],
        exports: [],
        errors: [{
          message: `Parser error: ${error instanceof Error ? error.message : 'Unknown error'}`,
          range: new vscode.Range(0, 0, 0, 0),
          severity: 'error',
          code: 'PARSER_ERROR'
        }],
        warnings: [],
        metrics: {
          linesOfCode: document.lineCount,
          cyclomaticComplexity: 0,
          cognitiveComplexity: 0,
          maintainabilityIndex: 0,
          technicalDebt: 0,
          duplicatedLines: 0
        }
      };
    }
  }

  /**
   * 批量解析文档
   */
  public async parseMultiple(documents: vscode.TextDocument[]): Promise<Map<string, ParseResult>> {
    const results = new Map<string, ParseResult>();
    
    // 并行解析（限制并发数）
    const concurrency = 5;
    const chunks = this.chunkArray(documents, concurrency);
    
    for (const chunk of chunks) {
      const promises = chunk.map(async doc => {
        const result = await this.parse(doc);
        return { uri: doc.uri.toString(), result };
      });
      
      const chunkResults = await Promise.all(promises);
      chunkResults.forEach(({ uri, result }) => {
        results.set(uri, result);
      });
    }

    console.log(`📚 Batch parsed ${documents.length} documents`);
    return results;
  }

  /**
   * 检查是否支持该语言
   */
  public supports(languageId: string): boolean {
    return this.parsers.has(languageId);
  }

  /**
   * 获取支持的语言列表
   */
  public getSupportedLanguages(): string[] {
    return Array.from(this.parsers.keys());
  }

  /**
   * 获取解析器统计信息
   */
  public getStats(): ParserStats {
    return { ...this.stats };
  }

  /**
   * 重置统计信息
   */
  public resetStats(): void {
    this.stats = {
      totalParsed: 0,
      successfulParsed: 0,
      failedParsed: 0,
      averageParseTime: 0,
      languageStats: {}
    };
    this.totalParseTime = 0;
  }

  /**
   * 更新解析器配置
   */
  public updateConfig(config: Partial<ParserConfig>): void {
    this.parsers.forEach(parser => {
      parser.updateConfig(config);
    });
    console.log('🔧 Parser configuration updated');
  }

  /**
   * 添加自定义解析器
   */
  public addParser(languageId: string, parser: BaseParser): void {
    this.parsers.set(languageId, parser);
    console.log(`➕ Added custom parser for ${languageId}`);
  }

  /**
   * 移除解析器
   */
  public removeParser(languageId: string): boolean {
    const removed = this.parsers.delete(languageId);
    if (removed) {
      console.log(`➖ Removed parser for ${languageId}`);
    }
    return removed;
  }

  /**
   * 获取解析器信息
   */
  public getParserInfo(): { [languageId: string]: string } {
    const info: { [languageId: string]: string } = {};
    
    this.parsers.forEach((parser, languageId) => {
      info[languageId] = parser.constructor.name;
    });

    return info;
  }

  /**
   * 创建不支持语言的结果
   */
  private createUnsupportedLanguageResult(languageId: string): ParseResult {
    return {
      success: false,
      symbols: [],
      dependencies: [],
      exports: [],
      errors: [{
        message: `Unsupported language: ${languageId}`,
        range: new vscode.Range(0, 0, 0, 0),
        severity: 'error',
        code: 'UNSUPPORTED_LANGUAGE'
      }],
      warnings: [],
      metrics: {
        linesOfCode: 0,
        cyclomaticComplexity: 0,
        cognitiveComplexity: 0,
        maintainabilityIndex: 0,
        technicalDebt: 0,
        duplicatedLines: 0
      }
    };
  }

  /**
   * 更新统计信息
   */
  private updateStats(languageId: string, parseTime: number, success: boolean): void {
    this.stats.totalParsed++;
    this.totalParseTime += parseTime;
    this.stats.averageParseTime = this.totalParseTime / this.stats.totalParsed;

    if (success) {
      this.stats.successfulParsed++;
    } else {
      this.stats.failedParsed++;
    }

    this.stats.languageStats[languageId] = (this.stats.languageStats[languageId] || 0) + 1;
  }

  /**
   * 将数组分块
   */
  private chunkArray<T>(array: T[], chunkSize: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize));
    }
    return chunks;
  }

  /**
   * 获取详细的解析报告
   */
  public getDetailedReport(): string {
    const stats = this.getStats();
    const parserInfo = this.getParserInfo();
    
    let report = '📊 Parser Manager Report\n';
    report += '========================\n\n';
    
    report += `📈 Overall Statistics:\n`;
    report += `  • Total Parsed: ${stats.totalParsed}\n`;
    report += `  • Successful: ${stats.successfulParsed} (${((stats.successfulParsed / stats.totalParsed) * 100).toFixed(1)}%)\n`;
    report += `  • Failed: ${stats.failedParsed} (${((stats.failedParsed / stats.totalParsed) * 100).toFixed(1)}%)\n`;
    report += `  • Average Parse Time: ${stats.averageParseTime.toFixed(2)}ms\n\n`;
    
    report += `🌐 Language Statistics:\n`;
    Object.entries(stats.languageStats).forEach(([lang, count]) => {
      const percentage = ((count / stats.totalParsed) * 100).toFixed(1);
      report += `  • ${lang}: ${count} files (${percentage}%)\n`;
    });
    
    report += `\n🔧 Available Parsers:\n`;
    Object.entries(parserInfo).forEach(([lang, parser]) => {
      report += `  • ${lang}: ${parser}\n`;
    });
    
    return report;
  }

  /**
   * 清理资源
   */
  public dispose(): void {
    this.parsers.clear();
    this.resetStats();
    console.log('🧹 ParserManager disposed');
  }
}