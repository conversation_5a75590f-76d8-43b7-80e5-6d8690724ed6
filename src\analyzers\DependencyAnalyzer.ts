import * as vscode from 'vscode';
import * as path from 'path';
import { ParseResult, DependencyInfo } from '../parsers/BaseParser';

/**
 * 依赖节点接口
 */
export interface DependencyNode {
  id: string;
  filePath: string;
  relativePath: string;
  language: string;
  dependencies: string[];
  dependents: string[];
  isExternal: boolean;
  moduleType: 'internal' | 'external' | 'builtin';
  metadata?: {
    size: number;
    lastModified: number;
    complexity?: number;
  };
}

/**
 * 依赖边接口
 */
export interface DependencyEdge {
  from: string;
  to: string;
  type: 'import' | 'require' | 'dynamic';
  weight: number;
  range?: vscode.Range;
}

/**
 * 循环依赖信息
 */
export interface CircularDependency {
  cycle: string[];
  severity: 'low' | 'medium' | 'high';
  description: string;
  suggestions: string[];
}

/**
 * 依赖图接口
 */
export interface DependencyGraph {
  nodes: Map<string, DependencyNode>;
  edges: DependencyEdge[];
  circularDependencies: CircularDependency[];
  externalDependencies: Set<string>;
  statistics: DependencyStatistics;
}

/**
 * 依赖统计信息
 */
export interface DependencyStatistics {
  totalNodes: number;
  totalEdges: number;
  internalNodes: number;
  externalNodes: number;
  circularDependencies: number;
  maxDepth: number;
  averageDependencies: number;
  mostDependentFile: string;
  mostDependedFile: string;
}

/**
 * 依赖分析配置
 */
export interface DependencyAnalyzerConfig {
  includeExternalDependencies: boolean;
  includeBuiltinModules: boolean;
  maxDepth: number;
  ignorePatterns: string[];
  circularDependencyThreshold: number;
  enableCaching: boolean;
}

/**
 * 依赖关系分析器
 * 构建项目依赖图并检测循环依赖等问题
 */
export class DependencyAnalyzer {
  private config: DependencyAnalyzerConfig;
  private dependencyGraph: DependencyGraph;
  private workspaceRoot: string;
  private cache = new Map<string, ParseResult>();

  constructor(config: Partial<DependencyAnalyzerConfig> = {}) {
    this.config = {
      includeExternalDependencies: true,
      includeBuiltinModules: false,
      maxDepth: 10,
      ignorePatterns: [
        '**/node_modules/**',
        '**/dist/**',
        '**/out/**',
        '**/*.test.*',
        '**/*.spec.*'
      ],
      circularDependencyThreshold: 3,
      enableCaching: true,
      ...config
    };

    this.dependencyGraph = this.createEmptyGraph();
    this.workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || '';
  }

  /**
   * 分析项目依赖关系
   */
  public async analyzeProject(parseResults: Map<string, ParseResult>): Promise<DependencyGraph> {
    console.log('🔍 Starting dependency analysis...');
    
    // 重置依赖图
    this.dependencyGraph = this.createEmptyGraph();
    
    // 构建节点
    await this.buildNodes(parseResults);
    
    // 构建边
    await this.buildEdges(parseResults);
    
    // 检测循环依赖
    this.detectCircularDependencies();
    
    // 计算统计信息
    this.calculateStatistics();
    
    console.log(`✅ Dependency analysis completed: ${this.dependencyGraph.nodes.size} nodes, ${this.dependencyGraph.edges.length} edges`);
    
    return this.dependencyGraph;
  }

  /**
   * 构建依赖节点
   */
  private async buildNodes(parseResults: Map<string, ParseResult>): Promise<void> {
    for (const [fileUri, parseResult] of parseResults) {
      if (!parseResult.success) continue;

      const filePath = vscode.Uri.parse(fileUri).fsPath;
      const relativePath = path.relative(this.workspaceRoot, filePath);
      
      // 跳过忽略的文件
      if (this.shouldIgnoreFile(relativePath)) continue;

      const node: DependencyNode = {
        id: fileUri,
        filePath,
        relativePath,
        language: this.getLanguageFromPath(filePath),
        dependencies: [],
        dependents: [],
        isExternal: false,
        moduleType: 'internal',
        metadata: {
          size: 0,
          lastModified: Date.now(),
          complexity: parseResult.metrics?.cyclomaticComplexity
        }
      };

      // 获取文件大小
      try {
        const stat = await vscode.workspace.fs.stat(vscode.Uri.parse(fileUri));
        node.metadata!.size = stat.size;
        node.metadata!.lastModified = stat.mtime;
      } catch (error) {
        // 忽略文件状态获取错误
      }

      this.dependencyGraph.nodes.set(fileUri, node);
    }
  }

  /**
   * 构建依赖边
   */
  private async buildEdges(parseResults: Map<string, ParseResult>): Promise<void> {
    for (const [fileUri, parseResult] of parseResults) {
      if (!parseResult.success) continue;

      const sourceNode = this.dependencyGraph.nodes.get(fileUri);
      if (!sourceNode) continue;

      for (const dependency of parseResult.dependencies) {
        const targetPath = this.resolveDependencyPath(sourceNode.filePath, dependency.module);
        
        if (!targetPath) continue;

        const targetUri = vscode.Uri.file(targetPath).toString();
        let targetNode = this.dependencyGraph.nodes.get(targetUri);

        // 如果目标节点不存在，创建外部依赖节点
        if (!targetNode && this.config.includeExternalDependencies) {
          targetNode = this.createExternalNode(targetPath, dependency.module);
          this.dependencyGraph.nodes.set(targetUri, targetNode);
        }

        if (targetNode) {
          // 创建依赖边
          const edge: DependencyEdge = {
            from: fileUri,
            to: targetUri,
            type: dependency.type,
            weight: this.calculateEdgeWeight(dependency),
            range: dependency.range
          };

          this.dependencyGraph.edges.push(edge);

          // 更新节点的依赖关系
          sourceNode.dependencies.push(targetUri);
          targetNode.dependents.push(fileUri);
        }
      }
    }
  }

  /**
   * 检测循环依赖
   */
  private detectCircularDependencies(): void {
    const visited = new Set<string>();
    const recursionStack = new Set<string>();
    const cycles: string[][] = [];

    const dfs = (nodeId: string, path: string[]): void => {
      if (recursionStack.has(nodeId)) {
        // 找到循环依赖
        const cycleStart = path.indexOf(nodeId);
        if (cycleStart !== -1) {
          const cycle = path.slice(cycleStart).concat([nodeId]);
          cycles.push(cycle);
        }
        return;
      }

      if (visited.has(nodeId)) return;

      visited.add(nodeId);
      recursionStack.add(nodeId);
      path.push(nodeId);

      const node = this.dependencyGraph.nodes.get(nodeId);
      if (node) {
        for (const depId of node.dependencies) {
          dfs(depId, [...path]);
        }
      }

      recursionStack.delete(nodeId);
      path.pop();
    };

    // 对每个节点执行DFS
    for (const nodeId of this.dependencyGraph.nodes.keys()) {
      if (!visited.has(nodeId)) {
        dfs(nodeId, []);
      }
    }

    // 转换为循环依赖对象
    this.dependencyGraph.circularDependencies = cycles.map(cycle => 
      this.createCircularDependency(cycle)
    );
  }

  /**
   * 创建循环依赖对象
   */
  private createCircularDependency(cycle: string[]): CircularDependency {
    const severity = this.calculateCycleSeverity(cycle);
    const fileNames = cycle.map(nodeId => {
      const node = this.dependencyGraph.nodes.get(nodeId);
      return node ? path.basename(node.filePath) : nodeId;
    });

    return {
      cycle,
      severity,
      description: `Circular dependency detected: ${fileNames.join(' → ')}`,
      suggestions: this.generateCycleSuggestions(cycle)
    };
  }

  /**
   * 计算循环依赖严重程度
   */
  private calculateCycleSeverity(cycle: string[]): 'low' | 'medium' | 'high' {
    if (cycle.length <= 2) return 'low';
    if (cycle.length <= 4) return 'medium';
    return 'high';
  }

  /**
   * 生成循环依赖解决建议
   */
  private generateCycleSuggestions(cycle: string[]): string[] {
    const suggestions: string[] = [];
    
    suggestions.push('考虑提取共同依赖到单独的模块');
    suggestions.push('使用依赖注入模式解耦组件');
    suggestions.push('重新设计模块架构，减少相互依赖');
    
    if (cycle.length > 3) {
      suggestions.push('将大型循环拆分为多个小型模块');
    }

    return suggestions;
  }

  /**
   * 计算统计信息
   */
  private calculateStatistics(): void {
    const nodes = Array.from(this.dependencyGraph.nodes.values());
    const internalNodes = nodes.filter(n => n.moduleType === 'internal');
    const externalNodes = nodes.filter(n => n.moduleType === 'external');

    let maxDependencies = 0;
    let maxDependents = 0;
    let mostDependentFile = '';
    let mostDependedFile = '';

    for (const node of nodes) {
      if (node.dependencies.length > maxDependencies) {
        maxDependencies = node.dependencies.length;
        mostDependentFile = node.relativePath;
      }
      
      if (node.dependents.length > maxDependents) {
        maxDependents = node.dependents.length;
        mostDependedFile = node.relativePath;
      }
    }

    this.dependencyGraph.statistics = {
      totalNodes: nodes.length,
      totalEdges: this.dependencyGraph.edges.length,
      internalNodes: internalNodes.length,
      externalNodes: externalNodes.length,
      circularDependencies: this.dependencyGraph.circularDependencies.length,
      maxDepth: this.calculateMaxDepth(),
      averageDependencies: nodes.length > 0 ? 
        nodes.reduce((sum, n) => sum + n.dependencies.length, 0) / nodes.length : 0,
      mostDependentFile,
      mostDependedFile
    };
  }

  /**
   * 计算最大依赖深度
   */
  private calculateMaxDepth(): number {
    let maxDepth = 0;
    const visited = new Set<string>();

    const dfs = (nodeId: string, depth: number): number => {
      if (visited.has(nodeId)) return depth;
      
      visited.add(nodeId);
      const node = this.dependencyGraph.nodes.get(nodeId);
      
      if (!node || node.dependencies.length === 0) {
        return depth;
      }

      let maxChildDepth = depth;
      for (const depId of node.dependencies) {
        const childDepth = dfs(depId, depth + 1);
        maxChildDepth = Math.max(maxChildDepth, childDepth);
      }

      return maxChildDepth;
    };

    for (const nodeId of this.dependencyGraph.nodes.keys()) {
      const depth = dfs(nodeId, 0);
      maxDepth = Math.max(maxDepth, depth);
      visited.clear();
    }

    return maxDepth;
  }

  /**
   * 解析依赖路径
   */
  private resolveDependencyPath(fromFile: string, moduleName: string): string | null {
    // 相对路径
    if (moduleName.startsWith('./') || moduleName.startsWith('../')) {
      const fromDir = path.dirname(fromFile);
      let resolvedPath = path.resolve(fromDir, moduleName);
      
      // 尝试添加常见扩展名
      const extensions = ['.ts', '.js', '.tsx', '.jsx', '.py'];
      for (const ext of extensions) {
        const pathWithExt = resolvedPath + ext;
        if (this.fileExists(pathWithExt)) {
          return pathWithExt;
        }
      }
      
      // 尝试index文件
      for (const ext of extensions) {
        const indexPath = path.join(resolvedPath, `index${ext}`);
        if (this.fileExists(indexPath)) {
          return indexPath;
        }
      }
      
      return resolvedPath;
    }

    // 绝对路径或模块名
    if (path.isAbsolute(moduleName)) {
      return moduleName;
    }

    // 外部模块
    if (this.config.includeExternalDependencies) {
      return path.join(this.workspaceRoot, 'node_modules', moduleName);
    }

    return null;
  }

  /**
   * 检查文件是否存在
   */
  private fileExists(filePath: string): boolean {
    try {
      return require('fs').existsSync(filePath);
    } catch {
      return false;
    }
  }

  /**
   * 创建外部依赖节点
   */
  private createExternalNode(targetPath: string, moduleName: string): DependencyNode {
    const isBuiltin = this.isBuiltinModule(moduleName);
    
    return {
      id: vscode.Uri.file(targetPath).toString(),
      filePath: targetPath,
      relativePath: moduleName,
      language: 'unknown',
      dependencies: [],
      dependents: [],
      isExternal: true,
      moduleType: isBuiltin ? 'builtin' : 'external',
      metadata: {
        size: 0,
        lastModified: 0
      }
    };
  }

  /**
   * 检查是否为内置模块
   */
  private isBuiltinModule(moduleName: string): boolean {
    const builtinModules = [
      'fs', 'path', 'os', 'crypto', 'http', 'https', 'url', 'util',
      'events', 'stream', 'buffer', 'child_process', 'cluster'
    ];
    
    return builtinModules.includes(moduleName);
  }

  /**
   * 计算边权重
   */
  private calculateEdgeWeight(dependency: DependencyInfo): number {
    // 基于依赖类型计算权重
    switch (dependency.type) {
      case 'import': return 1.0;
      case 'require': return 0.8;
      case 'dynamic': return 0.5;
      default: return 1.0;
    }
  }

  /**
   * 获取文件语言
   */
  private getLanguageFromPath(filePath: string): string {
    const ext = path.extname(filePath).toLowerCase();
    const languageMap: { [key: string]: string } = {
      '.ts': 'typescript',
      '.tsx': 'typescriptreact',
      '.js': 'javascript',
      '.jsx': 'javascriptreact',
      '.py': 'python',
      '.java': 'java',
      '.cs': 'csharp'
    };
    
    return languageMap[ext] || 'unknown';
  }

  /**
   * 检查是否应该忽略文件
   */
  private shouldIgnoreFile(relativePath: string): boolean {
    return this.config.ignorePatterns.some(pattern => {
      const regex = new RegExp(pattern.replace(/\*\*/g, '.*').replace(/\*/g, '[^/]*'));
      return regex.test(relativePath);
    });
  }

  /**
   * 创建空依赖图
   */
  private createEmptyGraph(): DependencyGraph {
    return {
      nodes: new Map(),
      edges: [],
      circularDependencies: [],
      externalDependencies: new Set(),
      statistics: {
        totalNodes: 0,
        totalEdges: 0,
        internalNodes: 0,
        externalNodes: 0,
        circularDependencies: 0,
        maxDepth: 0,
        averageDependencies: 0,
        mostDependentFile: '',
        mostDependedFile: ''
      }
    };
  }

  /**
   * 获取依赖图
   */
  public getDependencyGraph(): DependencyGraph {
    return this.dependencyGraph;
  }

  /**
   * 获取循环依赖
   */
  public getCircularDependencies(): CircularDependency[] {
    return this.dependencyGraph.circularDependencies;
  }

  /**
   * 获取节点的依赖路径
   */
  public getDependencyPath(fromNodeId: string, toNodeId: string): string[] | null {
    const visited = new Set<string>();
    const path: string[] = [];

    const dfs = (currentId: string, targetId: string): boolean => {
      if (currentId === targetId) {
        path.push(currentId);
        return true;
      }

      if (visited.has(currentId)) return false;
      
      visited.add(currentId);
      path.push(currentId);

      const node = this.dependencyGraph.nodes.get(currentId);
      if (node) {
        for (const depId of node.dependencies) {
          if (dfs(depId, targetId)) {
            return true;
          }
        }
      }

      path.pop();
      return false;
    };

    return dfs(fromNodeId, toNodeId) ? path : null;
  }

  /**
   * 生成依赖报告
   */
  public generateReport(): string {
    const stats = this.dependencyGraph.statistics;
    const cycles = this.dependencyGraph.circularDependencies;
    
    let report = '🔍 Dependency Analysis Report\n';
    report += '==============================\n\n';
    
    report += `📊 Statistics:\n`;
    report += `  • Total Files: ${stats.totalNodes}\n`;
    report += `  • Internal Files: ${stats.internalNodes}\n`;
    report += `  • External Dependencies: ${stats.externalNodes}\n`;
    report += `  • Total Dependencies: ${stats.totalEdges}\n`;
    report += `  • Average Dependencies per File: ${stats.averageDependencies.toFixed(2)}\n`;
    report += `  • Maximum Dependency Depth: ${stats.maxDepth}\n\n`;
    
    if (stats.mostDependentFile) {
      report += `📈 Most Dependent File: ${stats.mostDependentFile}\n`;
    }
    
    if (stats.mostDependedFile) {
      report += `📉 Most Depended File: ${stats.mostDependedFile}\n\n`;
    }
    
    if (cycles.length > 0) {
      report += `🔄 Circular Dependencies (${cycles.length}):\n`;
      cycles.forEach((cycle, index) => {
        report += `  ${index + 1}. [${cycle.severity.toUpperCase()}] ${cycle.description}\n`;
        cycle.suggestions.forEach(suggestion => {
          report += `     💡 ${suggestion}\n`;
        });
        report += '\n';
      });
    } else {
      report += `✅ No circular dependencies detected!\n\n`;
    }
    
    return report;
  }

  /**
   * 清理资源
   */
  public dispose(): void {
    this.cache.clear();
    this.dependencyGraph = this.createEmptyGraph();
  }
}