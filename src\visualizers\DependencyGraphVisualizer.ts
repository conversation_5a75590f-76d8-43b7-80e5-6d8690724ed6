import * as vscode from 'vscode';
import { DependencyGraph, DependencyNode, DependencyEdge, CircularDependency } from '../analyzers/DependencyAnalyzer';

/**
 * 可视化配置接口
 */
export interface VisualizationConfig {
  showExternalDependencies: boolean;
  showBuiltinModules: boolean;
  highlightCircularDependencies: boolean;
  nodeSize: 'small' | 'medium' | 'large';
  layoutAlgorithm: 'force' | 'hierarchical' | 'circular';
  colorScheme: 'default' | 'dark' | 'colorful';
}

/**
 * 可视化节点接口
 */
export interface VisualNode {
  id: string;
  label: string;
  group: string;
  level?: number;
  color?: string;
  size?: number;
  shape?: string;
  title?: string;
  metadata: any;
}

/**
 * 可视化边接口
 */
export interface VisualEdge {
  from: string;
  to: string;
  label?: string;
  color?: string;
  width?: number;
  dashes?: boolean;
  arrows?: string;
}

/**
 * 依赖图可视化器
 * 将依赖图转换为可视化数据
 */
export class DependencyGraphVisualizer {
  private config: VisualizationConfig;

  constructor(config: Partial<VisualizationConfig> = {}) {
    this.config = {
      showExternalDependencies: true,
      showBuiltinModules: false,
      highlightCircularDependencies: true,
      nodeSize: 'medium',
      layoutAlgorithm: 'force',
      colorScheme: 'default',
      ...config
    };
  }

  /**
   * 将依赖图转换为可视化数据
   */
  public visualize(dependencyGraph: DependencyGraph): { nodes: VisualNode[], edges: VisualEdge[] } {
    const nodes: VisualNode[] = [];
    const edges: VisualEdge[] = [];
    const circularNodes = this.getCircularDependencyNodes(dependencyGraph.circularDependencies);

    // 转换节点
    for (const [nodeId, node] of dependencyGraph.nodes) {
      if (!this.shouldIncludeNode(node)) continue;

      const visualNode = this.createVisualNode(node, circularNodes.has(nodeId));
      nodes.push(visualNode);
    }

    // 转换边
    for (const edge of dependencyGraph.edges) {
      if (!this.shouldIncludeEdge(edge, dependencyGraph.nodes)) continue;

      const visualEdge = this.createVisualEdge(edge, circularNodes);
      edges.push(visualEdge);
    }

    return { nodes, edges };
  }

  /**
   * 创建可视化节点
   */
  private createVisualNode(node: DependencyNode, isInCycle: boolean): VisualNode {
    const label = this.getNodeLabel(node);
    const group = this.getNodeGroup(node);
    const color = this.getNodeColor(node, isInCycle);
    const size = this.getNodeSize(node);
    const shape = this.getNodeShape(node);
    const title = this.getNodeTitle(node);

    return {
      id: node.id,
      label,
      group,
      color,
      size,
      shape,
      title,
      metadata: {
        filePath: node.filePath,
        relativePath: node.relativePath,
        language: node.language,
        dependencies: node.dependencies.length,
        dependents: node.dependents.length,
        isExternal: node.isExternal,
        moduleType: node.moduleType,
        complexity: node.metadata?.complexity,
        size: node.metadata?.size
      }
    };
  }

  /**
   * 创建可视化边
   */
  private createVisualEdge(edge: DependencyEdge, circularNodes: Set<string>): VisualEdge {
    const isCircular = circularNodes.has(edge.from) && circularNodes.has(edge.to);
    
    return {
      from: edge.from,
      to: edge.to,
      label: this.getEdgeLabel(edge),
      color: this.getEdgeColor(edge, isCircular),
      width: this.getEdgeWidth(edge),
      dashes: edge.type === 'dynamic',
      arrows: 'to'
    };
  }

  /**
   * 获取节点标签
   */
  private getNodeLabel(node: DependencyNode): string {
    if (node.isExternal) {
      return node.relativePath;
    }
    
    const fileName = node.relativePath.split('/').pop() || node.relativePath;
    return fileName.replace(/\.(ts|js|tsx|jsx|py)$/, '');
  }

  /**
   * 获取节点分组
   */
  private getNodeGroup(node: DependencyNode): string {
    if (node.moduleType === 'external') return 'external';
    if (node.moduleType === 'builtin') return 'builtin';
    
    // 基于文件路径分组
    const pathParts = node.relativePath.split('/');
    if (pathParts.length > 1) {
      return pathParts[0];
    }
    
    return 'root';
  }

  /**
   * 获取节点颜色
   */
  private getNodeColor(node: DependencyNode, isInCycle: boolean): string {
    if (isInCycle && this.config.highlightCircularDependencies) {
      return '#ff4444'; // 红色表示循环依赖
    }

    switch (this.config.colorScheme) {
      case 'dark':
        return this.getDarkModeColor(node);
      case 'colorful':
        return this.getColorfulColor(node);
      default:
        return this.getDefaultColor(node);
    }
  }

  /**
   * 获取默认颜色方案
   */
  private getDefaultColor(node: DependencyNode): string {
    switch (node.moduleType) {
      case 'external': return '#87CEEB'; // 天蓝色
      case 'builtin': return '#DDA0DD'; // 紫色
      default:
        switch (node.language) {
          case 'typescript': return '#3178C6';
          case 'javascript': return '#F7DF1E';
          case 'python': return '#3776AB';
          default: return '#97C2FC';
        }
    }
  }

  /**
   * 获取暗色模式颜色
   */
  private getDarkModeColor(node: DependencyNode): string {
    switch (node.moduleType) {
      case 'external': return '#4682B4';
      case 'builtin': return '#9370DB';
      default:
        switch (node.language) {
          case 'typescript': return '#2D5AA0';
          case 'javascript': return '#D4B830';
          case 'python': return '#2E5F8A';
          default: return '#7BA7D7';
        }
    }
  }

  /**
   * 获取彩色方案
   */
  private getColorfulColor(node: DependencyNode): string {
    const colors = [
      '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
      '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
    ];
    
    const hash = this.hashString(this.getNodeGroup(node));
    return colors[hash % colors.length];
  }

  /**
   * 获取节点大小
   */
  private getNodeSize(node: DependencyNode): number {
    const baseSize = this.config.nodeSize === 'small' ? 10 : 
                    this.config.nodeSize === 'large' ? 30 : 20;
    
    // 基于依赖数量调整大小
    const dependencyFactor = Math.min(node.dependencies.length + node.dependents.length, 10) / 10;
    return baseSize + (dependencyFactor * 10);
  }

  /**
   * 获取节点形状
   */
  private getNodeShape(node: DependencyNode): string {
    switch (node.moduleType) {
      case 'external': return 'box';
      case 'builtin': return 'diamond';
      default:
        switch (node.language) {
          case 'typescript':
          case 'javascript': return 'dot';
          case 'python': return 'triangle';
          default: return 'dot';
        }
    }
  }

  /**
   * 获取节点提示信息
   */
  private getNodeTitle(node: DependencyNode): string {
    let title = `File: ${node.relativePath}\n`;
    title += `Language: ${node.language}\n`;
    title += `Dependencies: ${node.dependencies.length}\n`;
    title += `Dependents: ${node.dependents.length}\n`;
    
    if (node.metadata?.complexity) {
      title += `Complexity: ${node.metadata.complexity}\n`;
    }
    
    if (node.metadata?.size) {
      title += `Size: ${this.formatFileSize(node.metadata.size)}\n`;
    }
    
    return title.trim();
  }

  /**
   * 获取边标签
   */
  private getEdgeLabel(edge: DependencyEdge): string {
    switch (edge.type) {
      case 'import': return '';
      case 'require': return 'req';
      case 'dynamic': return 'dyn';
      default: return '';
    }
  }

  /**
   * 获取边颜色
   */
  private getEdgeColor(edge: DependencyEdge, isCircular: boolean): string {
    if (isCircular && this.config.highlightCircularDependencies) {
      return '#ff4444';
    }

    switch (edge.type) {
      case 'import': return '#848484';
      case 'require': return '#4169E1';
      case 'dynamic': return '#FF6347';
      default: return '#848484';
    }
  }

  /**
   * 获取边宽度
   */
  private getEdgeWidth(edge: DependencyEdge): number {
    return Math.max(1, edge.weight * 3);
  }

  /**
   * 检查是否应该包含节点
   */
  private shouldIncludeNode(node: DependencyNode): boolean {
    if (node.moduleType === 'external' && !this.config.showExternalDependencies) {
      return false;
    }
    
    if (node.moduleType === 'builtin' && !this.config.showBuiltinModules) {
      return false;
    }
    
    return true;
  }

  /**
   * 检查是否应该包含边
   */
  private shouldIncludeEdge(edge: DependencyEdge, nodes: Map<string, DependencyNode>): boolean {
    const fromNode = nodes.get(edge.from);
    const toNode = nodes.get(edge.to);
    
    if (!fromNode || !toNode) return false;
    
    return this.shouldIncludeNode(fromNode) && this.shouldIncludeNode(toNode);
  }

  /**
   * 获取循环依赖中的节点
   */
  private getCircularDependencyNodes(circularDependencies: CircularDependency[]): Set<string> {
    const nodes = new Set<string>();
    
    for (const cycle of circularDependencies) {
      for (const nodeId of cycle.cycle) {
        nodes.add(nodeId);
      }
    }
    
    return nodes;
  }

  /**
   * 字符串哈希函数
   */
  private hashString(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash);
  }

  /**
   * 格式化文件大小
   */
  private formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * 生成可视化HTML
   */
  public generateVisualizationHTML(nodes: VisualNode[], edges: VisualEdge[]): string {
    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dependency Graph</title>
    <script src="https://unpkg.com/vis-network/standalone/umd/vis-network.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            background-color: var(--vscode-editor-background);
            color: var(--vscode-editor-foreground);
            font-family: var(--vscode-font-family);
        }
        
        #dependency-graph {
            width: 100vw;
            height: 100vh;
        }
        
        .controls {
            position: fixed;
            top: 10px;
            left: 10px;
            background: var(--vscode-panel-background);
            border: 1px solid var(--vscode-panel-border);
            border-radius: 6px;
            padding: 10px;
            z-index: 1000;
        }
        
        .controls button {
            background: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            border: none;
            border-radius: 4px;
            padding: 6px 12px;
            margin: 2px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .controls button:hover {
            background: var(--vscode-button-hoverBackground);
        }
        
        .legend {
            position: fixed;
            bottom: 10px;
            left: 10px;
            background: var(--vscode-panel-background);
            border: 1px solid var(--vscode-panel-border);
            border-radius: 6px;
            padding: 10px;
            font-size: 12px;
            max-width: 300px;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            margin: 4px 0;
        }
        
        .legend-color {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <div class="controls">
        <button onclick="fitNetwork()">🔍 Fit All</button>
        <button onclick="togglePhysics()">⚡ Toggle Physics</button>
        <button onclick="exportImage()">📷 Export</button>
        <button onclick="showStatistics()">📊 Statistics</button>
    </div>
    
    <div id="dependency-graph"></div>
    
    <div class="legend">
        <div class="legend-item">
            <div class="legend-color" style="background-color: #3178C6;"></div>
            <span>TypeScript Files</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background-color: #F7DF1E;"></div>
            <span>JavaScript Files</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background-color: #3776AB;"></div>
            <span>Python Files</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background-color: #87CEEB;"></div>
            <span>External Dependencies</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background-color: #ff4444;"></div>
            <span>Circular Dependencies</span>
        </div>
    </div>

    <script>
        // 数据
        const nodes = new vis.DataSet(${JSON.stringify(nodes)});
        const edges = new vis.DataSet(${JSON.stringify(edges)});
        
        // 配置
        const options = {
            layout: {
                ${this.getLayoutOptions()}
            },
            physics: {
                enabled: true,
                stabilization: { iterations: 100 }
            },
            nodes: {
                borderWidth: 2,
                shadow: true,
                font: {
                    size: 12,
                    color: 'var(--vscode-editor-foreground)'
                }
            },
            edges: {
                shadow: true,
                smooth: {
                    type: 'continuous'
                }
            },
            interaction: {
                hover: true,
                tooltipDelay: 200
            }
        };
        
        // 创建网络
        const container = document.getElementById('dependency-graph');
        const data = { nodes: nodes, edges: edges };
        const network = new vis.Network(container, data, options);
        
        // 事件处理
        network.on('click', function(params) {
            if (params.nodes.length > 0) {
                const nodeId = params.nodes[0];
                const node = nodes.get(nodeId);
                console.log('Selected node:', node);
            }
        });
        
        network.on('doubleClick', function(params) {
            if (params.nodes.length > 0) {
                const nodeId = params.nodes[0];
                const node = nodes.get(nodeId);
                if (node.metadata.filePath) {
                    // 通知VSCode打开文件
                    if (window.acquireVsCodeApi) {
                        const vscode = window.acquireVsCodeApi();
                        vscode.postMessage({
                            command: 'openFile',
                            filePath: node.metadata.filePath
                        });
                    }
                }
            }
        });
        
        // 控制函数
        function fitNetwork() {
            network.fit();
        }
        
        let physicsEnabled = true;
        function togglePhysics() {
            physicsEnabled = !physicsEnabled;
            network.setOptions({ physics: { enabled: physicsEnabled } });
        }
        
        function exportImage() {
            const canvas = container.querySelector('canvas');
            if (canvas) {
                const link = document.createElement('a');
                link.download = 'dependency-graph.png';
                link.href = canvas.toDataURL();
                link.click();
            }
        }
        
        function showStatistics() {
            const stats = {
                nodes: nodes.length,
                edges: edges.length,
                internalNodes: nodes.get().filter(n => !n.metadata.isExternal).length,
                externalNodes: nodes.get().filter(n => n.metadata.isExternal).length
            };
            
            alert(\`Dependency Graph Statistics:
Nodes: \${stats.nodes}
Edges: \${stats.edges}
Internal Files: \${stats.internalNodes}
External Dependencies: \${stats.externalNodes}\`);
        }
        
        // 初始化
        network.once('stabilizationIterationsDone', function() {
            network.fit();
        });
    </script>
</body>
</html>`;
  }

  /**
   * 获取布局选项
   */
  private getLayoutOptions(): string {
    switch (this.config.layoutAlgorithm) {
      case 'hierarchical':
        return `
          hierarchical: {
            enabled: true,
            direction: 'UD',
            sortMethod: 'directed'
          }
        `;
      case 'circular':
        return `
          randomSeed: 2
        `;
      default:
        return `
          randomSeed: 2
        `;
    }
  }

  /**
   * 更新配置
   */
  public updateConfig(newConfig: Partial<VisualizationConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * 获取配置
   */
  public getConfig(): VisualizationConfig {
    return { ...this.config };
  }
}