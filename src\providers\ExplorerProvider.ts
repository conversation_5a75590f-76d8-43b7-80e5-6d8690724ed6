import * as vscode from 'vscode';
import { getConstitutionalEngine, getDataDictionaryEngine } from '../extension';

export class ExplorerProvider
  implements vscode.TreeDataProvider<ExplorerItem>, vscode.Disposable
{
  private _onDidChangeTreeData: vscode.EventEmitter<
    ExplorerItem | undefined | null | void
  > = new vscode.EventEmitter<ExplorerItem | undefined | null | void>();
  readonly onDidChangeTreeData: vscode.Event<
    ExplorerItem | undefined | null | void
  > = this._onDidChangeTreeData.event;

  constructor(private context: vscode.ExtensionContext) {}

  refresh(): void {
    this._onDidChangeTreeData.fire();
  }

  getTreeItem(element: ExplorerItem): vscode.TreeItem {
    return element;
  }

  getChildren(element?: ExplorerItem): Thenable<ExplorerItem[]> {
    if (!element) {
      // Root level items
      return Promise.resolve([
        new ExplorerItem(
          '📋 项目蓝图 (Project Blueprint)',
          vscode.TreeItemCollapsibleState.Collapsed,
          'blueprint'
        ),
        new ExplorerItem(
          '⚖️ 业务规则 (Business Rules)',
          vscode.TreeItemCollapsibleState.Collapsed,
          'rules'
        ),
        new ExplorerItem(
          '📊 数据字典 (Data Dictionary)',
          vscode.TreeItemCollapsibleState.Collapsed,
          'data'
        ),
        new ExplorerItem(
          '🔍 差异分析 (Diff Analysis)',
          vscode.TreeItemCollapsibleState.Collapsed,
          'analysis'
        ),
      ]);
    } else {
      // Child items based on category
      switch (element.category) {
        case 'blueprint':
          return this.getBlueprintItems();
        case 'rules':
          return this.getBusinessRuleItems();
        case 'data':
          return this.getDataEntityItems();
        case 'analysis':
          return Promise.resolve([
            new ExplorerItem(
              '暂无分析结果 (No analysis results)',
              vscode.TreeItemCollapsibleState.None,
              'empty'
            ),
          ]);
        default:
          return Promise.resolve([]);
      }
    }
  }

  private getBlueprintItems(): Thenable<ExplorerItem[]> {
    try {
      const constitutionalEngine = getConstitutionalEngine();
      const constitution = constitutionalEngine?.getConstitution();
      
      if (!constitution) {
        return Promise.resolve([
          new ExplorerItem(
            '暂无蓝图 (No blueprints)',
            vscode.TreeItemCollapsibleState.None,
            'empty'
          ),
        ]);
      }

      const items = [
        new ExplorerItem(
          `🏛️ ${constitution.blueprint.name}`,
          vscode.TreeItemCollapsibleState.None,
          'blueprint-item'
        ),
        new ExplorerItem(
          `📦 模块数量: ${constitution.blueprint.modules.length}`,
          vscode.TreeItemCollapsibleState.None,
          'info'
        ),
      ];

      return Promise.resolve(items);
    } catch (error) {
      return Promise.resolve([
        new ExplorerItem(
          '暂无蓝图 (No blueprints)',
          vscode.TreeItemCollapsibleState.None,
          'empty'
        ),
      ]);
    }
  }

  private getBusinessRuleItems(): Thenable<ExplorerItem[]> {
    try {
      const constitutionalEngine = getConstitutionalEngine();
      const constitution = constitutionalEngine?.getConstitution();
      
      if (!constitution || constitution.businessRules.length === 0) {
        return Promise.resolve([
          new ExplorerItem(
            '暂无规则 (No rules)',
            vscode.TreeItemCollapsibleState.None,
            'empty'
          ),
        ]);
      }

      const items = constitution.businessRules.map((rule: any) => 
        new ExplorerItem(
          `⚖️ ${rule.name}`,
          vscode.TreeItemCollapsibleState.None,
          'rule-item'
        )
      );

      return Promise.resolve(items);
    } catch (error) {
      return Promise.resolve([
        new ExplorerItem(
          '暂无规则 (No rules)',
          vscode.TreeItemCollapsibleState.None,
          'empty'
        ),
      ]);
    }
  }

  private getDataEntityItems(): Thenable<ExplorerItem[]> {
    try {
      const dataDictionaryEngine = getDataDictionaryEngine();
      const entities = dataDictionaryEngine?.getAllEntities() || [];
      
      if (entities.length === 0) {
        return Promise.resolve([
          new ExplorerItem(
            '暂无数据实体 (No data entities)',
            vscode.TreeItemCollapsibleState.None,
            'empty'
          ),
        ]);
      }

      const items = entities.map(entity => 
        new ExplorerItem(
          `📊 ${entity.name} (${entity.fields.length} fields)`,
          vscode.TreeItemCollapsibleState.None,
          'entity-item'
        )
      );

      return Promise.resolve(items);
    } catch (error) {
      return Promise.resolve([
        new ExplorerItem(
          '暂无数据实体 (No data entities)',
          vscode.TreeItemCollapsibleState.None,
          'empty'
        ),
      ]);
    }
  }

  dispose(): void {
    this._onDidChangeTreeData.dispose();
  }
}

class ExplorerItem extends vscode.TreeItem {
  constructor(
    public readonly label: string,
    public readonly collapsibleState: vscode.TreeItemCollapsibleState,
    public readonly category: string
  ) {
    super(label, collapsibleState);
    this.tooltip = `${this.label}`;
    this.contextValue = category;
  }
}
