{"root": true, "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": 6, "sourceType": "module", "project": "./tsconfig.json"}, "plugins": ["@typescript-eslint", "prettier"], "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended", "prettier"], "rules": {"@typescript-eslint/naming-convention": ["warn", {"selector": "import", "format": ["camelCase", "PascalCase"]}], "@typescript-eslint/semi": "warn", "curly": "warn", "eqeqeq": "warn", "no-throw-literal": "warn", "semi": "off", "prettier/prettier": "error"}, "ignorePatterns": ["out", "dist", "**/*.d.ts"], "env": {"node": true, "es6": true}}