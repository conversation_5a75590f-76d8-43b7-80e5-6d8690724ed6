import {
  DataEntity,
  ValidationResult,
  ValidationResultImpl,
} from '../models';
import {
  DataFieldType,
  ValidationSeverity,
} from '../models/enums';
import {
  DataField,
  DataConstraint,
  EntityRelationship,
} from '../models/interfaces';

/**
 * 数据字典引擎配置接口
 */
export interface DataDictionaryConfig {
  enableStrictValidation: boolean;
  enableRelationshipValidation: boolean;
  maxEntityCount: number;
  maxFieldsPerEntity: number;
  enableAutoNaming: boolean;
}

/**
 * 数据字典操作结果接口
 */
export interface DataDictionaryOperationResult {
  success: boolean;
  entity?: DataEntity;
  entities?: DataEntity[];
  validation: ValidationResult;
  warnings?: string[];
}

/**
 * 实体创建选项接口
 */
export interface EntityCreationOptions {
  name: string;
  description?: string;
  tableName?: string;
  schema?: string;
  fields?: Partial<DataField>[];
  relationships?: Partial<EntityRelationship>[];
}

/**
 * 字段创建选项接口
 */
export interface FieldCreationOptions {
  name: string;
  type: DataFieldType;
  required?: boolean;
  defaultValue?: any;
  description?: string;
  constraints?: Partial<DataConstraint>[];
}

/**
 * 数据完整性检查结果接口
 */
export interface DataIntegrityReport {
  isValid: boolean;
  orphanedReferences: string[];
  circularReferences: string[];
  missingEntities: string[];
  duplicateNames: string[];
  constraintViolations: string[];
  recommendations: string[];
}

/**
 * 数据字典引擎类
 * 管理数据实体的定义、验证和完整性检查
 */
export class DataDictionaryEngine {
  private config: DataDictionaryConfig;
  private entities: Map<string, DataEntity> = new Map();
  private entityNameIndex: Map<string, string> = new Map(); // name -> id mapping

  constructor(config: Partial<DataDictionaryConfig> = {}) {
    this.config = {
      enableStrictValidation: true,
      enableRelationshipValidation: true,
      maxEntityCount: 100,
      maxFieldsPerEntity: 50,
      enableAutoNaming: true,
      ...config,
    };
  }

  /**
   * 创建新的数据实体
   */
  createEntity(options: EntityCreationOptions): DataDictionaryOperationResult {
    try {
      // 检查实体数量限制
      if (this.entities.size >= this.config.maxEntityCount) {
        const validation = ValidationResultImpl.failure(
          `Maximum entity count (${this.config.maxEntityCount}) exceeded`,
          'entities',
          'MAX_COUNT_EXCEEDED'
        );
        return { success: false, validation };
      }

      // 检查名称重复
      if (this.entityNameIndex.has(options.name)) {
        const validation = ValidationResultImpl.failure(
          `Entity with name "${options.name}" already exists`,
          'name',
          'DUPLICATE_NAME'
        );
        return { success: false, validation };
      }

      // 创建实体
      const entity = new DataEntity({
        name: options.name,
        description: options.description,
      });

      // 添加字段
      if (options.fields) {
        for (const fieldOptions of options.fields) {
          const fieldResult = this.addFieldToEntity(entity, fieldOptions as FieldCreationOptions);
          if (!fieldResult.success) {
            return fieldResult;
          }
        }
      }

      // 添加关系
      if (options.relationships) {
        for (const relationshipOptions of options.relationships) {
          const relationshipResult = this.addRelationshipToEntity(entity, relationshipOptions as EntityRelationship);
          if (!relationshipResult.success) {
            return relationshipResult;
          }
        }
      }

      // 验证实体
      const validation = this.validateEntity(entity);
      if (this.config.enableStrictValidation && !validation.isValid) {
        return { success: false, entity, validation };
      }

      // 存储实体
      this.entities.set(entity.id, entity);
      this.entityNameIndex.set(entity.name, entity.id);

      return {
        success: true,
        entity,
        validation,
        warnings: validation.warnings.map(w => w.message),
      };
    } catch (error) {
      const validation = ValidationResultImpl.failure(
        `Failed to create entity: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'entity',
        'CREATION_FAILED'
      );
      return { success: false, validation };
    }
  }

  /**
   * 更新现有实体
   */
  updateEntity(entityId: string, updates: Partial<EntityCreationOptions>): DataDictionaryOperationResult {
    const entity = this.entities.get(entityId);
    if (!entity) {
      const validation = ValidationResultImpl.failure(
        `Entity with ID ${entityId} not found`,
        'entityId',
        'NOT_FOUND'
      );
      return { success: false, validation };
    }

    try {
      // 更新基本信息
      if (updates.name && updates.name !== entity.name) {
        // 检查新名称是否重复
        if (this.entityNameIndex.has(updates.name)) {
          const validation = ValidationResultImpl.failure(
            `Entity with name "${updates.name}" already exists`,
            'name',
            'DUPLICATE_NAME'
          );
          return { success: false, validation };
        }

        // 更新名称索引
        this.entityNameIndex.delete(entity.name);
        this.entityNameIndex.set(updates.name, entity.id);
        entity.name = updates.name;
      }

      if (updates.description !== undefined) {
        entity.description = updates.description;
      }

      // 验证更新后的实体
      const validation = this.validateEntity(entity);
      if (this.config.enableStrictValidation && !validation.isValid) {
        return { success: false, entity, validation };
      }

      return {
        success: true,
        entity,
        validation,
        warnings: validation.warnings.map(w => w.message),
      };
    } catch (error) {
      const validation = ValidationResultImpl.failure(
        `Failed to update entity: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'entity',
        'UPDATE_FAILED'
      );
      return { success: false, validation };
    }
  }

  /**
   * 删除实体
   */
  deleteEntity(entityId: string): DataDictionaryOperationResult {
    const entity = this.entities.get(entityId);
    if (!entity) {
      const validation = ValidationResultImpl.failure(
        `Entity with ID ${entityId} not found`,
        'entityId',
        'NOT_FOUND'
      );
      return { success: false, validation };
    }

    try {
      // 检查是否有其他实体依赖此实体
      const dependencies = this.findEntityDependencies(entityId);
      if (dependencies.length > 0) {
        const validation = ValidationResultImpl.failure(
          `Cannot delete entity "${entity.name}". It is referenced by: ${dependencies.join(', ')}`,
          'dependencies',
          'HAS_DEPENDENCIES'
        );
        return { success: false, validation };
      }

      // 删除实体
      this.entities.delete(entityId);
      this.entityNameIndex.delete(entity.name);

      return {
        success: true,
        validation: ValidationResultImpl.success(),
      };
    } catch (error) {
      const validation = ValidationResultImpl.failure(
        `Failed to delete entity: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'entity',
        'DELETE_FAILED'
      );
      return { success: false, validation };
    }
  }

  /**
   * 向实体添加字段
   */
  addFieldToEntity(entity: DataEntity, fieldOptions: FieldCreationOptions): DataDictionaryOperationResult {
    try {
      // 检查字段数量限制
      if (entity.fields.length >= this.config.maxFieldsPerEntity) {
        const validation = ValidationResultImpl.failure(
          `Maximum field count (${this.config.maxFieldsPerEntity}) exceeded for entity "${entity.name}"`,
          'fields',
          'MAX_FIELDS_EXCEEDED'
        );
        return { success: false, validation };
      }

      // 检查字段名重复
      if (entity.getFieldByName(fieldOptions.name)) {
        const validation = ValidationResultImpl.failure(
          `Field with name "${fieldOptions.name}" already exists in entity "${entity.name}"`,
          'fieldName',
          'DUPLICATE_FIELD_NAME'
        );
        return { success: false, validation };
      }

      // 创建约束
      const constraints: DataConstraint[] = [];
      if (fieldOptions.constraints) {
        for (const constraintOptions of fieldOptions.constraints) {
          constraints.push({
            type: constraintOptions.type || 'CUSTOM',
            value: constraintOptions.value || '',
            message: constraintOptions.message || `Constraint violation for field ${fieldOptions.name}`,
          });
        }
      }

      // 添加字段
      const field = entity.addField({
        name: fieldOptions.name,
        type: fieldOptions.type,
        required: fieldOptions.required || false,
        defaultValue: fieldOptions.defaultValue,
        constraints,
        description: fieldOptions.description,
      });

      // 验证字段
      const validation = this.validateField(field, entity);

      // 在严格验证模式下，如果验证失败则返回失败
      if (this.config.enableStrictValidation && !validation.isValid) {
        // 移除刚添加的字段
        entity.removeField(field.id);
        return {
          success: false,
          entity,
          validation,
        };
      }

      return {
        success: true,
        entity,
        validation,
        warnings: validation.warnings.map(w => w.message),
      };
    } catch (error) {
      const validation = ValidationResultImpl.failure(
        `Failed to add field: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'field',
        'ADD_FIELD_FAILED'
      );
      return { success: false, validation };
    }
  }

  /**
   * 向实体添加关系
   */
  addRelationshipToEntity(entity: DataEntity, relationship: EntityRelationship): DataDictionaryOperationResult {
    try {
      // 验证目标实体是否存在
      if (this.config.enableRelationshipValidation) {
        const targetEntity = this.entities.get(relationship.targetEntityId);
        if (!targetEntity) {
          const validation = ValidationResultImpl.failure(
            `Target entity with ID ${relationship.targetEntityId} not found`,
            'targetEntityId',
            'TARGET_NOT_FOUND'
          );
          return { success: false, validation };
        }
      }

      // 添加关系（去掉id字段，让addRelationship方法自动生成）
      const relationshipToAdd = {
        type: relationship.type,
        targetEntityId: relationship.targetEntityId,
        foreignKey: relationship.foreignKey,
        cascadeDelete: relationship.cascadeDelete,
      };
      const addedRelationship = entity.addRelationship(relationshipToAdd);

      // 验证关系
      const validation = this.validateRelationship(addedRelationship, entity);

      // 在严格验证模式下，如果验证失败则返回失败
      if (this.config.enableStrictValidation && !validation.isValid) {
        // 移除刚添加的关系
        entity.removeRelationship(addedRelationship.id);
        return {
          success: false,
          entity,
          validation,
        };
      }

      return {
        success: true,
        entity,
        validation,
        warnings: validation.warnings.map(w => w.message),
      };
    } catch (error) {
      const validation = ValidationResultImpl.failure(
        `Failed to add relationship: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'relationship',
        'ADD_RELATIONSHIP_FAILED'
      );
      return { success: false, validation };
    }
  }

  /**
   * 获取实体
   */
  getEntity(entityId: string): DataEntity | undefined {
    return this.entities.get(entityId);
  }

  /**
   * 根据名称获取实体
   */
  getEntityByName(name: string): DataEntity | undefined {
    const entityId = this.entityNameIndex.get(name);
    return entityId ? this.entities.get(entityId) : undefined;
  }

  /**
   * 获取所有实体
   */
  getAllEntities(): DataEntity[] {
    return Array.from(this.entities.values());
  }

  /**
   * 搜索实体
   */
  searchEntities(query: string): DataEntity[] {
    const lowerQuery = query.toLowerCase();
    return Array.from(this.entities.values()).filter(entity =>
      entity.name.toLowerCase().includes(lowerQuery) ||
      (entity.description && entity.description.toLowerCase().includes(lowerQuery))
    );
  }

  /**
   * 验证实体
   */
  validateEntity(entity: DataEntity): ValidationResult {
    const result = new ValidationResultImpl();

    // 基础验证
    const basicValidation = entity.validate();
    result.merge(basicValidation);

    // 字段验证
    for (const field of entity.fields) {
      const fieldValidation = this.validateField(field, entity);
      result.merge(fieldValidation);
    }

    // 关系验证
    if (this.config.enableRelationshipValidation) {
      for (const relationship of entity.relationships) {
        const relationshipValidation = this.validateRelationship(relationship, entity);
        result.merge(relationshipValidation);
      }
    }

    return result;
  }

  /**
   * 验证字段
   */
  private validateField(field: DataField, entity: DataEntity): ValidationResult {
    const result = new ValidationResultImpl();

    // 字段类型特定验证
    switch (field.type) {
      case DataFieldType.EMAIL:
        if (field.defaultValue && typeof field.defaultValue === 'string') {
          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
          if (!emailRegex.test(field.defaultValue)) {
            result.addWarning(
              `Default value for email field "${field.name}" is not a valid email`,
              'defaultValue',
              'INVALID_EMAIL_FORMAT'
            );
          }
        }
        break;

      case DataFieldType.URL:
        if (field.defaultValue && typeof field.defaultValue === 'string') {
          try {
            new URL(field.defaultValue);
          } catch {
            result.addWarning(
              `Default value for URL field "${field.name}" is not a valid URL`,
              'defaultValue',
              'INVALID_URL_FORMAT'
            );
          }
        }
        break;

      case DataFieldType.REFERENCE:
        // 验证引用字段是否有外键约束
        const hasForeignKey = field.constraints.some(c => c.type === 'FOREIGN_KEY');
        if (!hasForeignKey) {
          result.addWarning(
            `Reference field "${field.name}" should have a foreign key constraint`,
            'constraints',
            'MISSING_FOREIGN_KEY'
          );
        }
        break;
    }

    // 约束验证
    for (const constraint of field.constraints) {
      const constraintValidation = this.validateConstraint(constraint, field);
      result.merge(constraintValidation);
    }

    return result;
  }

  /**
   * 验证约束
   */
  private validateConstraint(constraint: DataConstraint, field: DataField): ValidationResult {
    const result = new ValidationResultImpl();

    switch (constraint.type) {
      case 'LENGTH':
        if (typeof constraint.value !== 'number' || constraint.value <= 0) {
          result.addError(
            `Length constraint for field "${field.name}" must be a positive number`,
            'constraint.value',
            'INVALID_LENGTH_VALUE'
          );
        }
        break;

      case 'RANGE':
        if (!Array.isArray(constraint.value) || constraint.value.length !== 2) {
          result.addError(
            `Range constraint for field "${field.name}" must be an array of two numbers`,
            'constraint.value',
            'INVALID_RANGE_VALUE'
          );
        }
        break;

      case 'PATTERN':
        if (typeof constraint.value !== 'string') {
          result.addError(
            `Pattern constraint for field "${field.name}" must be a string`,
            'constraint.value',
            'INVALID_PATTERN_VALUE'
          );
        } else {
          try {
            new RegExp(constraint.value);
          } catch {
            result.addError(
              `Pattern constraint for field "${field.name}" is not a valid regular expression`,
              'constraint.value',
              'INVALID_REGEX_PATTERN'
            );
          }
        }
        break;
    }

    return result;
  }

  /**
   * 验证关系
   */
  private validateRelationship(relationship: EntityRelationship, entity: DataEntity): ValidationResult {
    const result = new ValidationResultImpl();

    // 检查目标实体是否存在
    const targetEntity = this.entities.get(relationship.targetEntityId);
    if (!targetEntity) {
      result.addError(
        `Target entity with ID ${relationship.targetEntityId} not found`,
        'targetEntityId',
        'TARGET_ENTITY_NOT_FOUND'
      );
      return result;
    }

    // 检查循环引用
    if (this.hasCircularReference(entity.id, relationship.targetEntityId)) {
      result.addWarning(
        `Circular reference detected between "${entity.name}" and "${targetEntity.name}"`,
        'circularReference',
        'CIRCULAR_REFERENCE'
      );
    }

    // 检查外键字段是否存在
    if (relationship.foreignKey) {
      const foreignKeyField = entity.getFieldByName(relationship.foreignKey);
      if (!foreignKeyField) {
        result.addError(
          `Foreign key field "${relationship.foreignKey}" not found in entity "${entity.name}"`,
          'foreignKey',
          'FOREIGN_KEY_FIELD_NOT_FOUND'
        );
      } else if (foreignKeyField.type !== DataFieldType.REFERENCE) {
        result.addWarning(
          `Foreign key field "${relationship.foreignKey}" should be of type REFERENCE`,
          'foreignKey',
          'FOREIGN_KEY_TYPE_MISMATCH'
        );
      }
    }

    return result;
  }

  /**
   * 检查数据完整性
   */
  checkDataIntegrity(): DataIntegrityReport {
    const report: DataIntegrityReport = {
      isValid: true,
      orphanedReferences: [],
      circularReferences: [],
      missingEntities: [],
      duplicateNames: [],
      constraintViolations: [],
      recommendations: [],
    };

    const entityNames = new Set<string>();
    const entityIds = new Set<string>();

    // 收集所有实体信息
    for (const entity of this.entities.values()) {
      // 检查重复名称
      if (entityNames.has(entity.name)) {
        report.duplicateNames.push(entity.name);
        report.isValid = false;
      }
      entityNames.add(entity.name);
      entityIds.add(entity.id);
    }

    // 检查关系完整性
    for (const entity of this.entities.values()) {
      for (const relationship of entity.relationships) {
        // 检查目标实体是否存在
        if (!entityIds.has(relationship.targetEntityId)) {
          report.missingEntities.push(relationship.targetEntityId);
          report.isValid = false;
        }

        // 检查循环引用
        if (this.hasCircularReference(entity.id, relationship.targetEntityId)) {
          const circularRef = `${entity.name} -> ${this.entities.get(relationship.targetEntityId)?.name || 'Unknown'}`;
          if (!report.circularReferences.includes(circularRef)) {
            report.circularReferences.push(circularRef);
          }
        }
      }

      // 检查外键字段
      for (const field of entity.fields) {
        if (field.type === DataFieldType.REFERENCE) {
          const hasRelationship = entity.relationships.some(r => r.foreignKey === field.name);
          if (!hasRelationship) {
            report.orphanedReferences.push(`${entity.name}.${field.name}`);
          }
        }
      }
    }

    // 生成建议
    if (report.duplicateNames.length > 0) {
      report.recommendations.push('Rename duplicate entities to have unique names');
    }

    if (report.orphanedReferences.length > 0) {
      report.recommendations.push('Add relationships for orphaned reference fields');
    }

    if (report.circularReferences.length > 0) {
      report.recommendations.push('Consider breaking circular references to improve data model clarity');
    }

    if (report.missingEntities.length > 0) {
      report.recommendations.push('Create missing target entities or remove invalid relationships');
    }

    return report;
  }

  /**
   * 查找实体依赖
   */
  private findEntityDependencies(entityId: string): string[] {
    const dependencies: string[] = [];

    for (const entity of this.entities.values()) {
      if (entity.id !== entityId) {
        for (const relationship of entity.relationships) {
          if (relationship.targetEntityId === entityId) {
            dependencies.push(entity.name);
            break;
          }
        }
      }
    }

    return dependencies;
  }

  /**
   * 检查循环引用
   */
  private hasCircularReference(sourceId: string, targetId: string, visited: Set<string> = new Set()): boolean {
    if (sourceId === targetId) {
      return true;
    }

    if (visited.has(sourceId)) {
      return false;
    }

    visited.add(sourceId);

    const sourceEntity = this.entities.get(sourceId);
    if (!sourceEntity) {
      return false;
    }

    for (const relationship of sourceEntity.relationships) {
      if (this.hasCircularReference(relationship.targetEntityId, targetId, new Set(visited))) {
        return true;
      }
    }

    return false;
  }

  /**
   * 导出数据字典
   */
  exportDataDictionary(): Record<string, any> {
    return {
      entities: Array.from(this.entities.values()).map(entity => entity.toJSON()),
      metadata: {
        entityCount: this.entities.size,
        exportDate: new Date().toISOString(),
        version: '1.0.0',
      },
    };
  }

  /**
   * 导入数据字典
   */
  importDataDictionary(data: Record<string, any>): DataDictionaryOperationResult {
    try {
      // 清空现有数据
      this.entities.clear();
      this.entityNameIndex.clear();

      // 导入实体
      if (data.entities && Array.isArray(data.entities)) {
        for (const entityData of data.entities) {
          const entity = DataEntity.fromJSON(entityData);
          this.entities.set(entity.id, entity);
          this.entityNameIndex.set(entity.name, entity.id);
        }
      }

      // 验证导入的数据
      const integrityReport = this.checkDataIntegrity();

      const validation = new ValidationResultImpl();
      if (!integrityReport.isValid) {
        validation.addWarning(
          'Data integrity issues found after import',
          'import',
          'INTEGRITY_ISSUES'
        );
      }

      return {
        success: true,
        entities: Array.from(this.entities.values()),
        validation,
        warnings: integrityReport.recommendations,
      };
    } catch (error) {
      const validation = ValidationResultImpl.failure(
        `Failed to import data dictionary: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'import',
        'IMPORT_FAILED'
      );
      return { success: false, validation };
    }
  }

  /**
   * 清空数据字典
   */
  clear(): void {
    this.entities.clear();
    this.entityNameIndex.clear();
  }

  /**
   * 获取统计信息
   */
  getStatistics(): Record<string, any> {
    const entities = Array.from(this.entities.values());
    const totalFields = entities.reduce((sum, entity) => sum + entity.fields.length, 0);
    const totalRelationships = entities.reduce((sum, entity) => sum + entity.relationships.length, 0);

    return {
      entityCount: entities.length,
      totalFields,
      totalRelationships,
      averageFieldsPerEntity: entities.length > 0 ? totalFields / entities.length : 0,
      averageRelationshipsPerEntity: entities.length > 0 ? totalRelationships / entities.length : 0,
    };
  }
}