import * as vscode from 'vscode';
import { Blueprint } from '../models/Blueprint';
import { ParseResult, SymbolInfo } from '../parsers/BaseParser';
import { CodeClassification } from '../classifiers/LegacyCodeClassifier';
import { DependencyGraph } from '../analyzers/DependencyAnalyzer';

/**
 * 比对结果状态
 */
export type ComparisonStatus = 'match' | 'partial_match' | 'mismatch' | 'missing' | 'extra';

/**
 * 四色分类
 */
export type FourColorClassification = 'green' | 'yellow' | 'red' | 'purple';

/**
 * 比对结果接口
 */
export interface BlueprintComparison {
  fileUri: string;
  filePath: string;
  relativePath: string;
  status: ComparisonStatus;
  classification: FourColorClassification;
  confidence: number;
  matches: ComparisonMatch[];
  mismatches: ComparisonMismatch[];
  suggestions: string[];
  summary: string;
}

/**
 * 匹配项接口
 */
export interface ComparisonMatch {
  type: 'entity' | 'function' | 'class' | 'interface' | 'property' | 'method';
  blueprintItem: string;
  codeItem: string;
  confidence: number;
  location?: vscode.Range;
}

/**
 * 不匹配项接口
 */
export interface ComparisonMismatch {
  type: 'missing_in_code' | 'missing_in_blueprint' | 'type_mismatch' | 'structure_mismatch';
  severity: 'low' | 'medium' | 'high';
  description: string;
  blueprintItem?: string;
  codeItem?: string;
  suggestion: string;
  location?: vscode.Range;
}

/**
 * 比对配置接口
 */
export interface ComparisonConfig {
  strictMode: boolean;
  ignorePrivateMembers: boolean;
  ignoreTestFiles: boolean;
  minimumConfidence: number;
  enableFuzzyMatching: boolean;
  fuzzyThreshold: number;
}

/**
 * 蓝图代码比对器
 * 实现Visual Intent Contract的核心功能：将用户意图（蓝图）与实际代码进行智能比对
 */
export class BlueprintCodeComparator {
  private config: ComparisonConfig;
  private comparisons = new Map<string, BlueprintComparison>();

  constructor(config: Partial<ComparisonConfig> = {}) {
    this.config = {
      strictMode: false,
      ignorePrivateMembers: true,
      ignoreTestFiles: true,
      minimumConfidence: 0.6,
      enableFuzzyMatching: true,
      fuzzyThreshold: 0.8,
      ...config
    };
  }

  /**
   * 比对蓝图与项目代码
   */
  public async compareProject(
    blueprint: Blueprint,
    parseResults: Map<string, ParseResult>,
    codeClassifications: Map<string, CodeClassification>,
    dependencyGraph: DependencyGraph
  ): Promise<Map<string, BlueprintComparison>> {
    console.log('🔍 Starting blueprint-code comparison...');
    
    this.comparisons.clear();

    // 提取蓝图中的预期结构
    const blueprintStructure = this.extractBlueprintStructure(blueprint);
    
    for (const [fileUri, parseResult] of parseResults) {
      if (!parseResult.success) continue;

      // 跳过测试文件
      if (this.config.ignoreTestFiles && this.isTestFile(fileUri)) continue;

      const codeClassification = codeClassifications.get(fileUri);
      const comparison = await this.compareFile(
        fileUri,
        parseResult,
        blueprintStructure,
        codeClassification,
        dependencyGraph
      );
      
      this.comparisons.set(fileUri, comparison);
    }

    console.log(`✅ Blueprint comparison completed: ${this.comparisons.size} files compared`);
    
    return this.comparisons;
  }

  /**
   * 提取蓝图结构
   */
  private extractBlueprintStructure(blueprint: Blueprint): BlueprintStructure {
    const structure: BlueprintStructure = {
      entities: [],
      functions: [],
      classes: [],
      interfaces: [],
      modules: [],
      relationships: []
    };

    // 从数据实体提取
    if (blueprint.dataEntities) {
      blueprint.dataEntities.forEach(entity => {
        structure.entities.push({
          name: entity.name,
          type: 'entity',
          properties: entity.fields?.map(field => ({
            name: field.name,
            type: field.type,
            required: field.required || false
          })) || [],
          description: entity.description
        });
      });
    }

    // 从功能模块提取
    if (blueprint.modules) {
      blueprint.modules.forEach(module => {
        structure.modules.push({
          name: module.name,
          type: 'module',
          functions: (module as any).functions || [],
          description: module.description
        });

        // 提取模块中的函数
        if ((module as any).functions) {
          (module as any).functions.forEach((func: any) => {
            structure.functions.push({
              name: func.name,
              type: 'function',
              parameters: func.parameters || [],
              returnType: func.returnType,
              description: func.description,
              module: module.name
            });
          });
        }
      });
    }

    return structure;
  }

  /**
   * 比对单个文件
   */
  private async compareFile(
    fileUri: string,
    parseResult: ParseResult,
    blueprintStructure: BlueprintStructure,
    codeClassification: CodeClassification | undefined,
    dependencyGraph: DependencyGraph
  ): Promise<BlueprintComparison> {
    const filePath = vscode.Uri.parse(fileUri).fsPath;
    const relativePath = vscode.workspace.asRelativePath(filePath);

    // 提取代码结构
    const codeStructure = this.extractCodeStructure(parseResult);
    
    // 执行比对
    const matches = this.findMatches(blueprintStructure, codeStructure);
    const mismatches = this.findMismatches(blueprintStructure, codeStructure, matches);
    
    // 计算置信度
    const confidence = this.calculateConfidence(matches, mismatches, codeStructure);
    
    // 确定状态
    const status = this.determineStatus(matches, mismatches, confidence);
    
    // 确定四色分类
    const classification = this.determineFourColorClassification(
      status, 
      confidence, 
      codeClassification,
      mismatches
    );
    
    // 生成建议
    const suggestions = this.generateSuggestions(mismatches, status, classification);
    
    // 生成摘要
    const summary = this.generateSummary(status, matches, mismatches, confidence);

    return {
      fileUri,
      filePath,
      relativePath,
      status,
      classification,
      confidence,
      matches,
      mismatches,
      suggestions,
      summary
    };
  }

  /**
   * 提取代码结构
   */
  private extractCodeStructure(parseResult: ParseResult): CodeStructure {
    const structure: CodeStructure = {
      entities: [],
      functions: [],
      classes: [],
      interfaces: [],
      properties: [],
      methods: []
    };

    parseResult.symbols.forEach(symbol => {
      switch (symbol.type) {
        case 'class':
          structure.classes.push({
            name: symbol.name,
            type: 'class',
            visibility: symbol.visibility,
            location: symbol.range,
            documentation: symbol.documentation
          });
          break;
          
        case 'interface':
          structure.interfaces.push({
            name: symbol.name,
            type: 'interface',
            visibility: symbol.visibility,
            location: symbol.range,
            documentation: symbol.documentation
          });
          break;
          
        case 'function':
          structure.functions.push({
            name: symbol.name,
            type: 'function',
            parameters: symbol.parameters || [],
            returnType: symbol.returnType,
            visibility: symbol.visibility,
            isAsync: symbol.isAsync,
            location: symbol.range,
            documentation: symbol.documentation
          });
          break;
          
        case 'method':
          structure.methods.push({
            name: symbol.name,
            type: 'method',
            parameters: symbol.parameters || [],
            returnType: symbol.returnType,
            visibility: symbol.visibility,
            isAsync: symbol.isAsync,
            isStatic: symbol.isStatic,
            location: symbol.range,
            documentation: symbol.documentation
          });
          break;
          
        case 'property':
          structure.properties.push({
            name: symbol.name,
            type: 'property',
            returnType: symbol.returnType,
            visibility: symbol.visibility,
            isStatic: symbol.isStatic,
            location: symbol.range,
            documentation: symbol.documentation
          });
          break;
      }
    });

    return structure;
  }

  /**
   * 查找匹配项
   */
  private findMatches(
    blueprintStructure: BlueprintStructure,
    codeStructure: CodeStructure
  ): ComparisonMatch[] {
    const matches: ComparisonMatch[] = [];

    // 匹配类
    blueprintStructure.entities.forEach(blueprintEntity => {
      const codeClass = codeStructure.classes.find(cls => 
        this.isNameMatch(blueprintEntity.name, cls.name)
      );
      
      if (codeClass) {
        matches.push({
          type: 'class',
          blueprintItem: blueprintEntity.name,
          codeItem: codeClass.name,
          confidence: this.calculateNameSimilarity(blueprintEntity.name, codeClass.name),
          location: codeClass.location
        });
      }
    });

    // 匹配函数
    blueprintStructure.functions.forEach(blueprintFunction => {
      const codeFunction = codeStructure.functions.find(func => 
        this.isNameMatch(blueprintFunction.name, func.name)
      );
      
      if (codeFunction) {
        const parameterMatch = this.compareParameters(
          blueprintFunction.parameters, 
          codeFunction.parameters
        );
        
        matches.push({
          type: 'function',
          blueprintItem: blueprintFunction.name,
          codeItem: codeFunction.name,
          confidence: Math.min(
            this.calculateNameSimilarity(blueprintFunction.name, codeFunction.name),
            parameterMatch
          ),
          location: codeFunction.location
        });
      }
    });

    // 匹配接口
    blueprintStructure.interfaces?.forEach(blueprintInterface => {
      const codeInterface = codeStructure.interfaces.find(intf => 
        this.isNameMatch(blueprintInterface.name, intf.name)
      );
      
      if (codeInterface) {
        matches.push({
          type: 'interface',
          blueprintItem: blueprintInterface.name,
          codeItem: codeInterface.name,
          confidence: this.calculateNameSimilarity(blueprintInterface.name, codeInterface.name),
          location: codeInterface.location
        });
      }
    });

    return matches.filter(match => match.confidence >= this.config.minimumConfidence);
  }

  /**
   * 查找不匹配项
   */
  private findMismatches(
    blueprintStructure: BlueprintStructure,
    codeStructure: CodeStructure,
    matches: ComparisonMatch[]
  ): ComparisonMismatch[] {
    const mismatches: ComparisonMismatch[] = [];
    const matchedBlueprintItems = new Set(matches.map(m => m.blueprintItem));
    const matchedCodeItems = new Set(matches.map(m => m.codeItem));

    // 蓝图中有但代码中缺失的项
    blueprintStructure.entities.forEach(entity => {
      if (!matchedBlueprintItems.has(entity.name)) {
        mismatches.push({
          type: 'missing_in_code',
          severity: 'high',
          description: `Entity '${entity.name}' defined in blueprint but not found in code`,
          blueprintItem: entity.name,
          suggestion: `Implement the '${entity.name}' entity as defined in the blueprint`
        });
      }
    });

    blueprintStructure.functions.forEach(func => {
      if (!matchedBlueprintItems.has(func.name)) {
        mismatches.push({
          type: 'missing_in_code',
          severity: 'medium',
          description: `Function '${func.name}' defined in blueprint but not found in code`,
          blueprintItem: func.name,
          suggestion: `Implement the '${func.name}' function as defined in the blueprint`
        });
      }
    });

    // 代码中有但蓝图中缺失的项（可能是额外实现）
    if (!this.config.strictMode) {
      codeStructure.classes.forEach(cls => {
        if (!matchedCodeItems.has(cls.name) && cls.visibility === 'public') {
          mismatches.push({
            type: 'missing_in_blueprint',
            severity: 'low',
            description: `Class '${cls.name}' exists in code but not defined in blueprint`,
            codeItem: cls.name,
            suggestion: `Consider adding '${cls.name}' to the blueprint or mark as implementation detail`,
            location: cls.location
          });
        }
      });
    }

    return mismatches;
  }

  /**
   * 计算置信度
   */
  private calculateConfidence(
    matches: ComparisonMatch[],
    mismatches: ComparisonMismatch[],
    codeStructure: CodeStructure
  ): number {
    if (matches.length === 0 && mismatches.length === 0) return 1.0;

    const totalItems = matches.length + mismatches.filter(m => m.type === 'missing_in_code').length;
    if (totalItems === 0) return 1.0;

    const matchScore = matches.reduce((sum, match) => sum + match.confidence, 0);
    const mismatchPenalty = mismatches.reduce((sum, mismatch) => {
      switch (mismatch.severity) {
        case 'high': return sum + 0.3;
        case 'medium': return sum + 0.2;
        case 'low': return sum + 0.1;
        default: return sum;
      }
    }, 0);

    const confidence = Math.max(0, (matchScore - mismatchPenalty) / totalItems);
    return Math.min(1.0, confidence);
  }

  /**
   * 确定状态
   */
  private determineStatus(
    matches: ComparisonMatch[],
    mismatches: ComparisonMismatch[],
    confidence: number
  ): ComparisonStatus {
    const highSeverityMismatches = mismatches.filter(m => m.severity === 'high').length;
    const missingInCode = mismatches.filter(m => m.type === 'missing_in_code').length;

    if (confidence >= 0.9 && highSeverityMismatches === 0) {
      return 'match';
    } else if (confidence >= 0.7 && highSeverityMismatches <= 1) {
      return 'partial_match';
    } else if (missingInCode > matches.length) {
      return 'missing';
    } else if (confidence < 0.3) {
      return 'mismatch';
    } else {
      return 'partial_match';
    }
  }

  /**
   * 确定四色分类
   */
  private determineFourColorClassification(
    status: ComparisonStatus,
    confidence: number,
    codeClassification: CodeClassification | undefined,
    mismatches: ComparisonMismatch[]
  ): FourColorClassification {
    const highSeverityMismatches = mismatches.filter(m => m.severity === 'high').length;
    const mediumSeverityMismatches = mismatches.filter(m => m.severity === 'medium').length;

    // 紫色：高质量代码但与蓝图不匹配（可能是合理的实现差异）
    if (codeClassification?.classification === 'green_zone' && status === 'partial_match' && confidence > 0.6) {
      return 'purple';
    }

    // 红色：严重不匹配或缺失关键功能
    if (status === 'mismatch' || status === 'missing' || highSeverityMismatches > 2) {
      return 'red';
    }

    // 黄色：部分匹配但需要关注
    if (status === 'partial_match' || mediumSeverityMismatches > 1 || confidence < 0.7) {
      return 'yellow';
    }

    // 绿色：完全匹配或高度匹配
    return 'green';
  }

  /**
   * 生成建议
   */
  private generateSuggestions(
    mismatches: ComparisonMismatch[],
    status: ComparisonStatus,
    classification: FourColorClassification
  ): string[] {
    const suggestions: string[] = [];

    // 基于不匹配项生成建议
    mismatches.forEach(mismatch => {
      if (!suggestions.includes(mismatch.suggestion)) {
        suggestions.push(mismatch.suggestion);
      }
    });

    // 基于分类生成通用建议
    switch (classification) {
      case 'red':
        suggestions.push('Priority: High - Immediate attention required to align with blueprint');
        suggestions.push('Review blueprint requirements and implement missing functionality');
        break;
      case 'yellow':
        suggestions.push('Priority: Medium - Review and improve alignment with blueprint');
        suggestions.push('Consider refactoring to better match intended design');
        break;
      case 'purple':
        suggestions.push('Priority: Low - Code quality is good but differs from blueprint');
        suggestions.push('Verify if implementation differences are intentional and beneficial');
        break;
      case 'green':
        suggestions.push('Excellent alignment with blueprint - maintain current implementation');
        break;
    }

    return suggestions;
  }

  /**
   * 生成摘要
   */
  private generateSummary(
    status: ComparisonStatus,
    matches: ComparisonMatch[],
    mismatches: ComparisonMismatch[],
    confidence: number
  ): string {
    const statusText = {
      'match': 'Fully matches blueprint',
      'partial_match': 'Partially matches blueprint',
      'mismatch': 'Does not match blueprint',
      'missing': 'Missing from implementation',
      'extra': 'Extra implementation'
    }[status];

    let summary = `${statusText} (${(confidence * 100).toFixed(1)}% confidence). `;
    summary += `Found ${matches.length} matches`;
    
    if (mismatches.length > 0) {
      const highSeverity = mismatches.filter(m => m.severity === 'high').length;
      const mediumSeverity = mismatches.filter(m => m.severity === 'medium').length;
      const lowSeverity = mismatches.filter(m => m.severity === 'low').length;
      
      summary += ` and ${mismatches.length} issues`;
      if (highSeverity > 0) summary += ` (${highSeverity} high severity)`;
      if (mediumSeverity > 0) summary += ` (${mediumSeverity} medium severity)`;
      if (lowSeverity > 0) summary += ` (${lowSeverity} low severity)`;
    }

    return summary;
  }

  /**
   * 检查名称匹配
   */
  private isNameMatch(blueprintName: string, codeName: string): boolean {
    if (blueprintName === codeName) return true;
    
    if (this.config.enableFuzzyMatching) {
      return this.calculateNameSimilarity(blueprintName, codeName) >= this.config.fuzzyThreshold;
    }
    
    return false;
  }

  /**
   * 计算名称相似度
   */
  private calculateNameSimilarity(name1: string, name2: string): number {
    const normalize = (str: string) => str.toLowerCase().replace(/[_-]/g, '');
    const n1 = normalize(name1);
    const n2 = normalize(name2);
    
    if (n1 === n2) return 1.0;
    
    // 简单的编辑距离算法
    const maxLen = Math.max(n1.length, n2.length);
    if (maxLen === 0) return 1.0;
    
    const distance = this.levenshteinDistance(n1, n2);
    return 1 - (distance / maxLen);
  }

  /**
   * 计算编辑距离
   */
  private levenshteinDistance(str1: string, str2: string): number {
    const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));
    
    for (let i = 0; i <= str1.length; i++) matrix[0][i] = i;
    for (let j = 0; j <= str2.length; j++) matrix[j][0] = j;
    
    for (let j = 1; j <= str2.length; j++) {
      for (let i = 1; i <= str1.length; i++) {
        const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
        matrix[j][i] = Math.min(
          matrix[j][i - 1] + 1,     // deletion
          matrix[j - 1][i] + 1,     // insertion
          matrix[j - 1][i - 1] + indicator // substitution
        );
      }
    }
    
    return matrix[str2.length][str1.length];
  }

  /**
   * 比较参数
   */
  private compareParameters(blueprintParams: any[], codeParams: any[]): number {
    if (blueprintParams.length === 0 && codeParams.length === 0) return 1.0;
    if (blueprintParams.length === 0 || codeParams.length === 0) return 0.5;
    
    const maxLen = Math.max(blueprintParams.length, codeParams.length);
    let matches = 0;
    
    for (let i = 0; i < Math.min(blueprintParams.length, codeParams.length); i++) {
      if (blueprintParams[i].name === codeParams[i].name) {
        matches++;
      }
    }
    
    return matches / maxLen;
  }

  /**
   * 检查是否为测试文件
   */
  private isTestFile(fileUri: string): boolean {
    const filePath = vscode.Uri.parse(fileUri).fsPath.toLowerCase();
    return filePath.includes('.test.') || 
           filePath.includes('.spec.') || 
           filePath.includes('/test/') || 
           filePath.includes('\\test\\');
  }

  /**
   * 获取比对结果
   */
  public getComparisons(): Map<string, BlueprintComparison> {
    return this.comparisons;
  }

  /**
   * 获取比对统计
   */
  public getComparisonStats(): {
    total: number;
    green: number;
    yellow: number;
    red: number;
    purple: number;
    averageConfidence: number;
    statusDistribution: { [key in ComparisonStatus]: number };
  } {
    const comparisons = Array.from(this.comparisons.values());
    const total = comparisons.length;

    return {
      total,
      green: comparisons.filter(c => c.classification === 'green').length,
      yellow: comparisons.filter(c => c.classification === 'yellow').length,
      red: comparisons.filter(c => c.classification === 'red').length,
      purple: comparisons.filter(c => c.classification === 'purple').length,
      averageConfidence: total > 0 ? comparisons.reduce((sum, c) => sum + c.confidence, 0) / total : 0,
      statusDistribution: {
        match: comparisons.filter(c => c.status === 'match').length,
        partial_match: comparisons.filter(c => c.status === 'partial_match').length,
        mismatch: comparisons.filter(c => c.status === 'mismatch').length,
        missing: comparisons.filter(c => c.status === 'missing').length,
        extra: comparisons.filter(c => c.status === 'extra').length
      }
    };
  }

  /**
   * 生成比对报告
   */
  public generateReport(): string {
    const stats = this.getComparisonStats();
    const comparisons = Array.from(this.comparisons.values());

    let report = '🔍 Blueprint-Code Comparison Report\n';
    report += '===================================\n\n';

    report += `📊 Overall Statistics:\n`;
    report += `  • Total Files: ${stats.total}\n`;
    report += `  • Average Confidence: ${(stats.averageConfidence * 100).toFixed(1)}%\n\n`;

    report += `🎯 Classification Distribution:\n`;
    report += `  • 🟢 Green (Perfect Match): ${stats.green} files (${((stats.green / stats.total) * 100).toFixed(1)}%)\n`;
    report += `  • 🟡 Yellow (Needs Attention): ${stats.yellow} files (${((stats.yellow / stats.total) * 100).toFixed(1)}%)\n`;
    report += `  • 🔴 Red (Major Issues): ${stats.red} files (${((stats.red / stats.total) * 100).toFixed(1)}%)\n`;
    report += `  • 🟣 Purple (Good Code, Different Design): ${stats.purple} files (${((stats.purple / stats.total) * 100).toFixed(1)}%)\n\n`;

    // 显示需要立即关注的文件
    const redFiles = comparisons.filter(c => c.classification === 'red')
      .sort((a, b) => a.confidence - b.confidence)
      .slice(0, 5);

    if (redFiles.length > 0) {
      report += `🚨 Files Requiring Immediate Attention:\n`;
      redFiles.forEach((file, index) => {
        report += `  ${index + 1}. ${file.relativePath} (${(file.confidence * 100).toFixed(1)}% match)\n`;
        report += `     ${file.summary}\n`;
      });
      report += '\n';
    }

    // 显示完美匹配的文件
    const greenFiles = comparisons.filter(c => c.classification === 'green')
      .sort((a, b) => b.confidence - a.confidence)
      .slice(0, 3);

    if (greenFiles.length > 0) {
      report += `✅ Perfect Blueprint Matches:\n`;
      greenFiles.forEach((file, index) => {
        report += `  ${index + 1}. ${file.relativePath} (${(file.confidence * 100).toFixed(1)}% match)\n`;
      });
    }

    return report;
  }

  /**
   * 获取配置
   */
  public getConfig(): ComparisonConfig {
    return { ...this.config };
  }

  /**
   * 更新配置
   */
  public updateConfig(newConfig: Partial<ComparisonConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * 清理资源
   */
  public dispose(): void {
    this.comparisons.clear();
  }
}

// 辅助接口
interface BlueprintStructure {
  entities: Array<{
    name: string;
    type: string;
    properties: Array<{
      name: string;
      type: string;
      required: boolean;
    }>;
    description?: string;
  }>;
  functions: Array<{
    name: string;
    type: string;
    parameters: any[];
    returnType?: string;
    description?: string;
    module?: string;
  }>;
  classes: Array<{
    name: string;
    type: string;
    description?: string;
  }>;
  interfaces: Array<{
    name: string;
    type: string;
    description?: string;
  }>;
  modules: Array<{
    name: string;
    type: string;
    functions: any[];
    description?: string;
  }>;
  relationships: Array<{
    from: string;
    to: string;
    type: string;
  }>;
}

interface CodeStructure {
  entities: any[];
  functions: Array<{
    name: string;
    type: string;
    parameters: any[];
    returnType?: string;
    visibility: string;
    isAsync?: boolean;
    location?: vscode.Range;
    documentation?: string;
  }>;
  classes: Array<{
    name: string;
    type: string;
    visibility: string;
    location?: vscode.Range;
    documentation?: string;
  }>;
  interfaces: Array<{
    name: string;
    type: string;
    visibility: string;
    location?: vscode.Range;
    documentation?: string;
  }>;
  properties: Array<{
    name: string;
    type: string;
    returnType?: string;
    visibility: string;
    isStatic?: boolean;
    location?: vscode.Range;
    documentation?: string;
  }>;
  methods: Array<{
    name: string;
    type: string;
    parameters: any[];
    returnType?: string;
    visibility: string;
    isAsync?: boolean;
    isStatic?: boolean;
    location?: vscode.Range;
    documentation?: string;
  }>;
}