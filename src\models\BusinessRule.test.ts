import { describe, it, expect } from 'vitest';
import { BusinessRule } from './BusinessRule';
import { BusinessRuleType, LogicOperator } from './enums';

describe('BusinessRule', () => {
  it('should create a business rule with default values', () => {
    const rule = new BusinessRule();

    expect(rule.id).toBeDefined();
    expect(rule.name).toBe('');
    expect(rule.type).toBe(BusinessRuleType.VALIDATION);
    expect(rule.priority).toBe(0);
    expect(rule.isActive).toBe(true);
  });

  it('should create a business rule with provided data', () => {
    const ruleData = {
      name: 'Email Validation',
      description: 'Validate email format',
      type: BusinessRuleType.VALIDATION,
      priority: 10,
    };

    const rule = new BusinessRule(ruleData);

    expect(rule.name).toBe('Email Validation');
    expect(rule.description).toBe('Validate email format');
    expect(rule.type).toBe(BusinessRuleType.VALIDATION);
    expect(rule.priority).toBe(10);
  });

  it('should validate rule successfully', () => {
    const rule = new BusinessRule({
      name: 'Valid Rule',
      priority: 50,
    });

    const validation = rule.validate();

    expect(validation.isValid).toBe(true);
    expect(validation.errors).toHaveLength(0);
  });

  it('should fail validation for empty name', () => {
    const rule = new BusinessRule({ name: '' });

    const validation = rule.validate();

    expect(validation.isValid).toBe(false);
    expect(validation.errors.some((e) => e.code === 'REQUIRED')).toBe(true);
  });

  it('should fail validation for invalid priority', () => {
    const rule = new BusinessRule({
      name: 'Test Rule',
      priority: 150, // Invalid priority > 100
    });

    const validation = rule.validate();

    expect(validation.isValid).toBe(false);
    expect(validation.errors.some((e) => e.code === 'INVALID_RANGE')).toBe(
      true
    );
  });

  it('should evaluate simple condition correctly', () => {
    const rule = new BusinessRule({
      name: 'Test Rule',
      condition: {
        operator: LogicOperator.EQUALS,
        operands: [
          { type: 'FIELD', value: 'status' },
          { type: 'CONSTANT', value: 'active' },
        ],
      },
    });

    const context = { status: 'active' };
    const result = rule.evaluate(context);

    expect(result).toBe(true);
  });

  it('should evaluate complex AND condition correctly', () => {
    const rule = new BusinessRule({
      name: 'Test Rule',
      condition: {
        operator: LogicOperator.AND,
        operands: [
          {
            operator: LogicOperator.EQUALS,
            operands: [
              { type: 'FIELD', value: 'status' },
              { type: 'CONSTANT', value: 'active' },
            ],
          },
          {
            operator: LogicOperator.EQUALS,
            operands: [
              { type: 'FIELD', value: 'verified' },
              { type: 'CONSTANT', value: true },
            ],
          },
        ],
      },
    });

    const context = { status: 'active', verified: true };
    const result = rule.evaluate(context);

    expect(result).toBe(true);

    const contextFalse = { status: 'active', verified: false };
    const resultFalse = rule.evaluate(contextFalse);

    expect(resultFalse).toBe(false);
  });

  it('should execute SET_VALUE action correctly', () => {
    const rule = new BusinessRule({
      name: 'Test Rule',
      action: {
        type: 'SET_VALUE',
        target: 'result',
        value: 'processed',
      },
    });

    const context = { status: 'pending' };
    const result = rule.executeAction(context);

    expect(result.result).toBe('processed');
    expect(result.status).toBe('pending'); // Original value preserved
  });

  it('should detect conflicts between rules', () => {
    const rule1 = new BusinessRule({
      name: 'Rule 1',
      priority: 10,
      action: {
        type: 'SET_VALUE',
        target: 'status',
        value: 'approved',
      },
    });

    const rule2 = new BusinessRule({
      name: 'Rule 2',
      priority: 10, // Same priority
      action: {
        type: 'SET_VALUE',
        target: 'status', // Same target
        value: 'rejected', // Different value
      },
    });

    const conflicts = rule1.detectConflicts([rule2]);

    expect(conflicts).toHaveLength(1);
    expect(conflicts[0].conflictType).toBe('PRIORITY_CONFLICT');
  });

  it('should serialize to JSON and deserialize correctly', () => {
    const rule = new BusinessRule({
      name: 'Test Rule',
      description: 'Test description',
      type: BusinessRuleType.WORKFLOW,
      priority: 25,
    });

    const json = rule.toJSON();
    const deserialized = BusinessRule.fromJSON(json);

    expect(deserialized.name).toBe(rule.name);
    expect(deserialized.description).toBe(rule.description);
    expect(deserialized.type).toBe(rule.type);
    expect(deserialized.priority).toBe(rule.priority);
  });

  it('should clone rule correctly', () => {
    const rule = new BusinessRule({
      name: 'Original Rule',
      priority: 30,
    });

    const cloned = rule.clone();

    expect(cloned.id).toBe(rule.id);
    expect(cloned.name).toBe(rule.name);
    expect(cloned.priority).toBe(rule.priority);

    // Ensure it's a deep clone
    cloned.name = 'Modified Rule';
    expect(rule.name).toBe('Original Rule');
  });
});
