import { Language, NodeStatus } from './enums';

/**
 * 项目结构接口
 */
export interface ProjectStructure {
  rootPath: string;
  files: FileStructure[];
  directories: DirectoryStructure[];
  packageInfo?: PackageInfo;
  buildConfig?: BuildConfig;
}

/**
 * 文件结构接口
 */
export interface FileStructure {
  path: string;
  name: string;
  extension: string;
  size: number;
  language: Language;
  lastModified: Date;
  content?: string;
  analysis?: FileAnalysis;
}

/**
 * 目录结构接口
 */
export interface DirectoryStructure {
  path: string;
  name: string;
  files: FileStructure[];
  subdirectories: DirectoryStructure[];
}

/**
 * 包信息接口
 */
export interface PackageInfo {
  name: string;
  version: string;
  dependencies: Record<string, string>;
  devDependencies: Record<string, string>;
  scripts: Record<string, string>;
}

/**
 * 构建配置接口
 */
export interface BuildConfig {
  type: 'webpack' | 'vite' | 'rollup' | 'parcel' | 'custom';
  entryPoints: string[];
  outputPath: string;
  configFile?: string;
}

/**
 * 文件分析接口
 */
export interface FileAnalysis {
  language: Language;
  functions: FunctionInfo[];
  classes: ClassInfo[];
  imports: ImportInfo[];
  exports: ExportInfo[];
  dependencies: string[];
  complexity: ComplexityMetrics;
  issues: CodeIssue[];
  coverage?: CoverageInfo;
}

/**
 * 函数信息接口
 */
export interface FunctionInfo {
  name: string;
  startLine: number;
  endLine: number;
  parameters: ParameterInfo[];
  returnType?: string;
  isAsync: boolean;
  isExported: boolean;
  complexity: number;
  documentation?: string;
}

/**
 * 参数信息接口
 */
export interface ParameterInfo {
  name: string;
  type?: string;
  optional: boolean;
  defaultValue?: any;
}

/**
 * 类信息接口
 */
export interface ClassInfo {
  name: string;
  startLine: number;
  endLine: number;
  methods: FunctionInfo[];
  properties: PropertyInfo[];
  extends?: string;
  implements?: string[];
  isExported: boolean;
  documentation?: string;
}

/**
 * 属性信息接口
 */
export interface PropertyInfo {
  name: string;
  type?: string;
  visibility: 'public' | 'private' | 'protected';
  isStatic: boolean;
  isReadonly: boolean;
}

/**
 * 导入信息接口
 */
export interface ImportInfo {
  source: string;
  imports: string[];
  isDefault: boolean;
  isNamespace: boolean;
  line: number;
}

/**
 * 导出信息接口
 */
export interface ExportInfo {
  name: string;
  type: 'function' | 'class' | 'variable' | 'type';
  isDefault: boolean;
  line: number;
}

/**
 * 复杂度指标接口
 */
export interface ComplexityMetrics {
  cyclomaticComplexity: number;
  cognitiveComplexity: number;
  linesOfCode: number;
  maintainabilityIndex: number;
}

/**
 * 代码问题接口
 */
export interface CodeIssue {
  id: string;
  type: 'ERROR' | 'WARNING' | 'INFO' | 'SUGGESTION';
  severity: 'HIGH' | 'MEDIUM' | 'LOW';
  message: string;
  line: number;
  column?: number;
  rule?: string;
  fixable: boolean;
  suggestedFix?: string;
}

/**
 * 覆盖率信息接口
 */
export interface CoverageInfo {
  lines: CoverageMetric;
  functions: CoverageMetric;
  branches: CoverageMetric;
  statements: CoverageMetric;
}

/**
 * 覆盖率指标接口
 */
export interface CoverageMetric {
  total: number;
  covered: number;
  percentage: number;
}

/**
 * 依赖图接口
 */
export interface DependencyGraph {
  nodes: DependencyNode[];
  edges: DependencyEdge[];
  circularDependencies: CircularDependency[];
}

/**
 * 依赖节点接口
 */
export interface DependencyNode {
  id: string;
  name: string;
  type: 'FILE' | 'MODULE' | 'PACKAGE';
  path: string;
  language: Language;
}

/**
 * 依赖边接口
 */
export interface DependencyEdge {
  from: string;
  to: string;
  type: 'IMPORT' | 'REQUIRE' | 'DYNAMIC_IMPORT';
  line?: number;
}

/**
 * 循环依赖接口
 */
export interface CircularDependency {
  id: string;
  nodes: string[];
  severity: 'HIGH' | 'MEDIUM' | 'LOW';
  description: string;
}

/**
 * 项目指标接口
 */
export interface ProjectMetrics {
  totalFiles: number;
  totalLines: number;
  totalFunctions: number;
  totalClasses: number;
  averageComplexity: number;
  technicalDebt: TechnicalDebt;
  qualityGate: QualityGate;
}

/**
 * 技术债务接口
 */
export interface TechnicalDebt {
  totalMinutes: number;
  highPriorityIssues: number;
  mediumPriorityIssues: number;
  lowPriorityIssues: number;
  categories: Record<string, number>;
}

/**
 * 质量门禁接口
 */
export interface QualityGate {
  passed: boolean;
  score: number;
  criteria: QualityCriterion[];
}

/**
 * 质量标准接口
 */
export interface QualityCriterion {
  name: string;
  threshold: number;
  actualValue: number;
  passed: boolean;
  weight: number;
}

/**
 * 遗留代码区域接口
 */
export interface LegacyArea {
  id: string;
  path: string;
  type: 'CLEAR' | 'AMBIGUOUS' | 'BLACK_BOX';
  status: NodeStatus;
  description: string;
  files: string[];
  complexity: number;
  reformationPlan?: ReformationPlan;
}

/**
 * 改造计划接口
 */
export interface ReformationPlan {
  id: string;
  priority: 'HIGH' | 'MEDIUM' | 'LOW';
  estimatedEffort: number;
  steps: ReformationStep[];
  dependencies: string[];
  risks: string[];
}

/**
 * 改造步骤接口
 */
export interface ReformationStep {
  id: string;
  description: string;
  type: 'REFACTOR' | 'REWRITE' | 'EXTRACT' | 'MODERNIZE';
  estimatedHours: number;
  prerequisites: string[];
}

/**
 * 实现信息接口
 */
export interface Implementation {
  id: string;
  moduleId: string;
  files: string[];
  functions: FunctionInfo[];
  classes: ClassInfo[];
  status: NodeStatus;
  completeness: number; // 0-100
  quality: QualityMetrics;
  issues: CodeIssue[];
  lastAnalyzed: Date;
}

/**
 * 质量指标接口
 */
export interface QualityMetrics {
  maintainability: number;
  reliability: number;
  security: number;
  performance: number;
  testability: number;
  overall: number;
}

/**
 * 代码现实类
 * 表示项目的实际代码状态，用于与蓝图进行对比
 */
export class CodeReality {
  public projectStructure: ProjectStructure;
  public implementations: Implementation[];
  public dependencies: DependencyGraph;
  public issues: CodeIssue[];
  public metrics: ProjectMetrics;
  public legacyAreas: LegacyArea[];
  public lastAnalyzed: Date;

  constructor(data: Partial<CodeReality> = {}) {
    this.projectStructure =
      data.projectStructure || this.createEmptyProjectStructure();
    this.implementations = data.implementations || [];
    this.dependencies = data.dependencies || this.createEmptyDependencyGraph();
    this.issues = data.issues || [];
    this.metrics = data.metrics || this.createEmptyMetrics();
    this.legacyAreas = data.legacyAreas || [];
    this.lastAnalyzed = data.lastAnalyzed || new Date();
  }

  /**
   * 添加实现
   */
  addImplementation(implementation: Implementation): void {
    this.implementations.push(implementation);
    this.touch();
  }

  /**
   * 更新实现
   */
  updateImplementation(
    implementationId: string,
    updates: Partial<Implementation>
  ): boolean {
    const index = this.implementations.findIndex(
      (impl) => impl.id === implementationId
    );
    if (index === -1) {
      return false;
    }

    this.implementations[index] = {
      ...this.implementations[index],
      ...updates,
      lastAnalyzed: new Date(),
    };
    this.touch();
    return true;
  }

  /**
   * 删除实现
   */
  removeImplementation(implementationId: string): boolean {
    const initialLength = this.implementations.length;
    this.implementations = this.implementations.filter(
      (impl) => impl.id !== implementationId
    );
    if (this.implementations.length < initialLength) {
      this.touch();
      return true;
    }
    return false;
  }

  /**
   * 根据模块ID查找实现
   */
  getImplementationsByModule(moduleId: string): Implementation[] {
    return this.implementations.filter((impl) => impl.moduleId === moduleId);
  }

  /**
   * 添加遗留代码区域
   */
  addLegacyArea(area: LegacyArea): void {
    this.legacyAreas.push(area);
    this.touch();
  }

  /**
   * 更新遗留代码区域状态
   */
  updateLegacyAreaStatus(areaId: string, status: NodeStatus): boolean {
    const area = this.legacyAreas.find((a) => a.id === areaId);
    if (!area) {
      return false;
    }

    area.status = status;
    this.touch();
    return true;
  }

  /**
   * 获取高优先级问题
   */
  getHighPriorityIssues(): CodeIssue[] {
    return this.issues.filter((issue) => issue.severity === 'HIGH');
  }

  /**
   * 获取可修复的问题
   */
  getFixableIssues(): CodeIssue[] {
    return this.issues.filter((issue) => issue.fixable);
  }

  /**
   * 计算整体质量分数
   */
  calculateQualityScore(): number {
    if (this.implementations.length === 0) {
      return 0;
    }

    const totalScore = this.implementations.reduce(
      (sum, impl) => sum + impl.quality.overall,
      0
    );
    return totalScore / this.implementations.length;
  }

  /**
   * 获取技术债务总量
   */
  getTechnicalDebtMinutes(): number {
    return this.metrics.technicalDebt.totalMinutes;
  }

  /**
   * 检查质量门禁是否通过
   */
  isQualityGatePassed(): boolean {
    return this.metrics.qualityGate.passed;
  }

  /**
   * 获取循环依赖
   */
  getCircularDependencies(): CircularDependency[] {
    return this.dependencies.circularDependencies;
  }

  /**
   * 获取文件分析结果
   */
  getFileAnalysis(filePath: string): FileAnalysis | undefined {
    const file = this.findFileByPath(filePath);
    return file?.analysis;
  }

  /**
   * 根据路径查找文件
   */
  private findFileByPath(filePath: string): FileStructure | undefined {
    return (
      this.findFileRecursive(this.projectStructure.files, filePath) ||
      this.findFileInDirectories(this.projectStructure.directories, filePath)
    );
  }

  /**
   * 递归查找文件
   */
  private findFileRecursive(
    files: FileStructure[],
    filePath: string
  ): FileStructure | undefined {
    return files.find((file) => file.path === filePath);
  }

  /**
   * 在目录中查找文件
   */
  private findFileInDirectories(
    directories: DirectoryStructure[],
    filePath: string
  ): FileStructure | undefined {
    for (const dir of directories) {
      const found =
        this.findFileRecursive(dir.files, filePath) ||
        this.findFileInDirectories(dir.subdirectories, filePath);
      if (found) {
        return found;
      }
    }
    return undefined;
  }

  /**
   * 创建空的项目结构
   */
  private createEmptyProjectStructure(): ProjectStructure {
    return {
      rootPath: '',
      files: [],
      directories: [],
    };
  }

  /**
   * 创建空的依赖图
   */
  private createEmptyDependencyGraph(): DependencyGraph {
    return {
      nodes: [],
      edges: [],
      circularDependencies: [],
    };
  }

  /**
   * 创建空的项目指标
   */
  private createEmptyMetrics(): ProjectMetrics {
    return {
      totalFiles: 0,
      totalLines: 0,
      totalFunctions: 0,
      totalClasses: 0,
      averageComplexity: 0,
      technicalDebt: {
        totalMinutes: 0,
        highPriorityIssues: 0,
        mediumPriorityIssues: 0,
        lowPriorityIssues: 0,
        categories: {},
      },
      qualityGate: {
        passed: true,
        score: 100,
        criteria: [],
      },
    };
  }

  /**
   * 序列化为JSON
   */
  toJSON(): Record<string, any> {
    return {
      projectStructure: this.projectStructure,
      implementations: this.implementations.map((impl) => ({
        ...impl,
        lastAnalyzed: impl.lastAnalyzed.toISOString(),
      })),
      dependencies: this.dependencies,
      issues: this.issues,
      metrics: this.metrics,
      legacyAreas: this.legacyAreas,
      lastAnalyzed: this.lastAnalyzed.toISOString(),
    };
  }

  /**
   * 从JSON反序列化
   */
  static fromJSON(json: Record<string, any>): CodeReality {
    return new CodeReality({
      ...json,
      implementations:
        json.implementations?.map((impl: any) => ({
          ...impl,
          lastAnalyzed: new Date(impl.lastAnalyzed),
        })) || [],
      lastAnalyzed: new Date(json.lastAnalyzed),
    });
  }

  /**
   * 更新时间戳
   */
  private touch(): void {
    this.lastAnalyzed = new Date();
  }
}
