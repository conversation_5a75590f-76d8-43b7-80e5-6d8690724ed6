// 简单的导入测试
const fs = require('fs');
const path = require('path');

const filePath = path.join(__dirname, 'src/engines/ConstitutionalEngine.ts');
const content = fs.readFileSync(filePath, 'utf8');

console.log('File size:', content.length);
console.log('First 200 chars:', content.substring(0, 200));
console.log('Has export class:', content.includes('export class'));
console.log('Has export interface:', content.includes('export interface'));