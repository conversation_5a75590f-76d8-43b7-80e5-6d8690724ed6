import { describe, it, expect, vi, beforeEach } from 'vitest';
import { WhiteboardProvider } from './WhiteboardProvider';
import * as vscode from 'vscode';

// Mock vscode module
const mockContext = {
  extensionUri: { fsPath: '/mock/path' },
  subscriptions: [],
} as unknown;

vi.mock('vscode', () => ({
  window: {
    createWebviewPanel: vi.fn(() => ({
      webview: { html: '' },
      reveal: vi.fn(),
      onDidDispose: vi.fn(),
      dispose: vi.fn(),
    })),
    showInformationMessage: vi.fn(),
  },
  ViewColumn: {
    One: 1,
  },
  TreeItem: class MockTreeItem {
    label: string;
    collapsibleState?: number;
    constructor(label: string, collapsibleState?: number) {
      this.label = label;
      this.collapsibleState = collapsibleState;
    }
  },
  Uri: {
    joinPath: vi.fn(),
  },
}));

describe('WhiteboardProvider', () => {
  let provider: WhiteboardProvider;

  beforeEach(() => {
    provider = new WhiteboardProvider(mockContext as vscode.ExtensionContext);
  });

  it('should create instance', () => {
    expect(provider).toBeDefined();
  });

  it('should have openWhiteboard method', () => {
    expect(typeof provider.openWhiteboard).toBe('function');
  });

  it('should have createBlueprint method', () => {
    expect(typeof provider.createBlueprint).toBe('function');
  });

  it('should have dispose method', () => {
    expect(typeof provider.dispose).toBe('function');
  });
});
