import {
  BaseEntity,
  DataField,
  EntityRelationship,
  ValidationRule,
  DataConstraint,
} from './interfaces';
import { DataFieldType } from './enums';
import { ValidationResult, ValidationResultImpl } from './ValidationResult';

/**
 * 数据实体类
 * 定义项目中的数据结构，作为"宪法"的重要组成部分
 */
export class DataEntity implements BaseEntity {
  public id: string;
  public name: string;
  public description?: string;
  public createdAt: Date;
  public updatedAt: Date;
  public version: string;
  public fields: DataField[];
  public relationships: EntityRelationship[];
  public validationRules: ValidationRule[];
  public constraints: DataConstraint[];

  constructor(data: Partial<DataEntity> = {}) {
    this.id = data.id || this.generateId();
    this.name = data.name || '';
    this.description = data.description;
    this.createdAt = data.createdAt || new Date();
    this.updatedAt = data.updatedAt || new Date();
    this.version = data.version || '1.0.0';
    this.fields = data.fields || [];
    this.relationships = data.relationships || [];
    this.validationRules = data.validationRules || [];
    this.constraints = data.constraints || [];
  }

  /**
   * 添加字段
   */
  addField(field: Omit<DataField, 'id'>): DataField {
    const newField: DataField = {
      id: this.generateId(),
      ...field,
    };
    this.fields.push(newField);
    this.touch();
    return newField;
  }

  /**
   * 更新字段
   */
  updateField(fieldId: string, updates: Partial<DataField>): boolean {
    const fieldIndex = this.fields.findIndex((f) => f.id === fieldId);
    if (fieldIndex === -1) {
      return false;
    }

    this.fields[fieldIndex] = {
      ...this.fields[fieldIndex],
      ...updates,
    };
    this.touch();
    return true;
  }

  /**
   * 删除字段
   */
  removeField(fieldId: string): boolean {
    const initialLength = this.fields.length;
    this.fields = this.fields.filter((f) => f.id !== fieldId);
    if (this.fields.length < initialLength) {
      this.touch();
      return true;
    }
    return false;
  }

  /**
   * 根据名称查找字段
   */
  getFieldByName(name: string): DataField | undefined {
    return this.fields.find((f) => f.name === name);
  }

  /**
   * 添加关系
   */
  addRelationship(
    relationship: Omit<EntityRelationship, 'id'>
  ): EntityRelationship {
    const newRelationship: EntityRelationship = {
      id: this.generateId(),
      ...relationship,
    };
    this.relationships.push(newRelationship);
    this.touch();
    return newRelationship;
  }

  /**
   * 删除关系
   */
  removeRelationship(relationshipId: string): boolean {
    const initialLength = this.relationships.length;
    this.relationships = this.relationships.filter(
      (r) => r.id !== relationshipId
    );
    if (this.relationships.length < initialLength) {
      this.touch();
      return true;
    }
    return false;
  }

  /**
   * 添加验证规则
   */
  addValidationRule(rule: ValidationRule): void {
    this.validationRules.push(rule);
    this.touch();
  }

  /**
   * 删除验证规则
   */
  removeValidationRule(ruleId: string): boolean {
    const initialLength = this.validationRules.length;
    this.validationRules = this.validationRules.filter((r) => r.id !== ruleId);
    if (this.validationRules.length < initialLength) {
      this.touch();
      return true;
    }
    return false;
  }

  /**
   * 验证数据实体的完整性
   */
  validate(): ValidationResult {
    const result = new ValidationResultImpl();

    // 验证基本信息
    if (!this.name || this.name.trim() === '') {
      result.addError('Entity name is required', 'name', 'REQUIRED');
    }

    if (!/^[a-zA-Z][a-zA-Z0-9_]*$/.test(this.name)) {
      result.addError(
        'Entity name must start with a letter and contain only letters, numbers, and underscores',
        'name',
        'INVALID_FORMAT'
      );
    }

    // 验证字段
    if (this.fields.length === 0) {
      result.addWarning(
        'Entity has no fields defined',
        'fields',
        'EMPTY_FIELDS'
      );
    }

    const fieldNames = new Set<string>();
    for (const field of this.fields) {
      // 检查字段名重复
      if (fieldNames.has(field.name)) {
        result.addError(
          `Duplicate field name: ${field.name}`,
          'fields',
          'DUPLICATE_FIELD'
        );
      }
      fieldNames.add(field.name);

      // 验证字段
      const fieldValidation = this.validateField(field);
      result.merge(fieldValidation);
    }

    // 验证关系
    for (const relationship of this.relationships) {
      const relationshipValidation = this.validateRelationship(relationship);
      result.merge(relationshipValidation);
    }

    return result;
  }

  /**
   * 验证字段
   */
  private validateField(field: DataField): ValidationResult {
    const result = new ValidationResultImpl();

    if (!field.name || field.name.trim() === '') {
      result.addError(
        'Field name is required',
        `fields.${field.id}.name`,
        'REQUIRED'
      );
    }

    if (!/^[a-zA-Z][a-zA-Z0-9_]*$/.test(field.name)) {
      result.addError(
        'Field name must start with a letter and contain only letters, numbers, and underscores',
        `fields.${field.id}.name`,
        'INVALID_FORMAT'
      );
    }

    // 验证字段类型特定的约束
    if (
      field.type === DataFieldType.REFERENCE &&
      !field.constraints.some((c) => c.type === 'FOREIGN_KEY')
    ) {
      result.addWarning(
        `Reference field ${field.name} should have a foreign key constraint`,
        `fields.${field.id}.constraints`,
        'MISSING_FOREIGN_KEY'
      );
    }

    return result;
  }

  /**
   * 验证关系
   */
  private validateRelationship(
    relationship: EntityRelationship
  ): ValidationResult {
    const result = new ValidationResultImpl();

    if (!relationship.targetEntityId) {
      result.addError(
        'Relationship target entity ID is required',
        `relationships.${relationship.id}.targetEntityId`,
        'REQUIRED'
      );
    }

    return result;
  }

  /**
   * 验证数据对象是否符合此实体定义
   */
  validateData(data: Record<string, any>): ValidationResult {
    const result = new ValidationResultImpl();

    // 检查必需字段
    for (const field of this.fields) {
      if (
        field.required &&
        (data[field.name] === undefined || data[field.name] === null)
      ) {
        result.addError(
          `Required field ${field.name} is missing`,
          field.name,
          'REQUIRED_FIELD_MISSING'
        );
        continue;
      }

      if (data[field.name] !== undefined) {
        const fieldValidation = this.validateFieldData(field, data[field.name]);
        result.merge(fieldValidation);
      }
    }

    // 检查额外字段
    for (const key in data) {
      if (!this.getFieldByName(key)) {
        result.addWarning(`Unknown field ${key} in data`, key, 'UNKNOWN_FIELD');
      }
    }

    return result;
  }

  /**
   * 验证字段数据
   */
  private validateFieldData(field: DataField, value: any): ValidationResult {
    const result = new ValidationResultImpl();

    // 类型验证
    if (!this.isValidFieldType(field.type, value)) {
      result.addError(
        `Field ${field.name} has invalid type. Expected ${
          field.type
        }, got ${typeof value}`,
        field.name,
        'INVALID_TYPE'
      );
      return result;
    }

    // 约束验证
    for (const constraint of field.constraints) {
      const constraintValidation = this.validateConstraint(
        field,
        value,
        constraint
      );
      result.merge(constraintValidation);
    }

    return result;
  }

  /**
   * 验证字段类型
   */
  private isValidFieldType(fieldType: DataFieldType, value: any): boolean {
    switch (fieldType) {
      case DataFieldType.STRING:
        return typeof value === 'string';
      case DataFieldType.NUMBER:
        return typeof value === 'number' && !isNaN(value);
      case DataFieldType.BOOLEAN:
        return typeof value === 'boolean';
      case DataFieldType.DATE:
      case DataFieldType.DATETIME:
        return value instanceof Date || !isNaN(Date.parse(value));
      case DataFieldType.EMAIL:
        return (
          typeof value === 'string' && /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)
        );
      case DataFieldType.URL:
        try {
          new URL(value);
          return true;
        } catch {
          return false;
        }
      case DataFieldType.JSON:
        try {
          if (typeof value === 'string') {
            JSON.parse(value);
          }
          return true;
        } catch {
          return false;
        }
      case DataFieldType.ARRAY:
        return Array.isArray(value);
      case DataFieldType.OBJECT:
        return (
          typeof value === 'object' && value !== null && !Array.isArray(value)
        );
      default:
        return true;
    }
  }

  /**
   * 验证约束
   */
  private validateConstraint(
    field: DataField,
    value: any,
    constraint: DataConstraint
  ): ValidationResult {
    const result = new ValidationResultImpl();

    switch (constraint.type) {
      case 'LENGTH':
        if (typeof value === 'string' && value.length > constraint.value) {
          result.addError(constraint.message, field.name, 'LENGTH_EXCEEDED');
        }
        break;
      case 'RANGE':
        if (typeof value === 'number') {
          const [min, max] = constraint.value;
          if (value < min || value > max) {
            result.addError(constraint.message, field.name, 'OUT_OF_RANGE');
          }
        }
        break;
      case 'PATTERN':
        if (
          typeof value === 'string' &&
          !new RegExp(constraint.value).test(value)
        ) {
          result.addError(constraint.message, field.name, 'PATTERN_MISMATCH');
        }
        break;
      // 其他约束类型的验证可以在这里添加
    }

    return result;
  }

  /**
   * 序列化为JSON
   */
  toJSON(): Record<string, any> {
    return {
      id: this.id,
      name: this.name,
      description: this.description,
      createdAt: this.createdAt.toISOString(),
      updatedAt: this.updatedAt.toISOString(),
      version: this.version,
      fields: this.fields,
      relationships: this.relationships,
      validationRules: this.validationRules,
      constraints: this.constraints,
    };
  }

  /**
   * 从JSON反序列化
   */
  static fromJSON(json: Record<string, any>): DataEntity {
    return new DataEntity({
      ...json,
      createdAt: new Date(json.createdAt),
      updatedAt: new Date(json.updatedAt),
    });
  }

  /**
   * 克隆实体
   */
  clone(): DataEntity {
    return DataEntity.fromJSON(this.toJSON());
  }

  /**
   * 更新时间戳
   */
  private touch(): void {
    this.updatedAt = new Date();
  }

  /**
   * 生成唯一ID
   */
  private generateId(): string {
    return `entity_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
