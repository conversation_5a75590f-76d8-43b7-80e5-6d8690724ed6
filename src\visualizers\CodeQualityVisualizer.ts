import * as vscode from 'vscode';
import { CodeClassification, CodeQualityLevel } from '../classifiers/LegacyCodeClassifier';

/**
 * 可视化节点接口
 */
export interface QualityVisualizationNode {
  id: string;
  label: string;
  group: string;
  color: string;
  size: number;
  shape: string;
  title: string;
  classification: string;
  qualityLevel: CodeQualityLevel;
  score: number;
  metadata: any;
}

/**
 * 代码质量可视化器
 * 将代码分类结果转换为可视化数据
 */
export class CodeQualityVisualizer {
  
  /**
   * 将分类结果转换为可视化数据
   */
  public visualizeClassifications(classifications: Map<string, CodeClassification>): QualityVisualizationNode[] {
    const nodes: QualityVisualizationNode[] = [];

    for (const [fileUri, classification] of classifications) {
      const node = this.createVisualizationNode(classification);
      nodes.push(node);
    }

    return nodes;
  }

  /**
   * 创建可视化节点
   */
  private createVisualizationNode(classification: CodeClassification): QualityVisualizationNode {
    return {
      id: classification.fileUri,
      label: this.getNodeLabel(classification),
      group: classification.classification,
      color: this.getZoneColor(classification.classification),
      size: this.getNodeSize(classification),
      shape: this.getNodeShape(classification),
      title: this.getNodeTitle(classification),
      classification: classification.classification,
      qualityLevel: classification.qualityLevel,
      score: classification.score,
      metadata: {
        filePath: classification.filePath,
        relativePath: classification.relativePath,
        metrics: classification.metrics,
        issues: classification.issues,
        reasons: classification.reasons,
        suggestions: classification.suggestions
      }
    };
  }

  /**
   * 获取节点标签
   */
  private getNodeLabel(classification: CodeClassification): string {
    const fileName = classification.relativePath.split('/').pop() || classification.relativePath;
    return fileName.replace(/\.(ts|js|tsx|jsx|py)$/, '');
  }

  /**
   * 获取区域颜色
   */
  private getZoneColor(zone: string): string {
    switch (zone) {
      case 'green_zone': return '#4CAF50';  // 绿色 - 高质量
      case 'yellow_zone': return '#FF9800'; // 橙色 - 需要关注
      case 'red_zone': return '#F44336';    // 红色 - 需要重构
      case 'purple_zone': return '#9C27B0'; // 紫色 - 特殊情况
      default: return '#9E9E9E';            // 灰色 - 未分类
    }
  }

  /**
   * 获取节点大小
   */
  private getNodeSize(classification: CodeClassification): number {
    const baseSize = 20;
    const scoreMultiplier = classification.score / 100;
    const complexityMultiplier = Math.min(classification.metrics.linesOfCode / 500, 2);
    
    return baseSize + (scoreMultiplier * 10) + (complexityMultiplier * 5);
  }

  /**
   * 获取节点形状
   */
  private getNodeShape(classification: CodeClassification): string {
    switch (classification.qualityLevel) {
      case 'excellent': return 'star';
      case 'good': return 'dot';
      case 'fair': return 'triangle';
      case 'poor': return 'square';
      case 'critical': return 'diamond';
      default: return 'dot';
    }
  }

  /**
   * 获取节点提示信息
   */
  private getNodeTitle(classification: CodeClassification): string {
    let title = `File: ${classification.relativePath}\n`;
    title += `Zone: ${this.getZoneDisplayName(classification.classification)}\n`;
    title += `Quality: ${classification.qualityLevel.toUpperCase()}\n`;
    title += `Score: ${classification.score.toFixed(1)}/100\n\n`;
    
    title += `Metrics:\n`;
    title += `  • Lines of Code: ${classification.metrics.linesOfCode}\n`;
    title += `  • Complexity: ${classification.metrics.cyclomaticComplexity}\n`;
    title += `  • Technical Debt: ${classification.metrics.technicalDebt} min\n`;
    title += `  • Documentation: ${(classification.metrics.documentationCoverage * 100).toFixed(1)}%\n`;
    title += `  • Code Smells: ${classification.metrics.codeSmells}\n\n`;
    
    if (classification.issues.length > 0) {
      title += `Top Issues:\n`;
      classification.issues.slice(0, 3).forEach(issue => {
        title += `  • [${issue.severity.toUpperCase()}] ${issue.message}\n`;
      });
    }

    return title.trim();
  }

  /**
   * 获取区域显示名称
   */
  private getZoneDisplayName(zone: string): string {
    switch (zone) {
      case 'green_zone': return '🟢 Green Zone (High Quality)';
      case 'yellow_zone': return '🟡 Yellow Zone (Needs Attention)';
      case 'red_zone': return '🔴 Red Zone (Needs Refactoring)';
      case 'purple_zone': return '🟣 Purple Zone (Special Cases)';
      default: return 'Unknown Zone';
    }
  }

  /**
   * 生成质量热力图HTML
   */
  public generateHeatmapHTML(nodes: QualityVisualizationNode[]): string {
    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Code Quality Heatmap</title>
    <script src="https://unpkg.com/vis-network/standalone/umd/vis-network.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            background-color: var(--vscode-editor-background);
            color: var(--vscode-editor-foreground);
            font-family: var(--vscode-font-family);
        }
        
        #quality-heatmap {
            width: 100vw;
            height: 100vh;
        }
        
        .controls {
            position: fixed;
            top: 10px;
            left: 10px;
            background: var(--vscode-panel-background);
            border: 1px solid var(--vscode-panel-border);
            border-radius: 6px;
            padding: 10px;
            z-index: 1000;
        }
        
        .controls button {
            background: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            border: none;
            border-radius: 4px;
            padding: 6px 12px;
            margin: 2px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .controls button:hover {
            background: var(--vscode-button-hoverBackground);
        }
        
        .controls select {
            background: var(--vscode-dropdown-background);
            color: var(--vscode-dropdown-foreground);
            border: 1px solid var(--vscode-dropdown-border);
            border-radius: 4px;
            padding: 4px 8px;
            margin: 2px;
        }
        
        .legend {
            position: fixed;
            bottom: 10px;
            left: 10px;
            background: var(--vscode-panel-background);
            border: 1px solid var(--vscode-panel-border);
            border-radius: 6px;
            padding: 10px;
            font-size: 12px;
            max-width: 300px;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            margin: 4px 0;
        }
        
        .legend-color {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .stats-panel {
            position: fixed;
            top: 10px;
            right: 10px;
            background: var(--vscode-panel-background);
            border: 1px solid var(--vscode-panel-border);
            border-radius: 6px;
            padding: 10px;
            font-size: 12px;
            min-width: 200px;
        }
        
        .zone-filter {
            margin: 5px 0;
        }
        
        .zone-filter input[type="checkbox"] {
            margin-right: 5px;
        }
    </style>
</head>
<body>
    <div class="controls">
        <button onclick="fitNetwork()">🔍 Fit All</button>
        <button onclick="togglePhysics()">⚡ Toggle Physics</button>
        <button onclick="exportImage()">📷 Export</button>
        <select onchange="changeLayout(this.value)">
            <option value="force">Force Layout</option>
            <option value="hierarchical">Hierarchical</option>
            <option value="circular">Circular</option>
        </select>
    </div>
    
    <div class="stats-panel">
        <h3>📊 Quality Statistics</h3>
        <div id="stats-content"></div>
        
        <h4>🎯 Zone Filters</h4>
        <div class="zone-filter">
            <input type="checkbox" id="green-zone" checked onchange="toggleZone('green_zone', this.checked)">
            <label for="green-zone">🟢 Green Zone</label>
        </div>
        <div class="zone-filter">
            <input type="checkbox" id="yellow-zone" checked onchange="toggleZone('yellow_zone', this.checked)">
            <label for="yellow-zone">🟡 Yellow Zone</label>
        </div>
        <div class="zone-filter">
            <input type="checkbox" id="red-zone" checked onchange="toggleZone('red_zone', this.checked)">
            <label for="red-zone">🔴 Red Zone</label>
        </div>
        <div class="zone-filter">
            <input type="checkbox" id="purple-zone" checked onchange="toggleZone('purple_zone', this.checked)">
            <label for="purple-zone">🟣 Purple Zone</label>
        </div>
    </div>
    
    <div id="quality-heatmap"></div>
    
    <div class="legend">
        <h4>📋 Legend</h4>
        <div class="legend-item">
            <div class="legend-color" style="background-color: #4CAF50;"></div>
            <span>Green Zone - High Quality</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background-color: #FF9800;"></div>
            <span>Yellow Zone - Needs Attention</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background-color: #F44336;"></div>
            <span>Red Zone - Needs Refactoring</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background-color: #9C27B0;"></div>
            <span>Purple Zone - Special Cases</span>
        </div>
        
        <h4>🔸 Shapes</h4>
        <div style="font-size: 11px;">
            <div>⭐ Excellent Quality</div>
            <div>⚫ Good Quality</div>
            <div>🔺 Fair Quality</div>
            <div>⬜ Poor Quality</div>
            <div>◆ Critical Quality</div>
        </div>
    </div>

    <script>
        // 数据
        const allNodes = ${JSON.stringify(nodes)};
        let visibleNodes = [...allNodes];
        const nodes = new vis.DataSet(visibleNodes);
        const edges = new vis.DataSet([]); // 质量热力图不需要边
        
        // 配置
        const options = {
            layout: {
                randomSeed: 2
            },
            physics: {
                enabled: true,
                stabilization: { iterations: 100 }
            },
            nodes: {
                borderWidth: 2,
                shadow: true,
                font: {
                    size: 12,
                    color: 'var(--vscode-editor-foreground)'
                }
            },
            interaction: {
                hover: true,
                tooltipDelay: 200
            },
            groups: {
                green_zone: { color: '#4CAF50' },
                yellow_zone: { color: '#FF9800' },
                red_zone: { color: '#F44336' },
                purple_zone: { color: '#9C27B0' }
            }
        };
        
        // 创建网络
        const container = document.getElementById('quality-heatmap');
        const data = { nodes: nodes, edges: edges };
        const network = new vis.Network(container, data, options);
        
        // 更新统计信息
        updateStatistics();
        
        // 事件处理
        network.on('click', function(params) {
            if (params.nodes.length > 0) {
                const nodeId = params.nodes[0];
                const node = nodes.get(nodeId);
                console.log('Selected node:', node);
                showNodeDetails(node);
            }
        });
        
        network.on('doubleClick', function(params) {
            if (params.nodes.length > 0) {
                const nodeId = params.nodes[0];
                const node = nodes.get(nodeId);
                if (node.metadata.filePath) {
                    // 通知VSCode打开文件
                    if (window.acquireVsCodeApi) {
                        const vscode = window.acquireVsCodeApi();
                        vscode.postMessage({
                            command: 'openFile',
                            filePath: node.metadata.filePath
                        });
                    }
                }
            }
        });
        
        // 控制函数
        function fitNetwork() {
            network.fit();
        }
        
        let physicsEnabled = true;
        function togglePhysics() {
            physicsEnabled = !physicsEnabled;
            network.setOptions({ physics: { enabled: physicsEnabled } });
        }
        
        function changeLayout(layout) {
            const layoutOptions = {
                force: { randomSeed: 2 },
                hierarchical: {
                    hierarchical: {
                        enabled: true,
                        direction: 'UD',
                        sortMethod: 'directed'
                    }
                },
                circular: { randomSeed: 2 }
            };
            
            network.setOptions({ layout: layoutOptions[layout] });
        }
        
        function toggleZone(zone, visible) {
            if (visible) {
                const zoneNodes = allNodes.filter(n => n.classification === zone);
                zoneNodes.forEach(node => {
                    if (!visibleNodes.find(n => n.id === node.id)) {
                        visibleNodes.push(node);
                        nodes.add(node);
                    }
                });
            } else {
                const nodesToRemove = visibleNodes.filter(n => n.classification === zone);
                nodesToRemove.forEach(node => {
                    nodes.remove(node.id);
                });
                visibleNodes = visibleNodes.filter(n => n.classification !== zone);
            }
            updateStatistics();
        }
        
        function exportImage() {
            const canvas = container.querySelector('canvas');
            if (canvas) {
                const link = document.createElement('a');
                link.download = 'code-quality-heatmap.png';
                link.href = canvas.toDataURL();
                link.click();
            }
        }
        
        function updateStatistics() {
            const stats = calculateStatistics(visibleNodes);
            const statsContent = document.getElementById('stats-content');
            
            statsContent.innerHTML = \`
                <div>Total Files: \${stats.total}</div>
                <div>Average Score: \${stats.averageScore.toFixed(1)}</div>
                <div style="margin-top: 10px;">
                    <div>🟢 Green: \${stats.green}</div>
                    <div>🟡 Yellow: \${stats.yellow}</div>
                    <div>🔴 Red: \${stats.red}</div>
                    <div>🟣 Purple: \${stats.purple}</div>
                </div>
            \`;
        }
        
        function calculateStatistics(nodeList) {
            const total = nodeList.length;
            const averageScore = total > 0 ? 
                nodeList.reduce((sum, n) => sum + n.score, 0) / total : 0;
            
            return {
                total,
                averageScore,
                green: nodeList.filter(n => n.classification === 'green_zone').length,
                yellow: nodeList.filter(n => n.classification === 'yellow_zone').length,
                red: nodeList.filter(n => n.classification === 'red_zone').length,
                purple: nodeList.filter(n => n.classification === 'purple_zone').length
            };
        }
        
        function showNodeDetails(node) {
            const details = \`
File: \${node.metadata.relativePath}
Zone: \${getZoneDisplayName(node.classification)}
Quality Level: \${node.qualityLevel.toUpperCase()}
Score: \${node.score.toFixed(1)}/100

Metrics:
• Lines of Code: \${node.metadata.metrics.linesOfCode}
• Complexity: \${node.metadata.metrics.cyclomaticComplexity}
• Technical Debt: \${node.metadata.metrics.technicalDebt} min
• Documentation: \${(node.metadata.metrics.documentationCoverage * 100).toFixed(1)}%

Issues: \${node.metadata.issues.length}
\${node.metadata.issues.slice(0, 3).map(issue => 
    \`• [\${issue.severity.toUpperCase()}] \${issue.message}\`
).join('\\n')}

Suggestions:
\${node.metadata.suggestions.slice(0, 3).map(s => \`• \${s}\`).join('\\n')}
            \`.trim();
            
            alert(details);
        }
        
        function getZoneDisplayName(zone) {
            const names = {
                'green_zone': '🟢 Green Zone (High Quality)',
                'yellow_zone': '🟡 Yellow Zone (Needs Attention)',
                'red_zone': '🔴 Red Zone (Needs Refactoring)',
                'purple_zone': '🟣 Purple Zone (Special Cases)'
            };
            return names[zone] || 'Unknown Zone';
        }
        
        // 初始化
        network.once('stabilizationIterationsDone', function() {
            network.fit();
        });
    </script>
</body>
</html>`;
  }

  /**
   * 生成质量矩阵HTML
   */
  public generateQualityMatrixHTML(classifications: Map<string, CodeClassification>): string {
    const classificationArray = Array.from(classifications.values());
    
    // 按目录分组
    const directoryGroups = new Map<string, CodeClassification[]>();
    
    classificationArray.forEach(classification => {
      const pathParts = classification.relativePath.split('/');
      const directory = pathParts.length > 1 ? pathParts[0] : 'root';
      
      if (!directoryGroups.has(directory)) {
        directoryGroups.set(directory, []);
      }
      directoryGroups.get(directory)!.push(classification);
    });

    let matrixHTML = '';
    
    for (const [directory, files] of directoryGroups) {
      matrixHTML += `<div class="directory-group">`;
      matrixHTML += `<h3>${directory}</h3>`;
      matrixHTML += `<div class="file-grid">`;
      
      files.forEach(file => {
        const zoneClass = file.classification.replace('_', '-');
        matrixHTML += `
          <div class="file-cell ${zoneClass}" 
               title="${file.relativePath}&#10;Score: ${file.score.toFixed(1)}/100&#10;Quality: ${file.qualityLevel}"
               onclick="openFile('${file.filePath}')">
            <div class="file-name">${file.relativePath.split('/').pop()}</div>
            <div class="file-score">${file.score.toFixed(0)}</div>
          </div>
        `;
      });
      
      matrixHTML += `</div></div>`;
    }

    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Code Quality Matrix</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background-color: var(--vscode-editor-background);
            color: var(--vscode-editor-foreground);
            font-family: var(--vscode-font-family);
        }
        
        .directory-group {
            margin-bottom: 30px;
        }
        
        .directory-group h3 {
            margin-bottom: 10px;
            color: var(--vscode-textLink-foreground);
        }
        
        .file-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            gap: 8px;
        }
        
        .file-cell {
            padding: 10px;
            border-radius: 6px;
            text-align: center;
            cursor: pointer;
            transition: transform 0.2s ease;
            border: 2px solid transparent;
        }
        
        .file-cell:hover {
            transform: scale(1.05);
            border-color: var(--vscode-focusBorder);
        }
        
        .file-cell.green-zone {
            background-color: #4CAF50;
            color: white;
        }
        
        .file-cell.yellow-zone {
            background-color: #FF9800;
            color: white;
        }
        
        .file-cell.red-zone {
            background-color: #F44336;
            color: white;
        }
        
        .file-cell.purple-zone {
            background-color: #9C27B0;
            color: white;
        }
        
        .file-name {
            font-size: 12px;
            font-weight: bold;
            margin-bottom: 4px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .file-score {
            font-size: 14px;
            font-weight: bold;
        }
        
        .legend {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--vscode-panel-background);
            border: 1px solid var(--vscode-panel-border);
            border-radius: 6px;
            padding: 15px;
            font-size: 12px;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            margin: 8px 0;
        }
        
        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 4px;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="legend">
        <h4>📋 Quality Zones</h4>
        <div class="legend-item">
            <div class="legend-color" style="background-color: #4CAF50;"></div>
            <span>Green Zone - High Quality</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background-color: #FF9800;"></div>
            <span>Yellow Zone - Needs Attention</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background-color: #F44336;"></div>
            <span>Red Zone - Needs Refactoring</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background-color: #9C27B0;"></div>
            <span>Purple Zone - Special Cases</span>
        </div>
    </div>
    
    <h1>🏗️ Code Quality Matrix</h1>
    
    ${matrixHTML}
    
    <script>
        function openFile(filePath) {
            if (window.acquireVsCodeApi) {
                const vscode = window.acquireVsCodeApi();
                vscode.postMessage({
                    command: 'openFile',
                    filePath: filePath
                });
            }
        }
    </script>
</body>
</html>`;
  }
}