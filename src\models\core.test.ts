import { describe, it, expect } from 'vitest';
import { DataEntity } from './DataEntity';
import { BusinessRule } from './BusinessRule';
import { Blueprint } from './Blueprint';
import { ProjectConstitution } from './ProjectConstitution';
import { DataFieldType, BusinessRuleType, LogicOperator } from './enums';

describe('Core Data Models Integration', () => {
  it('should create and validate a complete project constitution', () => {
    // 创建数据实体
    const userEntity = new DataEntity({
      name: 'User',
      description: 'User entity for authentication',
    });

    userEntity.addField({
      name: 'email',
      type: DataFieldType.EMAIL,
      required: true,
      constraints: [],
    });

    userEntity.addField({
      name: 'password',
      type: DataFieldType.STRING,
      required: true,
      constraints: [
        {
          type: 'LENGTH',
          value: 8,
          message: 'Password must be at least 8 characters',
        },
      ],
    });

    // 创建业务规则
    const emailValidationRule = new BusinessRule({
      name: 'Email Validation Rule',
      type: BusinessRuleType.VALIDATION,
      condition: {
        operator: LogicOperator.EQUALS,
        operands: [
          { type: 'FIELD', value: 'email' },
          { type: 'CONSTANT', value: 'valid' },
        ],
      },
      action: {
        type: 'SHOW_MESSAGE',
        target: 'user',
        value: 'Email is valid',
      },
      priority: 10,
    });

    // 创建蓝图
    const blueprint = new Blueprint({
      name: 'User Management System',
      description: 'A simple user management system',
    });

    blueprint.addDataEntity(userEntity);
    blueprint.addBusinessRule(emailValidationRule);

    // 创建项目宪法
    const constitution = new ProjectConstitution({
      name: 'User Management Constitution',
      description: 'Constitutional framework for user management',
      blueprint,
    });

    constitution.addDataEntity(userEntity);
    constitution.addBusinessRule(emailValidationRule);

    // 验证整个宪法
    const validation = constitution.validate();

    expect(validation.isValid).toBe(true);
    expect(constitution.dataEntities).toHaveLength(1);
    expect(constitution.businessRules).toHaveLength(1);
    expect(constitution.blueprint.dataEntities).toHaveLength(1);
    expect(constitution.blueprint.businessRules).toHaveLength(1);
  });

  it('should detect validation errors in incomplete data', () => {
    const entity = new DataEntity({ name: '' }); // Invalid name
    const validation = entity.validate();

    expect(validation.isValid).toBe(false);
    expect(validation.errors.length).toBeGreaterThan(0);
  });

  it('should serialize and deserialize correctly', () => {
    const entity = new DataEntity({
      name: 'TestEntity',
      description: 'Test entity for serialization',
    });

    entity.addField({
      name: 'testField',
      type: DataFieldType.STRING,
      required: true,
      constraints: [],
    });

    const json = entity.toJSON();
    const deserialized = DataEntity.fromJSON(json);

    expect(deserialized.name).toBe(entity.name);
    expect(deserialized.description).toBe(entity.description);
    expect(deserialized.fields).toHaveLength(1);
    expect(deserialized.fields[0].name).toBe('testField');
  });
});
