import * as vscode from 'vscode';
import { WhiteboardProvider } from './providers/WhiteboardProvider';
import { ExplorerProvider } from './providers/ExplorerProvider';
import { WebviewProvider } from './providers/WebviewProvider';
import { DataDictionaryEngine } from './engines/DataDictionaryEngine';
import { ConstitutionalEngine } from './engines/SimpleConstitutionalEngine';
import { FileSystemWatcher } from './services/FileSystemWatcher';
import { AnalysisEngine } from './services/AnalysisEngine';
import { ProjectType } from './models/enums';

// 全局引擎实例
let constitutionalEngine: ConstitutionalEngine;
let dataDictionaryEngine: DataDictionaryEngine;
let webviewProvider: WebviewProvider;
let fileSystemWatcher: FileSystemWatcher;
let analysisEngine: AnalysisEngine;

export function activate(context: vscode.ExtensionContext) {
  console.log('🏛️ AI Code Visualizer - Visual Intent Contract extension is now active!');

  // 初始化引擎
  constitutionalEngine = new ConstitutionalEngine({
    enableStrictValidation: true,
    enableConflictDetection: true,
    enableAutoResolution: false,
    maxRuleComplexity: 10,
    maxEntityCount: 100,
  });

  dataDictionaryEngine = new DataDictionaryEngine({
    enableStrictValidation: true,
    enableRelationshipValidation: true,
    maxEntityCount: 100,
    maxFieldsPerEntity: 50,
    enableAutoNaming: true,
  });

  webviewProvider = new WebviewProvider(context);
  
  // 初始化文件系统监听和分析引擎
  fileSystemWatcher = new FileSystemWatcher({
    enableCodeFileWatching: true,
    enableConfigFileWatching: true,
    debounceDelay: 500,
    maxEventsPerSecond: 10
  });
  
  analysisEngine = new AnalysisEngine({
    enableIncrementalAnalysis: true,
    enableBackgroundAnalysis: true,
    maxConcurrentTasks: 3
  });

  // Register providers
  const whiteboardProvider = new WhiteboardProvider(context);
  const explorerProvider = new ExplorerProvider(context);

  // Register commands
  const openWhiteboardCommand = vscode.commands.registerCommand(
    'aiCodeVisualizer.openWhiteboard',
    () => {
      whiteboardProvider.openWhiteboard();
    }
  );

  const openWebviewCommand = vscode.commands.registerCommand(
    'aiCodeVisualizer.openWebview',
    () => {
      webviewProvider.createOrShow();
    }
  );

  const renderBlueprintCommand = vscode.commands.registerCommand(
    'aiCodeVisualizer.renderBlueprint',
    () => {
      const constitution = constitutionalEngine.getConstitution();
      
      if (!constitution || !constitution.blueprint) {
        vscode.window.showWarningMessage('No blueprint available. Please create a project blueprint first.');
        return;
      }

      // 通过Webview渲染蓝图
      webviewProvider.postMessage({
        command: 'renderBlueprint',
        data: {
          blueprint: constitution.blueprint
        }
      });
      
      webviewProvider.createOrShow();
    }
  );

  const renderComparisonCommand = vscode.commands.registerCommand(
    'aiCodeVisualizer.renderComparison',
    async () => {
      try {
        const constitution = constitutionalEngine.getConstitution();
        
        if (!constitution || !constitution.blueprint) {
          vscode.window.showWarningMessage('No blueprint available. Please create a project blueprint first.');
          return;
        }

        // 获取项目分析结果
        const analysis = await analysisEngine.getFullAnalysis();
        
        // 执行蓝图比对
        const comparison = await analysisEngine.compareBlueprint(constitution.blueprint);
        
        // 通过Webview渲染比对结果
        webviewProvider.postMessage({
          command: 'renderComparison',
          data: {
            blueprint: constitution.blueprint,
            analysis: analysis,
            comparison: comparison
          }
        });
        
        webviewProvider.createOrShow();
        
      } catch (error) {
        vscode.window.showErrorMessage(`❌ Failed to render comparison: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }
  );

  const showFileWatcherStatusCommand = vscode.commands.registerCommand(
    'aiCodeVisualizer.showFileWatcherStatus',
    () => {
      const watcherStats = fileSystemWatcher.getStats();
      const analysisStats = analysisEngine.getStats();
      const parserStats = analysisEngine.getParserStats();
      
      const message = `📁 文件系统监听状态:\n` +
        `  • 监听器数量: ${watcherStats.watcherCount} 个\n` +
        `  • 队列事件: ${watcherStats.queuedEvents} 个\n` +
        `  • 事件处理器: ${watcherStats.totalHandlers} 个\n` +
        `  • 状态: ${watcherStats.isActive ? '🟢 活跃' : '🔴 停止'}\n\n` +
        `🔬 分析引擎状态:\n` +
        `  • 队列任务: ${analysisStats.queuedTasks} 个\n` +
        `  • 活跃任务: ${analysisStats.activeTasks} 个\n` +
        `  • 分析结果: ${analysisStats.totalResults} 个\n` +
        `  • 状态: ${analysisStats.isProcessing ? '🟢 处理中' : '🔴 停止'}\n\n` +
        `🔧 解析器状态:\n` +
        `  • 支持语言: ${analysisEngine.getSupportedLanguages().join(', ')}\n` +
        `  • 解析成功率: ${parserStats.totalParsed > 0 ? ((parserStats.successfulParsed / parserStats.totalParsed) * 100).toFixed(1) : 0}%\n` +
        `  • 平均解析时间: ${parserStats.averageParseTime.toFixed(2)}ms`;

      vscode.window.showInformationMessage(message, { modal: true });
    }
  );

  const showDetailedAnalysisReportCommand = vscode.commands.registerCommand(
    'aiCodeVisualizer.showDetailedAnalysisReport',
    () => {
      const report = analysisEngine.getDetailedReport();
      
      // 创建一个新的文档显示报告
      vscode.workspace.openTextDocument({
        content: report,
        language: 'plaintext'
      }).then(doc => {
        vscode.window.showTextDocument(doc);
      });
    }
  );

  const analyzeDependenciesCommand = vscode.commands.registerCommand(
    'aiCodeVisualizer.analyzeDependencies',
    async () => {
      try {
        vscode.window.showInformationMessage('🔍 Starting dependency analysis...');
        
        const dependencyGraph = await analysisEngine.analyzeDependencies();
        const stats = dependencyGraph.statistics;
        const cycles = dependencyGraph.circularDependencies;
        
        let message = `✅ Dependency analysis completed!\n\n`;
        message += `📊 Statistics:\n`;
        message += `  • Total Files: ${stats.totalNodes}\n`;
        message += `  • Dependencies: ${stats.totalEdges}\n`;
        message += `  • Circular Dependencies: ${stats.circularDependencies}\n`;
        message += `  • Max Depth: ${stats.maxDepth}\n`;
        
        if (cycles.length > 0) {
          message += `\n⚠️ Found ${cycles.length} circular dependencies:\n`;
          cycles.slice(0, 3).forEach((cycle, index) => {
            message += `  ${index + 1}. [${cycle.severity.toUpperCase()}] ${cycle.description}\n`;
          });
          
          if (cycles.length > 3) {
            message += `  ... and ${cycles.length - 3} more\n`;
          }
        }

        vscode.window.showInformationMessage(message, { modal: true }, 'Show Report', 'Visualize Graph')
          .then(selection => {
            if (selection === 'Show Report') {
              vscode.commands.executeCommand('aiCodeVisualizer.showDependencyReport');
            } else if (selection === 'Visualize Graph') {
              vscode.commands.executeCommand('aiCodeVisualizer.visualizeDependencyGraph');
            }
          });
        
      } catch (error) {
        vscode.window.showErrorMessage(`❌ Dependency analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }
  );

  const showDependencyReportCommand = vscode.commands.registerCommand(
    'aiCodeVisualizer.showDependencyReport',
    () => {
      const report = analysisEngine.generateDependencyReport();
      
      vscode.workspace.openTextDocument({
        content: report,
        language: 'plaintext'
      }).then(doc => {
        vscode.window.showTextDocument(doc);
      });
    }
  );

  const visualizeDependencyGraphCommand = vscode.commands.registerCommand(
    'aiCodeVisualizer.visualizeDependencyGraph',
    () => {
      const dependencyGraph = analysisEngine.getLastDependencyGraph();
      
      if (!dependencyGraph) {
        vscode.window.showWarningMessage('No dependency graph available. Please run dependency analysis first.');
        return;
      }

      // 通过Webview显示依赖图
      webviewProvider.postMessage({
        command: 'showDependencyGraph',
        data: {
          nodes: Array.from(dependencyGraph.nodes.values()),
          edges: dependencyGraph.edges,
          statistics: dependencyGraph.statistics,
          circularDependencies: dependencyGraph.circularDependencies
        }
      });
      
      webviewProvider.createOrShow();
    }
  );

  const classifyLegacyCodeCommand = vscode.commands.registerCommand(
    'aiCodeVisualizer.classifyLegacyCode',
    async () => {
      try {
        vscode.window.showInformationMessage('🏗️ Starting legacy code classification...');
        
        const classifications = await analysisEngine.classifyLegacyCode();
        const stats = analysisEngine.getLegacyCodeClassifier().getClassificationStats();
        
        let message = `✅ Legacy code classification completed!\n\n`;
        message += `📊 Statistics:\n`;
        message += `  • Total Files: ${stats.total}\n`;
        message += `  • Average Score: ${stats.averageScore.toFixed(1)}/100\n\n`;
        message += `🎯 Zone Distribution:\n`;
        message += `  • 🟢 Green Zone: ${stats.greenZone} files (${((stats.greenZone / stats.total) * 100).toFixed(1)}%)\n`;
        message += `  • 🟡 Yellow Zone: ${stats.yellowZone} files (${((stats.yellowZone / stats.total) * 100).toFixed(1)}%)\n`;
        message += `  • 🔴 Red Zone: ${stats.redZone} files (${((stats.redZone / stats.total) * 100).toFixed(1)}%)\n`;
        message += `  • 🟣 Purple Zone: ${stats.purpleZone} files (${((stats.purpleZone / stats.total) * 100).toFixed(1)}%)\n`;

        vscode.window.showInformationMessage(message, { modal: true }, 'Show Report', 'Show Heatmap', 'Show Matrix')
          .then(selection => {
            if (selection === 'Show Report') {
              vscode.commands.executeCommand('aiCodeVisualizer.showClassificationReport');
            } else if (selection === 'Show Heatmap') {
              vscode.commands.executeCommand('aiCodeVisualizer.showQualityHeatmap');
            } else if (selection === 'Show Matrix') {
              vscode.commands.executeCommand('aiCodeVisualizer.showQualityMatrix');
            }
          });
        
      } catch (error) {
        vscode.window.showErrorMessage(`❌ Legacy code classification failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }
  );

  const showClassificationReportCommand = vscode.commands.registerCommand(
    'aiCodeVisualizer.showClassificationReport',
    () => {
      const report = analysisEngine.generateClassificationReport();
      
      vscode.workspace.openTextDocument({
        content: report,
        language: 'plaintext'
      }).then(doc => {
        vscode.window.showTextDocument(doc);
      });
    }
  );

  const showQualityHeatmapCommand = vscode.commands.registerCommand(
    'aiCodeVisualizer.showQualityHeatmap',
    () => {
      const classifications = analysisEngine.getLastClassifications();
      
      if (!classifications) {
        vscode.window.showWarningMessage('No code classification available. Please run legacy code classification first.');
        return;
      }

      // 通过Webview显示质量热力图
      webviewProvider.postMessage({
        command: 'showQualityHeatmap',
        data: {
          classifications: Array.from(classifications.values())
        }
      });
      
      webviewProvider.createOrShow();
    }
  );

  const showQualityMatrixCommand = vscode.commands.registerCommand(
    'aiCodeVisualizer.showQualityMatrix',
    () => {
      const classifications = analysisEngine.getLastClassifications();
      
      if (!classifications) {
        vscode.window.showWarningMessage('No code classification available. Please run legacy code classification first.');
        return;
      }

      // 通过Webview显示质量矩阵
      webviewProvider.postMessage({
        command: 'showQualityMatrix',
        data: {
          classifications: Array.from(classifications.values())
        }
      });
      
      webviewProvider.createOrShow();
    }
  );

  const compareBlueprintCommand = vscode.commands.registerCommand(
    'aiCodeVisualizer.compareBlueprint',
    async () => {
      try {
        // 获取当前项目的蓝图
        const constitution = constitutionalEngine.getConstitution();
        
        if (!constitution || !constitution.blueprint) {
          vscode.window.showWarningMessage('No blueprint available. Please create a project blueprint first.');
          return;
        }

        vscode.window.showInformationMessage('🔍 Starting blueprint-code comparison...');
        
        const comparisons = await analysisEngine.compareBlueprintWithCode(constitution.blueprint);
        const stats = analysisEngine.getBlueprintComparator().getComparisonStats();
        
        let message = `✅ Blueprint comparison completed!\n\n`;
        message += `📊 Statistics:\n`;
        message += `  • Total Files: ${stats.total}\n`;
        message += `  • Average Confidence: ${(stats.averageConfidence * 100).toFixed(1)}%\n\n`;
        message += `🎯 Classification Distribution:\n`;
        message += `  • 🟢 Green (Perfect Match): ${stats.green} files (${((stats.green / stats.total) * 100).toFixed(1)}%)\n`;
        message += `  • 🟡 Yellow (Needs Attention): ${stats.yellow} files (${((stats.yellow / stats.total) * 100).toFixed(1)}%)\n`;
        message += `  • 🔴 Red (Major Issues): ${stats.red} files (${((stats.red / stats.total) * 100).toFixed(1)}%)\n`;
        message += `  • 🟣 Purple (Good Code, Different Design): ${stats.purple} files (${((stats.purple / stats.total) * 100).toFixed(1)}%)\n`;

        vscode.window.showInformationMessage(message, { modal: true }, 'Show Report', 'Show Visual Comparison')
          .then(selection => {
            if (selection === 'Show Report') {
              vscode.commands.executeCommand('aiCodeVisualizer.showComparisonReport');
            } else if (selection === 'Show Visual Comparison') {
              vscode.commands.executeCommand('aiCodeVisualizer.showBlueprintComparison');
            }
          });
        
      } catch (error) {
        vscode.window.showErrorMessage(`❌ Blueprint comparison failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }
  );

  const showComparisonReportCommand = vscode.commands.registerCommand(
    'aiCodeVisualizer.showComparisonReport',
    () => {
      const report = analysisEngine.generateComparisonReport();
      
      vscode.workspace.openTextDocument({
        content: report,
        language: 'plaintext'
      }).then(doc => {
        vscode.window.showTextDocument(doc);
      });
    }
  );

  const showBlueprintComparisonCommand = vscode.commands.registerCommand(
    'aiCodeVisualizer.showBlueprintComparison',
    () => {
      const comparisons = analysisEngine.getLastComparisons();
      
      if (!comparisons) {
        vscode.window.showWarningMessage('No blueprint comparison available. Please run blueprint comparison first.');
        return;
      }

      // 通过Webview显示蓝图比对结果
      webviewProvider.postMessage({
        command: 'showBlueprintComparison',
        data: {
          comparisons: Array.from(comparisons.values())
        }
      });
      
      webviewProvider.createOrShow();
    }
  );

  const createBlueprintCommand = vscode.commands.registerCommand(
    'aiCodeVisualizer.createBlueprint',
    async () => {
      const projectName = await vscode.window.showInputBox({
        prompt: '请输入项目名称 (Enter project name)',
        placeHolder: 'My Project',
        validateInput: (value) => {
          if (!value || value.trim() === '') {
            return '项目名称不能为空 (Project name cannot be empty)';
          }
          return null;
        },
      });

      if (!projectName) {
        return;
      }

      const projectTypes = [
        { label: '🌐 Web应用 (Web Application)', value: ProjectType.WEB_APPLICATION },
        { label: '📱 移动应用 (Mobile App)', value: ProjectType.MOBILE_APP },
        { label: '🖥️ 桌面应用 (Desktop App)', value: ProjectType.DESKTOP_APP },
        { label: '🔌 API服务 (API Service)', value: ProjectType.API_SERVICE },
        { label: '📚 库/框架 (Library)', value: ProjectType.LIBRARY },
        { label: '🏢 企业应用 (Enterprise App)', value: ProjectType.ENTERPRISE_APP },
      ];

      const selectedType = await vscode.window.showQuickPick(projectTypes, {
        placeHolder: '选择项目类型 (Select project type)',
      });

      if (!selectedType) {
        return;
      }

      try {
        const result = constitutionalEngine.createBlueprint({
          projectType: selectedType.value,
          name: projectName,
          description: `${selectedType.label} project`,
          author: 'VSCode User',
          targetLanguages: ['TypeScript', 'JavaScript'],
          estimatedComplexity: 'MEDIUM',
        });

        if (result.success) {
          vscode.window.showInformationMessage(
            `✅ 项目蓝图 "${projectName}" 创建成功！(Blueprint created successfully!)`
          );
          
          // 刷新资源管理器
          explorerProvider.refresh();
          
          // 打开白板显示蓝图
          whiteboardProvider.openWhiteboard();
        } else {
          vscode.window.showErrorMessage(
            `❌ 创建蓝图失败: ${result.validation.errors.map((e: any) => e.message).join(', ')}`
          );
        }
      } catch (error) {
        vscode.window.showErrorMessage(
          `❌ 创建蓝图时发生错误: ${error instanceof Error ? error.message : 'Unknown error'}`
        );
      }
    }
  );

  const createDataEntityCommand = vscode.commands.registerCommand(
    'aiCodeVisualizer.createDataEntity',
    async () => {
      const entityName = await vscode.window.showInputBox({
        prompt: '请输入数据实体名称 (Enter data entity name)',
        placeHolder: 'User',
        validateInput: (value) => {
          if (!value || value.trim() === '') {
            return '实体名称不能为空 (Entity name cannot be empty)';
          }
          if (!/^[a-zA-Z][a-zA-Z0-9_]*$/.test(value)) {
            return '实体名称必须以字母开头，只能包含字母、数字和下划线 (Invalid entity name format)';
          }
          return null;
        },
      });

      if (!entityName) {
        return;
      }

      const description = await vscode.window.showInputBox({
        prompt: '请输入实体描述 (Enter entity description)',
        placeHolder: 'User entity for authentication',
      });

      try {
        const result = dataDictionaryEngine.createEntity({
          name: entityName,
          description: description || `${entityName} entity`,
        });

        if (result.success) {
          vscode.window.showInformationMessage(
            `✅ 数据实体 "${entityName}" 创建成功！(Data entity created successfully!)`
          );
          
          // 刷新资源管理器
          explorerProvider.refresh();
        } else {
          vscode.window.showErrorMessage(
            `❌ 创建数据实体失败: ${result.validation.errors.map(e => e.message).join(', ')}`
          );
        }
      } catch (error) {
        vscode.window.showErrorMessage(
          `❌ 创建数据实体时发生错误: ${error instanceof Error ? error.message : 'Unknown error'}`
        );
      }
    }
  );

  const showStatisticsCommand = vscode.commands.registerCommand(
    'aiCodeVisualizer.showStatistics',
    () => {
      const constitution = constitutionalEngine.getConstitution();
      const dictStats = dataDictionaryEngine.getStatistics();
      
      let message = '📊 项目统计信息 (Project Statistics)\n\n';
      
      if (constitution) {
        message += `🏛️ 宪法信息 (Constitution):\n`;
        message += `  • 项目名称: ${constitution.blueprint.name}\n`;
        message += `  • 数据实体: ${constitution.dataEntities.length} 个\n`;
        message += `  • 业务规则: ${constitution.businessRules.length} 个\n`;
        message += `  • 模块数量: ${constitution.blueprint.modules.length} 个\n\n`;
      }
      
      message += `📚 数据字典 (Data Dictionary):\n`;
      message += `  • 实体数量: ${dictStats.entityCount} 个\n`;
      message += `  • 总字段数: ${dictStats.totalFields} 个\n`;
      message += `  • 总关系数: ${dictStats.totalRelationships} 个\n`;
      message += `  • 平均字段数: ${dictStats.averageFieldsPerEntity.toFixed(1)} 个/实体\n`;

      vscode.window.showInformationMessage(message, { modal: true });
    }
  );

  // Register tree data provider
  vscode.window.createTreeView('aiCodeVisualizer.explorer', {
    treeDataProvider: explorerProvider,
    showCollapseAll: true,
  });

  // 添加状态栏项
  const statusBarItem = vscode.window.createStatusBarItem(
    vscode.StatusBarAlignment.Right,
    100
  );
  statusBarItem.text = '🏛️ Visual Intent Contract';
  statusBarItem.tooltip = 'AI Code Visualizer - Visual Intent Contract System';
  statusBarItem.command = 'aiCodeVisualizer.showStatistics';
  statusBarItem.show();

  context.subscriptions.push(
    openWhiteboardCommand,
    openWebviewCommand,
    createBlueprintCommand,
    createDataEntityCommand,
    showStatisticsCommand,
    showFileWatcherStatusCommand,
    showDetailedAnalysisReportCommand,
    analyzeDependenciesCommand,
    showDependencyReportCommand,
    visualizeDependencyGraphCommand,
    classifyLegacyCodeCommand,
    showClassificationReportCommand,
    showQualityHeatmapCommand,
    showQualityMatrixCommand,
    compareBlueprintCommand,
    showComparisonReportCommand,
    showBlueprintComparisonCommand,
    whiteboardProvider,
    explorerProvider,
    statusBarItem
  );

  // 启动文件系统监听和分析引擎
  if (vscode.workspace.workspaceFolders && vscode.workspace.workspaceFolders.length > 0) {
    // 启动文件系统监听
    fileSystemWatcher.start();
    
    // 启动分析引擎
    analysisEngine.start();
    
    // 连接文件变更事件到分析引擎
    const fileChangeDisposable = fileSystemWatcher.onFileChange(event => {
      analysisEngine.handleFileChange(event);
    });
    
    // 处理分析结果
    const analysisResultDisposable = analysisEngine.onAnalysisResult(result => {
      console.log('📊 Analysis result:', {
        file: result.fileUri.fsPath,
        type: result.analysisType,
        success: result.success,
        data: result.data
      });
      
      // 通知Webview更新
      if (result.success && result.data) {
        webviewProvider.postMessage({
          command: 'analysisUpdate',
          data: {
            file: result.fileUri.fsPath,
            analysis: result.data
          }
        });
      }
    });
    
    context.subscriptions.push(fileChangeDisposable, analysisResultDisposable);
    
    console.log('🔍 File system monitoring and analysis engine started');
  } else {
    console.log('⚠️ No workspace folder found, file system monitoring disabled');
  }

  // 显示欢迎消息
  vscode.window.showInformationMessage(
    '🏛️ Visual Intent Contract 系统已激活！点击状态栏图标查看统计信息。',
    '创建蓝图',
    '创建数据实体'
  ).then(selection => {
    if (selection === '创建蓝图') {
      vscode.commands.executeCommand('aiCodeVisualizer.createBlueprint');
    } else if (selection === '创建数据实体') {
      vscode.commands.executeCommand('aiCodeVisualizer.createDataEntity');
    }
  });
}

export function deactivate() {
  console.log('🏛️ AI Code Visualizer extension is now deactivated!');
  
  // 清理资源
  if (fileSystemWatcher) {
    fileSystemWatcher.stop();
  }
  
  if (analysisEngine) {
    analysisEngine.stop();
  }
}

// 导出引擎实例供其他模块使用
export function getConstitutionalEngine(): ConstitutionalEngine {
  return constitutionalEngine;
}

export function getDataDictionaryEngine(): DataDictionaryEngine {
  return dataDictionaryEngine;
}
