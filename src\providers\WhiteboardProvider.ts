import * as vscode from 'vscode';

export class WhiteboardProvider implements vscode.Disposable {
  private panel: vscode.WebviewPanel | undefined;

  constructor(private context: vscode.ExtensionContext) {}

  public openWhiteboard(): void {
    if (this.panel) {
      this.panel.reveal();
      return;
    }

    this.panel = vscode.window.createWebviewPanel(
      'aiCodeVisualizerWhiteboard',
      'Visual Intent Contract Whiteboard',
      vscode.ViewColumn.One,
      {
        enableScripts: true,
        retainContextWhenHidden: true,
        localResourceRoots: [
          vscode.Uri.joinPath(this.context.extensionUri, 'media'),
        ],
      }
    );

    this.panel.webview.html = this.getWebviewContent();

    this.panel.onDidDispose(() => {
      this.panel = undefined;
    });
  }

  public createBlueprint(): void {
    vscode.window.showInformationMessage(
      'Creating new blueprint... (Implementation pending)'
    );
  }

  private getWebviewContent(): string {
    return `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Visual Intent Contract Whiteboard</title>
        <style>
          body {
            font-family: var(--vscode-font-family);
            color: var(--vscode-foreground);
            background-color: var(--vscode-editor-background);
            margin: 0;
            padding: 20px;
          }
          .header {
            text-align: center;
            margin-bottom: 30px;
          }
          .role-definition {
            background: var(--vscode-editor-inactiveSelectionBackground);
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
          }
          .role {
            margin: 10px 0;
            padding: 10px;
            border-left: 4px solid var(--vscode-textLink-foreground);
          }
          .whiteboard-placeholder {
            border: 2px dashed var(--vscode-textLink-foreground);
            height: 400px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--vscode-descriptionForeground);
            font-size: 18px;
          }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>🏛️ 可视化意图契约白板系统</h1>
          <p>Visual Intent Contract Whiteboard System</p>
        </div>
        
        <div class="role-definition">
          <h2>权力分离与角色界定</h2>
          <div class="role">
            <strong>👨‍⚖️ 用户 = 立法者</strong><br>
            制定规则，建立项目"宪法"
          </div>
          <div class="role">
            <strong>📋 白板 = 宪法</strong><br>
            不可违背的法律框架
          </div>
          <div class="role">
            <strong>🤖 AI = 执行者</strong><br>
            严格受约束的工具，禁止创造性和"幻觉"
          </div>
        </div>

        <div class="whiteboard-placeholder">
          白板界面即将实现...
          <br>
          Whiteboard interface coming soon...
        </div>

        <script>
          console.log('Visual Intent Contract Whiteboard initialized');
        </script>
      </body>
      </html>
    `;
  }

  dispose(): void {
    if (this.panel) {
      this.panel.dispose();
    }
  }
}
