import { describe, it, expect, beforeEach } from 'vitest';
import { DataDictionaryEngine } from './DataDictionaryEngine';
import { DataFieldType } from '../models/enums';

describe('DataDictionaryEngine', () => {
  let engine: DataDictionaryEngine;

  beforeEach(() => {
    engine = new DataDictionaryEngine({
      enableStrictValidation: true,
      enableRelationshipValidation: true,
      maxEntityCount: 10,
      maxFieldsPerEntity: 20,
      enableAutoNaming: true,
    });
  });

  describe('Entity Management', () => {
    it('should create a new entity successfully', () => {
      const result = engine.createEntity({
        name: 'User',
        description: 'User entity for authentication',
        fields: [
          {
            name: 'id',
            type: DataFieldType.NUMBER,
            required: true,
            description: 'Primary key',
          },
          {
            name: 'email',
            type: DataFieldType.EMAIL,
            required: true,
            description: 'User email address',
          },
          {
            name: 'name',
            type: DataFieldType.STRING,
            required: true,
            description: 'User full name',
          },
        ],
      });

      expect(result.success).toBe(true);
      expect(result.entity).toBeDefined();
      expect(result.entity!.name).toBe('User');
      expect(result.entity!.fields).toHaveLength(3);
      expect(result.validation.isValid).toBe(true);
    });

    it('should prevent duplicate entity names', () => {
      // 创建第一个实体
      const result1 = engine.createEntity({
        name: 'User',
        description: 'First user entity',
      });

      expect(result1.success).toBe(true);

      // 尝试创建同名实体
      const result2 = engine.createEntity({
        name: 'User',
        description: 'Second user entity',
      });

      expect(result2.success).toBe(false);
      expect(result2.validation.errors.some(e => e.code === 'DUPLICATE_NAME')).toBe(true);
    });

    it('should update entity successfully', () => {
      // 创建实体
      const createResult = engine.createEntity({
        name: 'User',
        description: 'Original description',
      });

      expect(createResult.success).toBe(true);

      // 更新实体
      const updateResult = engine.updateEntity(createResult.entity!.id, {
        name: 'UpdatedUser',
        description: 'Updated description',
      });

      expect(updateResult.success).toBe(true);
      expect(updateResult.entity!.name).toBe('UpdatedUser');
      expect(updateResult.entity!.description).toBe('Updated description');
    });

    it('should delete entity successfully', () => {
      // 创建实体
      const createResult = engine.createEntity({
        name: 'User',
        description: 'User entity',
      });

      expect(createResult.success).toBe(true);

      // 删除实体
      const deleteResult = engine.deleteEntity(createResult.entity!.id);

      expect(deleteResult.success).toBe(true);
      expect(engine.getEntity(createResult.entity!.id)).toBeUndefined();
    });

    it('should prevent deletion of entity with dependencies', () => {
      // 创建用户实体
      const userResult = engine.createEntity({
        name: 'User',
        description: 'User entity',
      });

      expect(userResult.success).toBe(true);

      // 创建订单实体，引用用户实体
      const orderResult = engine.createEntity({
        name: 'Order',
        description: 'Order entity',
        relationships: [
          {
            id: 'user_relationship',
            type: 'MANY_TO_ONE',
            targetEntityId: userResult.entity!.id,
            foreignKey: 'userId',
          },
        ],
      });

      expect(orderResult.success).toBe(true);

      // 尝试删除被引用的用户实体
      const deleteResult = engine.deleteEntity(userResult.entity!.id);

      expect(deleteResult.success).toBe(false);
      expect(deleteResult.validation.errors.some(e => e.code === 'HAS_DEPENDENCIES')).toBe(true);
    });
  });

  describe('Field Management', () => {
    let userEntity: any;

    beforeEach(() => {
      const result = engine.createEntity({
        name: 'User',
        description: 'User entity',
      });
      userEntity = result.entity;
    });

    it('should add field to entity successfully', () => {
      const result = engine.addFieldToEntity(userEntity, {
        name: 'email',
        type: DataFieldType.EMAIL,
        required: true,
        description: 'User email address',
        constraints: [
          {
            type: 'PATTERN',
            value: '^[^@]+@[^@]+\\.[^@]+$',
            message: 'Invalid email format',
          },
        ],
      });

      expect(result.success).toBe(true);
      expect(userEntity.fields).toHaveLength(1);
      expect(userEntity.fields[0].name).toBe('email');
      expect(userEntity.fields[0].type).toBe(DataFieldType.EMAIL);
      expect(userEntity.fields[0].constraints).toHaveLength(1);
    });

    it('should prevent duplicate field names', () => {
      // 添加第一个字段
      const result1 = engine.addFieldToEntity(userEntity, {
        name: 'email',
        type: DataFieldType.EMAIL,
        required: true,
      });

      expect(result1.success).toBe(true);

      // 尝试添加同名字段
      const result2 = engine.addFieldToEntity(userEntity, {
        name: 'email',
        type: DataFieldType.STRING,
        required: false,
      });

      expect(result2.success).toBe(false);
      expect(result2.validation.errors.some(e => e.code === 'DUPLICATE_FIELD_NAME')).toBe(true);
    });

    it('should validate field constraints', () => {
      const result = engine.addFieldToEntity(userEntity, {
        name: 'age',
        type: DataFieldType.NUMBER,
        required: true,
        constraints: [
          {
            type: 'RANGE',
            value: [0, 150],
            message: 'Age must be between 0 and 150',
          },
        ],
      });

      expect(result.success).toBe(true);
      expect(result.validation.isValid).toBe(true);
    });

    it('should validate invalid constraints', () => {
      const result = engine.addFieldToEntity(userEntity, {
        name: 'pattern_field',
        type: DataFieldType.STRING,
        required: true,
        constraints: [
          {
            type: 'PATTERN',
            value: '[invalid regex',
            message: 'Invalid pattern',
          },
        ],
      });

      expect(result.success).toBe(false);
      expect(result.validation.errors.some(e => e.code === 'INVALID_REGEX_PATTERN')).toBe(true);
    });
  });

  describe('Relationship Management', () => {
    let userEntity: any;
    let orderEntity: any;

    beforeEach(() => {
      const userResult = engine.createEntity({
        name: 'User',
        description: 'User entity',
      });
      userEntity = userResult.entity;

      const orderResult = engine.createEntity({
        name: 'Order',
        description: 'Order entity',
      });
      orderEntity = orderResult.entity;
    });

    it('should add relationship successfully', () => {
      const result = engine.addRelationshipToEntity(orderEntity, {
        id: 'user_relationship',
        type: 'MANY_TO_ONE',
        targetEntityId: userEntity.id,
        foreignKey: 'userId',
      });

      expect(result.success).toBe(true);
      expect(orderEntity.relationships).toHaveLength(1);
      expect(orderEntity.relationships[0].targetEntityId).toBe(userEntity.id);
    });

    it('should validate relationship target exists', () => {
      const result = engine.addRelationshipToEntity(orderEntity, {
        id: 'invalid_relationship',
        type: 'MANY_TO_ONE',
        targetEntityId: 'non_existent_id',
        foreignKey: 'invalidId',
      });

      expect(result.success).toBe(false);
      expect(result.validation.errors.some(e => e.code === 'TARGET_NOT_FOUND')).toBe(true);
    });

    it('should detect circular references', () => {
      // 添加 User -> Order 关系
      engine.addRelationshipToEntity(userEntity, {
        id: 'user_to_order',
        type: 'ONE_TO_MANY',
        targetEntityId: orderEntity.id,
      });

      // 添加 Order -> User 关系（形成循环）
      const result = engine.addRelationshipToEntity(orderEntity, {
        id: 'order_to_user',
        type: 'MANY_TO_ONE',
        targetEntityId: userEntity.id,
        foreignKey: 'userId',
      });

      expect(result.success).toBe(true); // 允许循环引用，但会有警告
      expect(result.validation.warnings.some(w => w.code === 'CIRCULAR_REFERENCE')).toBe(true);
    });
  });

  describe('Data Integrity', () => {
    it('should check data integrity successfully', () => {
      // 创建正常的实体结构
      const userResult = engine.createEntity({
        name: 'User',
        description: 'User entity',
        fields: [
          {
            name: 'id',
            type: DataFieldType.NUMBER,
            required: true,
          },
        ],
      });

      const orderResult = engine.createEntity({
        name: 'Order',
        description: 'Order entity',
        fields: [
          {
            name: 'userId',
            type: DataFieldType.REFERENCE,
            required: true,
          },
        ],
        relationships: [
          {
            id: 'user_relationship',
            type: 'MANY_TO_ONE',
            targetEntityId: userResult.entity!.id,
            foreignKey: 'userId',
          },
        ],
      });

      const integrityReport = engine.checkDataIntegrity();

      expect(integrityReport.isValid).toBe(true);
      expect(integrityReport.orphanedReferences).toHaveLength(0);
      expect(integrityReport.missingEntities).toHaveLength(0);
      expect(integrityReport.duplicateNames).toHaveLength(0);
    });

    it('should detect orphaned references', () => {
      // 创建带有引用字段但没有关系的实体
      engine.createEntity({
        name: 'Order',
        description: 'Order entity',
        fields: [
          {
            name: 'userId',
            type: DataFieldType.REFERENCE,
            required: true,
          },
        ],
      });

      const integrityReport = engine.checkDataIntegrity();

      expect(integrityReport.orphanedReferences).toContain('Order.userId');
      expect(integrityReport.recommendations).toContain('Add relationships for orphaned reference fields');
    });
  });

  describe('Search and Query', () => {
    beforeEach(() => {
      engine.createEntity({
        name: 'User',
        description: 'User entity for authentication',
      });

      engine.createEntity({
        name: 'Order',
        description: 'Order entity for e-commerce',
      });

      engine.createEntity({
        name: 'Product',
        description: 'Product catalog entity',
      });
    });

    it('should get entity by name', () => {
      const entity = engine.getEntityByName('User');

      expect(entity).toBeDefined();
      expect(entity!.name).toBe('User');
    });

    it('should search entities by query', () => {
      const results = engine.searchEntities('order');

      expect(results).toHaveLength(1);
      expect(results[0].name).toBe('Order');
    });

    it('should search entities by description', () => {
      const results = engine.searchEntities('authentication');

      expect(results).toHaveLength(1);
      expect(results[0].name).toBe('User');
    });

    it('should return all entities', () => {
      const entities = engine.getAllEntities();

      expect(entities).toHaveLength(3);
      expect(entities.map(e => e.name)).toContain('User');
      expect(entities.map(e => e.name)).toContain('Order');
      expect(entities.map(e => e.name)).toContain('Product');
    });
  });

  describe('Import/Export', () => {
    beforeEach(() => {
      engine.createEntity({
        name: 'User',
        description: 'User entity',
        fields: [
          {
            name: 'id',
            type: DataFieldType.NUMBER,
            required: true,
          },
          {
            name: 'email',
            type: DataFieldType.EMAIL,
            required: true,
          },
        ],
      });
    });

    it('should export data dictionary', () => {
      const exported = engine.exportDataDictionary();

      expect(exported.entities).toHaveLength(1);
      expect(exported.entities[0].name).toBe('User');
      expect(exported.metadata).toBeDefined();
      expect(exported.metadata.entityCount).toBe(1);
    });

    it('should import data dictionary', () => {
      const exported = engine.exportDataDictionary();

      // 清空引擎
      engine.clear();
      expect(engine.getAllEntities()).toHaveLength(0);

      // 导入数据
      const result = engine.importDataDictionary(exported);

      expect(result.success).toBe(true);
      expect(engine.getAllEntities()).toHaveLength(1);
      expect(engine.getEntityByName('User')).toBeDefined();
    });
  });

  describe('Statistics', () => {
    it('should provide accurate statistics', () => {
      // 创建测试数据
      const userResult = engine.createEntity({
        name: 'User',
        description: 'User entity',
        fields: [
          { name: 'id', type: DataFieldType.NUMBER, required: true },
          { name: 'email', type: DataFieldType.EMAIL, required: true },
        ],
      });

      const orderResult = engine.createEntity({
        name: 'Order',
        description: 'Order entity',
        fields: [
          { name: 'id', type: DataFieldType.NUMBER, required: true },
          { name: 'userId', type: DataFieldType.REFERENCE, required: true },
        ],
        relationships: [
          {
            id: 'user_relationship',
            type: 'MANY_TO_ONE',
            targetEntityId: userResult.entity!.id,
            foreignKey: 'userId',
          },
        ],
      });

      const stats = engine.getStatistics();

      expect(stats.entityCount).toBe(2);
      expect(stats.totalFields).toBe(4);
      expect(stats.totalRelationships).toBe(1);
      expect(stats.averageFieldsPerEntity).toBe(2);
      expect(stats.averageRelationshipsPerEntity).toBe(0.5);
    });
  });

  describe('Configuration', () => {
    it('should respect entity count limit', () => {
      const limitedEngine = new DataDictionaryEngine({
        maxEntityCount: 2,
      });

      // 创建两个实体（达到限制）
      limitedEngine.createEntity({ name: 'Entity1' });
      limitedEngine.createEntity({ name: 'Entity2' });

      // 尝试创建第三个实体
      const result = limitedEngine.createEntity({ name: 'Entity3' });

      expect(result.success).toBe(false);
      expect(result.validation.errors.some(e => e.code === 'MAX_COUNT_EXCEEDED')).toBe(true);
    });

    it('should respect field count limit per entity', () => {
      const limitedEngine = new DataDictionaryEngine({
        maxFieldsPerEntity: 2,
      });

      const entityResult = limitedEngine.createEntity({ name: 'TestEntity' });
      const entity = entityResult.entity!;

      // 添加两个字段（达到限制）
      limitedEngine.addFieldToEntity(entity, {
        name: 'field1',
        type: DataFieldType.STRING,
      });
      limitedEngine.addFieldToEntity(entity, {
        name: 'field2',
        type: DataFieldType.STRING,
      });

      // 尝试添加第三个字段
      const result = limitedEngine.addFieldToEntity(entity, {
        name: 'field3',
        type: DataFieldType.STRING,
      });

      expect(result.success).toBe(false);
      expect(result.validation.errors.some(e => e.code === 'MAX_FIELDS_EXCEEDED')).toBe(true);
    });

    it('should handle disabled relationship validation', () => {
      const noValidationEngine = new DataDictionaryEngine({
        enableRelationshipValidation: false,
      });

      const entityResult = noValidationEngine.createEntity({ name: 'TestEntity' });
      const entity = entityResult.entity!;

      // 添加指向不存在实体的关系
      const result = noValidationEngine.addRelationshipToEntity(entity, {
        id: 'invalid_relationship',
        type: 'MANY_TO_ONE',
        targetEntityId: 'non_existent_id',
      });

      // 应该成功，因为关系验证被禁用
      expect(result.success).toBe(true);
    });
  });
});