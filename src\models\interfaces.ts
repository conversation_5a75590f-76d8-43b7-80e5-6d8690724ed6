import {
  ProjectType,
  ModuleType,
  ModuleStatus,
  ComponentType,
  DataFieldType,
  BusinessRuleType,
  LogicOperator,
  ValidationSeverity,
  NodeStatus,
  Language,
  RelationshipType,
} from './enums';

/**
 * 基础实体接口
 */
export interface BaseEntity {
  id: string;
  name: string;
  description?: string;
  createdAt: Date;
  updatedAt: Date;
  version: string;
}

/**
 * 位置信息接口
 */
export interface Position {
  x: number;
  y: number;
}

/**
 * 尺寸信息接口
 */
export interface Size {
  width: number;
  height: number;
}

/**
 * 关系接口
 */
export interface Relationship {
  id: string;
  type: RelationshipType;
  sourceId: string;
  targetId: string;
  properties?: Record<string, any>;
}

/**
 * 元数据接口
 */
export interface Metadata {
  author: string;
  tags: string[];
  customProperties: Record<string, any>;
}

/**
 * 蓝图元数据接口
 */
export interface BlueprintMetadata extends Metadata {
  projectType: ProjectType;
  targetLanguages: Language[];
  estimatedComplexity: 'LOW' | 'MEDIUM' | 'HIGH';
  dependencies: string[];
}

/**
 * 宪法元数据接口
 */
export interface ConstitutionMetadata extends Metadata {
  constitutionVersion: string;
  lastAmendment: Date;
  amendmentHistory: ConstitutionChange[];
}

/**
 * 宪法变更记录接口
 */
export interface ConstitutionChange {
  id: string;
  timestamp: Date;
  changeType: 'CREATE' | 'UPDATE' | 'DELETE' | 'AMENDMENT';
  description: string;
  author: string;
  affectedElements: string[];
}

/**
 * 数据字段接口
 */
export interface DataField {
  id: string;
  name: string;
  type: DataFieldType;
  required: boolean;
  defaultValue?: any;
  constraints: DataConstraint[];
  description?: string;
}

/**
 * 数据约束接口
 */
export interface DataConstraint {
  type: 'LENGTH' | 'RANGE' | 'PATTERN' | 'UNIQUE' | 'FOREIGN_KEY' | 'CUSTOM';
  value: any;
  message: string;
}

/**
 * 实体关系接口
 */
export interface EntityRelationship {
  id: string;
  type: 'ONE_TO_ONE' | 'ONE_TO_MANY' | 'MANY_TO_ONE' | 'MANY_TO_MANY';
  targetEntityId: string;
  foreignKey?: string;
  cascadeDelete?: boolean;
}

/**
 * 验证规则接口
 */
export interface ValidationRule {
  id: string;
  name: string;
  condition: LogicCondition;
  message: string;
  severity: ValidationSeverity;
}

/**
 * 逻辑条件接口
 */
export interface LogicCondition {
  operator: LogicOperator;
  operands: (LogicCondition | LogicValue)[];
}

/**
 * 逻辑值接口
 */
export interface LogicValue {
  type: 'FIELD' | 'CONSTANT' | 'FUNCTION';
  value: any;
}

/**
 * 逻辑动作接口
 */
export interface LogicAction {
  type: 'SET_VALUE' | 'SHOW_MESSAGE' | 'TRIGGER_EVENT' | 'CALL_FUNCTION';
  target: string;
  value?: any;
  parameters?: Record<string, any>;
}

/**
 * 组件属性接口
 */
export interface ComponentProperty {
  name: string;
  type: string;
  value: any;
  required: boolean;
  description?: string;
}

/**
 * 组件事件接口
 */
export interface ComponentEvent {
  name: string;
  type: string;
  handler?: string;
  parameters?: Record<string, any>;
}

/**
 * 数据需求接口
 */
export interface DataRequirement {
  entityId: string;
  operations: ('CREATE' | 'READ' | 'UPDATE' | 'DELETE')[];
  constraints?: string[];
}

/**
 * 可追溯性信息接口
 */
export interface TraceabilityInfo {
  originalBlueprintId: string;
  originalModuleId?: string;
  originalRuleId?: string;
  creationTimestamp: Date;
  lastModified: Date;
}

/**
 * 规则冲突接口
 */
export interface RuleConflict {
  id: string;
  conflictingRuleIds: string[];
  conflictType: 'LOGICAL_CONTRADICTION' | 'PRIORITY_CONFLICT' | 'SCOPE_OVERLAP';
  description: string;
  severity: ValidationSeverity;
  suggestedResolutions: Resolution[];
}

/**
 * 冲突解决方案接口
 */
export interface Resolution {
  id: string;
  description: string;
  action: 'MODIFY_RULE' | 'DELETE_RULE' | 'CHANGE_PRIORITY' | 'ADD_CONDITION';
  parameters: Record<string, any>;
}

/**
 * 冲突报告接口
 */
export interface ConflictReport {
  conflicts: RuleConflict[];
  hasBlockingConflicts: boolean;
  resolutionRequired: boolean;
}

/**
 * 节点样式接口
 */
export interface NodeStyle {
  backgroundColor: string;
  borderColor: string;
  textColor: string;
  borderWidth: number;
  borderRadius: number;
  opacity: number;
}

/**
 * 视口接口
 */
export interface Viewport {
  x: number;
  y: number;
  zoom: number;
}

/**
 * 交互模式枚举
 */
export enum InteractionMode {
  VIEW = 'VIEW',
  EDIT = 'EDIT',
  SELECT = 'SELECT',
  DRAG = 'DRAG',
  CONNECT = 'CONNECT',
}

/**
 * 布局接口
 */
export interface Layout {
  nodes: Map<string, Position>;
  algorithm: 'FORCE_DIRECTED' | 'HIERARCHICAL' | 'CIRCULAR' | 'MANUAL';
  parameters: Record<string, any>;
}
