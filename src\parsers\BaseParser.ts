import * as vscode from 'vscode';

/**
 * AST节点基础接口
 */
export interface ASTNode {
  type: string;
  name?: string;
  range: vscode.Range;
  children: ASTNode[];
  metadata?: { [key: string]: any };
}

/**
 * 解析结果接口
 */
export interface ParseResult {
  success: boolean;
  ast?: ASTNode;
  symbols: SymbolInfo[];
  dependencies: DependencyInfo[];
  exports: ExportInfo[];
  errors: ParseError[];
  warnings: ParseWarning[];
  metrics: CodeMetrics;
}

/**
 * 符号信息接口
 */
export interface SymbolInfo {
  name: string;
  type: 'function' | 'class' | 'interface' | 'variable' | 'constant' | 'method' | 'property';
  range: vscode.Range;
  visibility: 'public' | 'private' | 'protected' | 'internal';
  isAsync?: boolean;
  isStatic?: boolean;
  parameters?: ParameterInfo[];
  returnType?: string;
  documentation?: string;
}

/**
 * 参数信息接口
 */
export interface ParameterInfo {
  name: string;
  type?: string;
  optional?: boolean;
  defaultValue?: string;
}

/**
 * 依赖信息接口
 */
export interface DependencyInfo {
  module: string;
  type: 'import' | 'require' | 'dynamic';
  range: vscode.Range;
  imported?: string[];
  isDefault?: boolean;
  alias?: string;
}

/**
 * 导出信息接口
 */
export interface ExportInfo {
  name: string;
  type: 'default' | 'named';
  range: vscode.Range;
  isReExport?: boolean;
  originalModule?: string;
}

/**
 * 解析错误接口
 */
export interface ParseError {
  message: string;
  range: vscode.Range;
  severity: 'error' | 'warning';
  code?: string;
}

/**
 * 解析警告接口
 */
export interface ParseWarning {
  message: string;
  range: vscode.Range;
  code?: string;
}

/**
 * 代码度量接口
 */
export interface CodeMetrics {
  linesOfCode: number;
  cyclomaticComplexity: number;
  cognitiveComplexity: number;
  maintainabilityIndex: number;
  technicalDebt: number;
  duplicatedLines: number;
  testCoverage?: number;
}

/**
 * 解析器配置接口
 */
export interface ParserConfig {
  includeComments: boolean;
  includeDocumentation: boolean;
  calculateMetrics: boolean;
  detectPatterns: boolean;
  maxDepth: number;
  timeout: number;
}

/**
 * 基础解析器抽象类
 */
export abstract class BaseParser {
  protected config: ParserConfig;

  constructor(config: Partial<ParserConfig> = {}) {
    this.config = {
      includeComments: true,
      includeDocumentation: true,
      calculateMetrics: true,
      detectPatterns: true,
      maxDepth: 50,
      timeout: 10000,
      ...config
    };
  }

  /**
   * 解析文档
   */
  public abstract parse(document: vscode.TextDocument): Promise<ParseResult>;

  /**
   * 检查是否支持该语言
   */
  public abstract supports(languageId: string): boolean;

  /**
   * 获取支持的语言列表
   */
  public abstract getSupportedLanguages(): string[];

  /**
   * 创建范围对象
   */
  protected createRange(startLine: number, startChar: number, endLine: number, endChar: number): vscode.Range {
    return new vscode.Range(
      new vscode.Position(startLine, startChar),
      new vscode.Position(endLine, endChar)
    );
  }

  /**
   * 计算圈复杂度
   */
  protected calculateCyclomaticComplexity(content: string): number {
    const complexityKeywords = [
      'if', 'else if', 'while', 'for', 'do', 'switch', 'case',
      'catch', 'finally', '&&', '||', '?', ':'
    ];

    let complexity = 1; // 基础复杂度

    complexityKeywords.forEach(keyword => {
      const regex = new RegExp(`\\b${keyword}\\b|\\${keyword}`, 'g');
      const matches = content.match(regex);
      if (matches) {
        complexity += matches.length;
      }
    });

    return complexity;
  }

  /**
   * 计算认知复杂度
   */
  protected calculateCognitiveComplexity(content: string): number {
    // 简化的认知复杂度计算
    let complexity = 0;
    let nestingLevel = 0;

    const lines = content.split('\n');
    
    for (const line of lines) {
      const trimmed = line.trim();
      
      // 增加嵌套级别
      if (trimmed.includes('{')) {
        nestingLevel++;
      }
      
      // 减少嵌套级别
      if (trimmed.includes('}')) {
        nestingLevel = Math.max(0, nestingLevel - 1);
      }
      
      // 计算复杂度增量
      if (trimmed.match(/\b(if|while|for|do|switch|catch)\b/)) {
        complexity += 1 + nestingLevel;
      }
      
      if (trimmed.match(/\b(else if|else)\b/)) {
        complexity += 1;
      }
      
      if (trimmed.match(/&&|\|\|/)) {
        complexity += 1;
      }
    }

    return complexity;
  }

  /**
   * 计算可维护性指数
   */
  protected calculateMaintainabilityIndex(metrics: Partial<CodeMetrics>): number {
    const loc = metrics.linesOfCode || 1;
    const cc = metrics.cyclomaticComplexity || 1;
    const cognitiveComplexity = metrics.cognitiveComplexity || 1;

    // 简化的可维护性指数计算
    const maintainabilityIndex = Math.max(0, 
      171 - 5.2 * Math.log(loc) - 0.23 * cc - 16.2 * Math.log(cognitiveComplexity)
    );

    return Math.round(maintainabilityIndex);
  }

  /**
   * 估算技术债务
   */
  protected calculateTechnicalDebt(metrics: Partial<CodeMetrics>): number {
    const cc = metrics.cyclomaticComplexity || 1;
    const cognitiveComplexity = metrics.cognitiveComplexity || 1;
    const duplicatedLines = metrics.duplicatedLines || 0;

    // 技术债务估算（以分钟为单位）
    let debt = 0;

    // 基于复杂度的债务
    if (cc > 10) {
      debt += (cc - 10) * 5;
    }

    if (cognitiveComplexity > 15) {
      debt += (cognitiveComplexity - 15) * 3;
    }

    // 基于重复代码的债务
    debt += duplicatedLines * 0.5;

    return Math.round(debt);
  }

  /**
   * 检测重复代码行
   */
  protected detectDuplicatedLines(content: string): number {
    const lines = content.split('\n')
      .map(line => line.trim())
      .filter(line => line.length > 0 && !line.startsWith('//') && !line.startsWith('/*'));

    const lineCount = new Map<string, number>();
    
    lines.forEach(line => {
      lineCount.set(line, (lineCount.get(line) || 0) + 1);
    });

    let duplicatedLines = 0;
    lineCount.forEach((count, line) => {
      if (count > 1 && line.length > 10) { // 只计算长度超过10的重复行
        duplicatedLines += count - 1;
      }
    });

    return duplicatedLines;
  }

  /**
   * 创建基础度量对象
   */
  protected createBaseMetrics(content: string): CodeMetrics {
    const linesOfCode = content.split('\n').filter(line => line.trim().length > 0).length;
    const cyclomaticComplexity = this.calculateCyclomaticComplexity(content);
    const cognitiveComplexity = this.calculateCognitiveComplexity(content);
    const duplicatedLines = this.detectDuplicatedLines(content);

    const metrics: CodeMetrics = {
      linesOfCode,
      cyclomaticComplexity,
      cognitiveComplexity,
      duplicatedLines,
      maintainabilityIndex: 0,
      technicalDebt: 0
    };

    metrics.maintainabilityIndex = this.calculateMaintainabilityIndex(metrics);
    metrics.technicalDebt = this.calculateTechnicalDebt(metrics);

    return metrics;
  }

  /**
   * 创建解析错误
   */
  protected createError(message: string, line: number, character: number, code?: string): ParseError {
    return {
      message,
      range: this.createRange(line, character, line, character + 1),
      severity: 'error',
      code
    };
  }

  /**
   * 创建解析警告
   */
  protected createWarning(message: string, line: number, character: number, code?: string): ParseWarning {
    return {
      message,
      range: this.createRange(line, character, line, character + 1),
      code
    };
  }

  /**
   * 获取配置
   */
  public getConfig(): ParserConfig {
    return { ...this.config };
  }

  /**
   * 更新配置
   */
  public updateConfig(newConfig: Partial<ParserConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }
}